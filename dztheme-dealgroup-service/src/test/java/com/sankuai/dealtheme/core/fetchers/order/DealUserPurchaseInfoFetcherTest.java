package com.sankuai.dealtheme.core.fetchers.order;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.athena.graphql.Execution;
import com.sankuai.athena.graphql.ExecutionContext;
import com.sankuai.athena.graphql.FetchingContext;
import com.sankuai.dealtheme.DealThemeConfig;
import com.sankuai.dealtheme.core.fetchers.model.DealModel;
import com.sankuai.dealtheme.core.fetchers.model.DealUserPurchaseInfoModel;
import com.sankuai.dealtheme.core.nr.atom.FacadeService;
import com.sankuai.general.order.querycenter.api.response.OrderSearchResponse;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealUserPurchaseInfoFetcherTest {
    @InjectMocks
    private DealUserPurchaseInfoFetcher fetcher;
    @Mock
    private FacadeService facadeService;

    @Mock
    private Execution execution;

    @Mock
    private ExecutionContext executionContext;

    @Mock
    private FetchingContext fetchingContext;
    private Map<Integer,FetchingContext> keyFetchContexts;
    private MockedStatic<AthenaBeanFactory> anthenaBeanFactoryMockedStatic;


    @Before
    public void setUp() {
        keyFetchContexts = new HashMap<>();
        keyFetchContexts.put(123, fetchingContext);
        when(fetchingContext.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.getExecution()).thenReturn(execution);
        when(fetchingContext.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.getExecution()).thenReturn(execution);
        anthenaBeanFactoryMockedStatic = Mockito.mockStatic(AthenaBeanFactory.class);
        anthenaBeanFactoryMockedStatic.when(() -> AthenaBeanFactory.getBean(FacadeService.class)).thenReturn(facadeService);
    }

    @After
    public void after() {
        anthenaBeanFactoryMockedStatic.close();
    }

    @Test
    public void testFetcherNotNull() {
        // 验证 fetcher 不为空
        assertNotNull(fetcher);
    }

    @Test
    public void testFacadeServiceNotNull() {
        // 验证 facadeService 不为空
        assertNotNull(facadeService);
    }
}