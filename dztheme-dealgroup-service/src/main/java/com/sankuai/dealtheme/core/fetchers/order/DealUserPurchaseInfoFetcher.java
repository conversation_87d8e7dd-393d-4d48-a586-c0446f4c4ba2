package com.sankuai.dealtheme.core.fetchers.order;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.athena.graphql.Execution;
import com.sankuai.athena.graphql.ExecutionContext;
import com.sankuai.athena.graphql.FetchingContext;
import com.sankuai.athena.theme.framework.annotations.Fetcher;
import com.sankuai.athena.theme.framework.transformer.fetcher.BatchFetcher;
import com.sankuai.dealtheme.DealThemeConfig;
import com.sankuai.dealtheme.core.fetchers.model.DealModel;
import com.sankuai.dealtheme.core.fetchers.model.DealUserPurchaseInfoModel;
import com.sankuai.dealtheme.core.fetchers.utils.ContextUtils;
import com.sankuai.dealtheme.core.fetchers.utils.NodeUtils;
import com.sankuai.dealtheme.core.nr.atom.FacadeService;
import com.sankuai.general.order.querycenter.api.enums.BizLineEnum;
import com.sankuai.general.order.querycenter.api.enums.PlatformIndexKeyEnum;
import com.sankuai.general.order.querycenter.api.request.OrderSearchDetailRequest;
import com.sankuai.general.order.querycenter.api.request.OrderSearchRequest;
import com.sankuai.general.order.querycenter.api.request.RangeSearchRequest;
import com.sankuai.general.order.querycenter.api.request.SessionContext;
import com.sankuai.general.order.querycenter.api.request.TermSearchRequest;
import com.sankuai.general.order.querycenter.api.response.OrderSearchResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 用户商品购买历史查询Fetcher
 * 查询用户在指定门店下180天内的商品购买次数统计
 *
 * <AUTHOR>
 * @date 2025/1/16
 */
@Fetcher(name = "DealUserPurchaseInfoFetcher",
        description = "用户商品购买历史查询，支持美团和点评双平台",
        type = "Deal",
        field = "dealUserPurchaseInfoModel",
        params = {DealThemeConfig.PLATFORM, DealThemeConfig.USER_ID,
                  DealThemeConfig.DP_SHOP_ID_FOR_LONG, DealThemeConfig.MT_SHOP_ID_FOR_LONG},
        needFields = {})
public class DealUserPurchaseInfoFetcher extends BatchFetcher<Integer, DealUserPurchaseInfoModel> {

    private static final Logger LOGGER = LoggerFactory.getLogger(DealUserPurchaseInfoFetcher.class);

    /** 查询时间范围：180天 */
    private static final int QUERY_DAYS = 180;

    /** 单次查询最大订单数量 */
    private static final int MAX_PAGE_SIZE = 1000;

    /** 毫秒转换常量 */
    private static final long MILLIS_PER_DAY = 24 * 60 * 60 * 1000L;

    @Resource
    private FacadeService facadeService;

    @Override
    public int batchSize() {
        return 200;
    }

    @Override
    public Integer batchKey(FetchingContext fetchingContext) {
        DealModel dealModel = fetchingContext.getSource();
        return dealModel.getDealId();
    }

    @Override
    public CompletableFuture<Map<Integer, DealUserPurchaseInfoModel>> batchGet(Map<Integer, FetchingContext> keyFetchContexts) {
        if (MapUtils.isEmpty(keyFetchContexts)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }

        try {
            Execution execution = ContextUtils.getExecution(keyFetchContexts);
            ExecutionContext executionContext = ContextUtils.getExecutionContext(keyFetchContexts);

            // 获取参数
            int platform = execution.getParameter(DealThemeConfig.PLATFORM);
            Long userId = execution.getParameter(DealThemeConfig.USER_ID);
            Long dpShopId = execution.getParameter(DealThemeConfig.DP_SHOP_ID_FOR_LONG);
            Long mtShopId = execution.getParameter(DealThemeConfig.MT_SHOP_ID_FOR_LONG);

            if (userId == null || userId <= 0) {
                LOGGER.warn("DealUserPurchaseInfoFetcher: 用户ID为空或无效, userId={}", userId);
                return CompletableFuture.completedFuture(Maps.newHashMap());
            }

            if ((dpShopId == null || dpShopId <= 0) && (mtShopId == null || mtShopId <= 0)) {
                LOGGER.warn("DealUserPurchaseInfoFetcher: 门店ID为空或无效, dpShopId={}, mtShopId={}", dpShopId, mtShopId);
                return CompletableFuture.completedFuture(Maps.newHashMap());
            }

            List<Integer> dealIds = Lists.newArrayList(keyFetchContexts.keySet());

            // 获取用户ID映射和订单查询
            return getUserIdMappingAndSearchOrders(platform, userId, dpShopId, mtShopId, dealIds, executionContext);

        } catch (Exception e) {
            LOGGER.error("DealUserPurchaseInfoFetcher: 查询用户购买信息异常", e);
            Cat.logError("DealUserPurchaseInfoFetcher_error", e);
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
    }

    /**
     * 获取用户ID映射并查询订单
     */
    private CompletableFuture<Map<Integer, DealUserPurchaseInfoModel>> getUserIdMappingAndSearchOrders(
            int platform, Long userId, Long dpShopId, Long mtShopId, List<Integer> dealIds, ExecutionContext executionContext) {

        if (platform == 1) { // 点评平台
            return searchDpOrders(userId, dpShopId, dealIds, executionContext)
                    .thenCompose(dpResult -> {
                        // 查询美团订单需要先获取美团用户ID
                        return getMtUserIdFromDpUserId(userId)
                                .thenCompose(mtUserId -> {
                                    if (mtUserId != null && mtUserId > 0) {
                                        return searchMtOrders(mtUserId, mtShopId, dealIds, executionContext);
                                    } else {
                                        return CompletableFuture.completedFuture(Maps.newHashMap());
                                    }
                                })
                                .thenApply(mtResult -> mergeResults(dpResult, mtResult, dealIds));
                    });
        } else { // 美团平台
            return searchMtOrders(userId, mtShopId, dealIds, executionContext)
                    .thenCompose(mtResult -> {
                        // 查询点评订单需要先获取点评用户ID
                        return getDpUserIdFromMtUserId(userId)
                                .thenCompose(dpUserId -> {
                                    if (dpUserId != null && dpUserId > 0) {
                                        return searchDpOrders(dpUserId, dpShopId, dealIds, executionContext);
                                    } else {
                                        return CompletableFuture.completedFuture(Maps.newHashMap());
                                    }
                                })
                                .thenApply(dpResult -> mergeResults(dpResult, mtResult, dealIds));
                    });
        }
    }

    /**
     * 获取美团用户ID（从点评用户ID）
     */
    private CompletableFuture<Long> getMtUserIdFromDpUserId(Long dpUserId) {
        FacadeService service = AthenaBeanFactory.getBean(FacadeService.class);
        return service.getMtRealUserIdByDpUserId(dpUserId);
    }

    /**
     * 获取点评用户ID（从美团用户ID）
     */
    private CompletableFuture<Long> getDpUserIdFromMtUserId(Long mtUserId) {
        FacadeService service = AthenaBeanFactory.getBean(FacadeService.class);
        return service.getDpRealUserIdByMtUserId(mtUserId);
    }

    /**
     * 查询点评订单
     */
    private CompletableFuture<Map<Integer, Integer>> searchDpOrders(Long dpUserId, Long dpShopId,
                                                                   List<Integer> dealIds, ExecutionContext executionContext) {
        if (dpUserId == null || dpUserId <= 0 || dpShopId == null || dpShopId <= 0) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }

        // 获取点评团单ID映射
        CompletableFuture<Map<Integer, Integer>> dp2MtDealIdMapFuture = NodeUtils.getDp2MTDealIdMap(executionContext);
        return dp2MtDealIdMapFuture.thenCompose(dp2MtDealIdMap -> {
            // 将美团团单ID转换为点评团单ID
            Set<Integer> dpDealIds = new HashSet<>();
            for (Integer dealId : dealIds) {
                // 如果是美团团单ID，需要转换为点评团单ID
                if (dp2MtDealIdMap.containsValue(dealId)) {
                    // 找到对应的点评团单ID
                    for (Map.Entry<Integer, Integer> entry : dp2MtDealIdMap.entrySet()) {
                        if (entry.getValue().equals(dealId)) {
                            dpDealIds.add(entry.getKey());
                            break;
                        }
                    }
                } else {
                    // 假设是点评团单ID
                    dpDealIds.add(dealId);
                }
            }

            if (dpDealIds.isEmpty()) {
                return CompletableFuture.completedFuture(Maps.newHashMap());
            }

            OrderSearchRequest request = buildOrderSearchRequest(dpUserId, dpShopId, Lists.newArrayList(dpDealIds), true);
            SessionContext sessionContext = buildSessionContext();

            return facadeService.searchOrder(request, sessionContext)
                    .thenApply(response -> processOrderSearchResponse(response, Lists.newArrayList(dpDealIds), dp2MtDealIdMap));
        });
    }

    /**
     * 查询美团订单
     */
    private CompletableFuture<Map<Integer, Integer>> searchMtOrders(Long mtUserId, Long mtShopId,
                                                                   List<Integer> dealIds, ExecutionContext executionContext) {
        if (mtUserId == null || mtUserId <= 0 || mtShopId == null || mtShopId <= 0) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }

        // 获取美团到点评团单ID映射
        CompletableFuture<Map<Integer, Integer>> mt2DpDealIdMapFuture = NodeUtils.getMt2DpDealIdMap(executionContext);
        return mt2DpDealIdMapFuture.thenCompose(mt2DpDealIdMap -> {
            // 将点评团单ID转换为美团团单ID
            Set<Integer> mtDealIds = new HashSet<>();
            for (Integer dealId : dealIds) {
                // 如果是点评团单ID，需要转换为美团团单ID
                if (mt2DpDealIdMap.containsValue(dealId)) {
                    // 找到对应的美团团单ID
                    for (Map.Entry<Integer, Integer> entry : mt2DpDealIdMap.entrySet()) {
                        if (entry.getValue().equals(dealId)) {
                            mtDealIds.add(entry.getKey());
                            break;
                        }
                    }
                } else {
                    // 假设是美团团单ID
                    mtDealIds.add(dealId);
                }
            }

            if (mtDealIds.isEmpty()) {
                return CompletableFuture.completedFuture(Maps.newHashMap());
            }

            OrderSearchRequest request = buildOrderSearchRequest(mtUserId, mtShopId, Lists.newArrayList(mtDealIds), false);
            SessionContext sessionContext = buildSessionContext();

            return facadeService.searchOrder(request, sessionContext)
                    .thenApply(response -> processOrderSearchResponse(response, Lists.newArrayList(mtDealIds), null));
        });
    }

    /**
     * 构建订单查询请求
     */
    private OrderSearchRequest buildOrderSearchRequest(Long userId, Long shopId, List<Integer> dealIds, boolean isDp) {
        OrderSearchRequest request = new OrderSearchRequest();
        request.setPageNo(1);
        request.setPageSize(MAX_PAGE_SIZE);

        OrderSearchDetailRequest detailRequest = new OrderSearchDetailRequest();

        // 设置用户ID查询条件
        TermSearchRequest userIdTerm = new TermSearchRequest();
        if (isDp) {
            userIdTerm.setFieldName(PlatformIndexKeyEnum.DP_USER_ID.getFieldName());
        } else {
            userIdTerm.setFieldName(PlatformIndexKeyEnum.MT_USER_ID.getFieldName());
        }
        userIdTerm.setFieldValues(Collections.singleton(userId.toString()));
        userIdTerm.setInclude(true);
        detailRequest.setTermSearchRequestList(Lists.newArrayList(userIdTerm));

        // 设置门店ID查询条件
        TermSearchRequest shopIdTerm = new TermSearchRequest();
        if (isDp) {
            shopIdTerm.setFieldName(PlatformIndexKeyEnum.POI_ID.getFieldName());
        } else {
            shopIdTerm.setFieldName("mtShopId"); // 美团门店ID字段
        }
        shopIdTerm.setFieldValues(Collections.singleton(shopId.toString()));
        shopIdTerm.setInclude(true);
        detailRequest.getTermSearchRequestList().add(shopIdTerm);

        // 设置团单ID查询条件
        if (CollectionUtils.isNotEmpty(dealIds)) {
            TermSearchRequest dealIdTerm = new TermSearchRequest();
            dealIdTerm.setFieldName("bizProductId"); // 业务商品ID字段
            Set<String> dealIdStrs = dealIds.stream().map(String::valueOf).collect(Collectors.toSet());
            dealIdTerm.setFieldValues(dealIdStrs);
            dealIdTerm.setInclude(true);
            detailRequest.getTermSearchRequestList().add(dealIdTerm);
        }

        // 设置时间范围查询条件（最近180天）
        RangeSearchRequest timeRange = new RangeSearchRequest();
        timeRange.setFieldName(PlatformIndexKeyEnum.CREATE_TIME.getFieldName());
        long currentTime = System.currentTimeMillis();
        long startTime = currentTime - (QUERY_DAYS * MILLIS_PER_DAY);
        timeRange.setFrom(String.valueOf(startTime));
        timeRange.setTo(String.valueOf(currentTime));
        timeRange.setIncludeLower(true);
        timeRange.setIncludeUpper(true);
        detailRequest.setRangeSearchRequestList(Lists.newArrayList(timeRange));

        // 设置订单状态过滤（只查询已完成的订单）
        TermSearchRequest statusTerm = new TermSearchRequest();
        statusTerm.setFieldName("orderStatus");
        statusTerm.setFieldValues(Set.of("50", "60")); // 已完成状态
        statusTerm.setInclude(true);
        detailRequest.getTermSearchRequestList().add(statusTerm);

        request.setOrderSearchDetailRequestList(Lists.newArrayList(detailRequest));
        return request;
    }

    /**
     * 构建会话上下文
     */
    private SessionContext buildSessionContext() {
        SessionContext context = new SessionContext();
        context.setBizLine(BizLineEnum.DISPLAY.getBizLine()); // 使用展示链路
        return context;
    }

    /**
     * 处理订单查询响应
     */
    private Map<Integer, Integer> processOrderSearchResponse(OrderSearchResponse response,
                                                           List<Integer> dealIds,
                                                           Map<Integer, Integer> dealIdMap) {
        Map<Integer, Integer> result = new HashMap<>();

        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            // 初始化所有团单的购买次数为0
            for (Integer dealId : dealIds) {
                result.put(dealId, 0);
            }
            return result;
        }

        // 统计每个团单的购买次数
        Map<Integer, Integer> purchaseCount = new HashMap<>();
        for (Map<String, String> orderData : response.getData()) {
            String bizProductIdStr = orderData.get("bizProductId");
            if (bizProductIdStr != null) {
                try {
                    Integer bizProductId = Integer.valueOf(bizProductIdStr);
                    purchaseCount.put(bizProductId, purchaseCount.getOrDefault(bizProductId, 0) + 1);
                } catch (NumberFormatException e) {
                    LOGGER.warn("DealUserPurchaseInfoFetcher: 无效的商品ID格式: {}", bizProductIdStr);
                }
            }
        }

        // 将结果映射到原始团单ID
        for (Integer dealId : dealIds) {
            Integer count = purchaseCount.getOrDefault(dealId, 0);

            // 如果有ID映射，需要将结果映射回原始ID
            if (dealIdMap != null) {
                Integer mappedDealId = dealIdMap.get(dealId);
                if (mappedDealId != null) {
                    result.put(mappedDealId, count);
                } else {
                    result.put(dealId, count);
                }
            } else {
                result.put(dealId, count);
            }
        }

        return result;
    }

    /**
     * 合并点评和美团的查询结果
     */
    private Map<Integer, DealUserPurchaseInfoModel> mergeResults(Map<Integer, Integer> dpResult,
                                                               Map<Integer, Integer> mtResult,
                                                               List<Integer> dealIds) {
        Map<Integer, DealUserPurchaseInfoModel> finalResult = new HashMap<>();

        for (Integer dealId : dealIds) {
            int dpCount = dpResult.getOrDefault(dealId, 0);
            int mtCount = mtResult.getOrDefault(dealId, 0);
            int totalCount = dpCount + mtCount;

            DealUserPurchaseInfoModel model = new DealUserPurchaseInfoModel();
            model.setDealId(dealId);
            model.setDealPurchaseNums(totalCount);

            finalResult.put(dealId, model);
        }

        return finalResult;
    }
}
