**1.依赖

1.1 appkey

1.2 SDK

1.3 鉴权申请

1.4 大禹路由

2.通用参数

2.1 SessionContext

2.2 BaseResponse

2.3 QueryOptionDTO

3.订单查询

3.1 OrderMappingQueryService

3.1.1 query

3.1.2 batchQuery

3.2 OrderMappingMasterQueryService

3.3 OrderQueryService

3.3.1 query

3.3.2 batchQuery

3.4 OrderMasterQueryService

4.订单检索

4.1 OrderSearchService

4.1.1 search

4.1.2 searchByUserId

4.1.3 searchByOrderId

6.接口文档​正文 

* 申请对接时麻烦按照模版（查询中心接入申请模版，在文档下新增子文档即可）提供必要信息
* 如果是从老服务迁移过来的，字段映射关系见 新老模型映射​​

目录[1.依赖]()[1.1 appkey]()[1.2 SDK]()[1.3 鉴权申请]()[1.4 大禹路由]()[2.通用参数]()[2.1 SessionContext]()[2.2 BaseResponse]()[2.3 QueryOptionDTO]()[3.订单查询]()[3.1 OrderMappingQueryService]()[3.1.1 query]()[3.1.2 batchQuery]()[3.2 OrderMappingMasterQueryService]()[3.3 OrderQueryService]()[3.3.1 query]()[3.3.2 batchQuery]()[3.4 OrderMasterQueryService]()[4.订单检索]()[4.1 OrderSearchService]()[4.1.1 search]()[4.1.2 searchByUserId]()[4.1.3 searchByOrderId]()## 1.依赖

### 1.1 appkey

com.sankuai.generalorder.querycenter

### 1.2 SDK

XML**```
xxxxxxxxxx
```

 ```
<dependency>
```

```
<groupId>com.sankuai.general</groupId>
```

```
<artifactId>order-query-center-api</artifactId>
```

```
<version>${version}</version>
```

```
</dependency>
```

### 1.3 鉴权申请

申请服务粒度鉴权即可

线上鉴权申请之前麻烦按照模版（查询中心接入申请模版，在文档下新增子文档即可）提供必要信息

测试环境鉴权申请（不要直接申请，提前沟通需求，注意切换到自己的appkey）

线上环境鉴权申请（上线之前再申请，注意切换到自己的appkey）

octo鉴权申请示例​​

### 1.4 大禹路由

上线之前联系 @王子  进行大禹路由配置

## 2.通用参数

### 2.1 SessionContext

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | bizLine | int | 业务身份 | BizLineEnum

DEFAULT(0, "默认链路"),TRADE(1, "交易链路"),DISPLAY(2, "展示链路");

默认链路为兜底，非交易场景使用展示链路 |

### 2.2 BaseResponse

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | code | int | 状态码 | ResponseCodeEnum

SUCCESS(200, "成功"),ILLEGAL_PARAMS(401, "请求参数错误"),UNKNOWN_ERROR(500, "未知异常"),INTERNAL_EXCEPTION(501, "内部异常"),EXTERNAL_EXCEPTION(502, "外部异常"), |
|  | errMessage | String | 错误信息 |  |

### 2.3 QueryOptionDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | queryParams | List<Integer> | 需要查询的数据 | QueryParamEnum

ORDER_BASIC_INFO(101, "订单基本信息"),ORDER_STATUS(102, "订单状态信息"),ORDER_STATUS_DETAIL(103, "订单状态明细信息"),ORDER_ENVIRONMENT(104, "订单环境信息"),ORDER_EXTRA_FIELD(105, "订单扩展字段"),ORDER_RELATION(106, "订单关联信息"),ORDER_ITEM(107, "订单商品信息"),ORDER_ITEM_DETAIL(108, "订单商品明细信息"),ORDER_ITEM_EXTRA_FIELD(109, "订单商品扩展字段"),ORDER_ITEM_SNAPSHOT(110, "订单商品快照"),ORDER_ADDITION_FEE(111, "订单附加费用"),PRE_AMOUNT_DETAIL(201, "下单金额明细信息"),PRE_AMOUNT_DETAIL_EXTRA_FIELD(202, "下单金额明细信息"),PAYMENT(203, "订单支付单信息"),PAYMENT_EXTRA_FIELD(204, "订单支付单扩展字段"),PAYMENT_DETAIL(205, "订单支付金额明细信息"),PAYMENT_DETAIL_EXTRA_FIELD(206, "订单支付金额明细扩展字段"),RECEIPT_AMOUNT_DETAIL(301, "订单凭证金额分摊明细信息"), |
|  | decryptMobile | boolean | 是否对手机号解密 |  |

各查询条件返回的数据

|  | 查询条件 | 返回数据 | 备注 |
| --- | --- | --- | --- |
|  | ORDER_BASIC_INFO(101, "订单基本信息") | UniOrderDTO.orderBasic |  |
|  | ORDER_STATUS(102, "订单状态信息") | UniOrderDTO.orderStatusDTO | 不包含 statusDetail |
|  | ORDER_STATUS_DETAIL(103, "订单状态明细信息") | UniOrderDTO.orderStatusDTO |  |
|  | ORDER_ENVIRONMENT(104, "订单环境信息") | UniOrderDTO.environment |  |
|  | ORDER_EXTRA_FIELD(105, "订单扩展字段") | UniOrderDTO.orderExtraMap |  |
|  | ORDER_RELATION(106, "订单关联信息") | UniOrderDTO.orderRelation |  |
|  | ORDER_ITEM(107, "订单商品信息") | UniOrderDTO.orderItem | 不包含 orderItemDetails |
|  | ORDER_ITEM_DETAIL(108, "订单商品明细信息") | UniOrderDTO.orderItem.orderItemDetails | 不包含 OrderItemDetailDTO.itemSnapshot

不包含 OrderItemDetailDTO.extraMap |
|  | ORDER_ITEM_EXTRA_FIELD(109, "订单商品扩展字段") | UniOrderDTO.orderItem.orderItemDetails.extraMap | 不包含 OrderItemDetailDTO.itemSnapshot |
|  | ORDER_ITEM_SNAPSHOT(110, "订单商品快照") | UniOrderDTO.orderItem.orderItemDetails.itemSnapshot | 不包含 OrderItemDetailDTO.extraMap

PS：非必要不建议查询该字段，对性能有影响 |
|  | ORDER_ADDITION_FEE(111, "订单附加费用") | UniOrderDTO.additionalFees |  |
|  | PRE_AMOUNT_DETAIL(201, "下单金额明细信息") | UniOrderDTO.preAmountDetails | 不包含 PreAmountDetailDTO.extraMap |
|  | PRE_AMOUNT_DETAIL_EXTRA_FIELD(202, "下单金额明细信息") | UniOrderDTO.preAmountDetail.extraMap |  |
|  | PAYMENT(203, "订单支付单信息") | UniOrderDTO.payments | 不包含 PaymentDTO.paymentDetails

不包含 PaymentDTO.extraMap |
|  | PAYMENT_EXTRA_FIELD(204, "订单支付单扩展字段") | UniOrderDTO.payments.extraMap | 不包含 PaymentDTO.paymentDetails |
|  | PAYMENT_DETAIL(205, "订单支付金额明细信息") | UniOrderDTO.payments.paymentDetails | 不包含 PaymentDetailDTO.extraMap |
|  | PAYMENT_DETAIL_EXTRA_FIELD(206, "订单支付金额明细扩展字段") | UniOrderDTO.payments.paymentDetails.extraMap |  |
|  | RECEIPT_AMOUNT_DETAIL(301, "订单凭证金额分摊明细信息") | UniOrderDTO.receiptAmountDetails |  |

## 3.订单查询

### 3.1 OrderMappingQueryService

#### 3.1.1 query

非主库查询

OrderMappingQueryRequest

|  | 字段名 | 类型 | 是否必填 | 描述 | 备注 |
| --- | --- | --- | --- | --- | --- |
|  | uniOrderId | String | 和longOrderId二选一 | 统一订单号 | 和longOrderId二选一必填，如果都存在，以uniOrderId为准 |
|  | longOrderId | Long | 和uniOrderId二选一 | 长订单号 | 和uniOrderId二选一必填，如果都存在，以uniOrderId为准 |
|  | original | boolean | 否 | 是否为16年之前的团购订单号 | 2016年以后的订单无需关注 |

OrderMappingResponse

|  | 字段名 | 类型 | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  | data | OrderMappingDTO | uniOrderId | String | 统一订单号 |  |
|  |  |  | longOrderId | Long | 长订单号 |  |
|  |  |  | originalOrderId | Long | 团购订单号 | 2016年以后的订单无需关注 |
|  |  |  | bizCode | String | 交易类型 | com.sankuai.general.order.querycenter.api.enums.ProductEnum |
|  |  |  | bizType | Integer | 交易类型 | com.sankuai.general.order.querycenter.api.enums.ProductEnum |

#### 3.1.2 batchQuery

非主库查询

OrderMappingBatchQueryRequest

|  | 字段名 | 类型 | 是否必填 | 描述 | 备注 |
| --- | --- | --- | --- | --- | --- |
|  | uniOrderIds | List<String> | 和longOrderIds二选一 | 统一订单号 | 和longOrderIds二选一必填，如果都存在，以uniOrderIds为准，最多为50个 |
|  | longOrderIds | List<Long> | 和uniOrderIds二选一 | 长订单号 | 和uniOrderIds二选一必填，如果都存在，以uniOrderIds为准，最多为50个 |
|  | original | boolean | 否 | 是否为16年之前的团购订单号 | 2016年以后的订单无需关注 |

OrderMappingListResponse

|  | 字段名 | 类型 | 描述 |
| --- | --- | --- | --- |
|  | data | List<OrderMappingDTO> | 见 OrderMappingResponse |

### 3.2 OrderMappingMasterQueryService

主库查询，参数同2.1

### 3.3 OrderQueryService

#### 3.3.1 query

非主库查询

JSON**```
xxxxxxxxxx
```

```
"prototype_blockList": "[{\"key\":\"headImage\",\"value\":\"{\\\"content\\\":[{\\\"data\\\":[{\\\"desc\\\":\\\"\\\",\\\"path\\\":\\\"https://p0.meituan.net/dpmerchantpic/89c6bd0d344d1bec930db9275dded30218703.png\\\",\\\"title\\\":\\\"\\\"},{\\\"desc\\\":\\\"\\\",\\\"path\\\":\\\"https://p1.meituan.net/dpmerchantpic/fb507c7d2a945cd162e3b0cc0da804aa19041.png\\\",\\\"title\\\":\\\"\\\"},{\\\"desc\\\":\\\"\\\",\\\"path\\\":\\\"https://p1.meituan.net/dpmerchantpic/68cf483c5ef7555340e2ac0e4577480d15904.jpg\\\",\\\"title\\\":\\\"\\\"}],\\\"type\\\":\\\"imageList\\\"}],\\\"headline\\\":\\\"头图\\\",\\\"version\\\":1}\"},{\"key\":\"detailInfo\",\"value\":\"{\\\"content\\\":[{\\\"data\\\":{\\\"totalPrice\\\":50.00,\\\"salePrice\\\":39.00,\\\"groups\\\":[{\\\"units\\\":[{\\\"skuCateId\\\":840,\\\"amount\\\":1,\\\"price\\\":50.0,\\\"updateTime\\\":1621494080000,\\\"attrValues\\\":{\\\"duration\\\":\\\"1\\\",\\\"dressNum\\\":\\\"0\\\",\\\"photoEnv\\\":\\\"棚内\\\",\\\"photoOutput\\\":\\\"1寸或者2寸一版\\\",\\\"applytarget\\\":\\\"个人\\\",\\\"spuCategory\\\":\\\"护照、签证照、报名照\\\",\\\"dressStyle\\\":\\\"自备\\\",\\\"intensiveRepairNum\\\":\\\"0\\\",\\\"photoBackground\\\":\\\"红、白、蓝\\\",\\\"poseProduct\\\":\\\"无\\\",\\\"photoCount\\\":\\\"5\\\"},\\\"projectName\\\":\\\"证件照\\\",\\\"properties\\\":\\\"快照；1小时；护照、签证照、报名照；个人；无；0套；自备；棚...\\\",\\\"skuId\\\":0}],\\\"optionalCount\\\":0}]},\\\"type\\\":\\\"uniform-structure-table\\\"}],\\\"headline\\\":\\\"团购详情\\\",\\\"version\\\":1}\"},{\"key\":\"productIntro\",\"value\":\"{\\\"content\\\":[{\\\"data\\\":[{\\\"desc\\\":\\\"\\\",\\\"path\\\":\\\"https://p0.meituan.net/dpmerchantpic/7f988c8a757bb1bf0b1d1a102ed4898f254060.jpg\\\",\\\"title\\\":\\\"\\\"}],\\\"type\\\":\\\"imageList\\\"},{\\\"data\\\":[{\\\"desc\\\":\\\"\\\",\\\"path\\\":\\\"https://p1.meituan.net/dpmerchantpic/ad07dbbf817fd7662f8a1d283260bbab186285.jpg\\\",\\\"title\\\":\\\"\\\"}],\\\"type\\\":\\\"imageList\\\"}],\\\"headline\\\":\\\"产品介绍\\\",\\\"version\\\":1}\"}]",
```

 ```
查询条件：
```

```
1.{"uniOrderId":"4981003387839621828","queryOption":{"queryParams":[101,102,103,104,105,106,107,108,109,110,111,201,202,203,204,205,206,301],"decryptMobile":false}}
```

```
2.{"bizLine":2}
```

```
返回数据：
```

```
{
```

```
"code": 200,
```

```
"errMessage": null,
```

```
"data": {
```

```
"uniOrderId": "4981003387839621828",
```

```
"longOrderId": "4981003387839621828",
```

```
"bizCode": "nib.general.groupbuy",
```

OrderQueryRequest

|  | 字段名 | 类型 | 是否必填 | 描述 | 备注 |
| --- | --- | --- | --- | --- | --- |
|  | uniOrderId | String | 是 | 订单号 | 支持统一订单号和长订单号，但不支持originalOrderId |
|  | queryOption | QueryOptionDTO | 否 | 需要查询的数据 | 不传时，仅返回 uniOrderId、longOrderId、bizCode、bizType |

OrderResponse

|  | 字段名 | 类型 | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  | data | UniOrderDTO | uniOrderId | String | 统一订单号 |  |
|  |  |  | longOrderId | Long | 长订单号 |  |
|  |  |  | bizCode | String | 交易类型 | com.sankuai.general.order.querycenter.api.enums.ProductEnum |
|  |  |  | bizType | Integer | 交易类型 | com.sankuai.general.order.querycenter.api.enums.ProductEnum |
|  |  |  | orderBasic | OrderBasicDTO | 订单基本信息 |  |
|  |  |  | orderStatusDTO | OrderStatusDTO | 订单状态信息 |  |
|  |  |  | environment | OrderEnvironmentDTO | 订单环境信息 |  |
|  |  |  | orderExtraMap | Map<String, String> | 订单扩展信息 |  |
|  |  |  | orderRelation | OrderRelationDTO | 关联订单 | 老订单不存在 |
|  |  |  | orderItem | OrderItemDTO | 商品信息 |  |
|  |  |  | additionalFees | List<AdditionalFeeDTO> | 附加费用 |  |
|  |  |  | preAmountDetails | List<PreAmountDetailDTO> | 下单金额明细 |  |
|  |  |  | payments | List<PaymentDTO> | 支付信息 |  |
|  |  |  | receiptAmountDetails | List<ReceiptAmountDetailDTO> | 凭证金额明细 |  |

OrderBasicDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | dpUserId | Long | 点评用户ID |  |
|  | mtUserId | Long | 美团用户ID |  |
|  | platform | Integer | 下单平台 | com.sankuai.general.order.querycenter.api.enums.OrderPlatformEnum |
|  | addAt | Long | 下单时间 |  |

OrderStatusDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | status | Integer | 订单状态 | com.sankuai.general.order.querycenter.api.enums.UniOrderStatusEnum |
|  | subStatus | Integer | 订单子状态 | com.sankuai.general.order.querycenter.api.enums.OrderSubStatusEnum |
|  | hide | Boolean | 是否隐藏 |  |
|  | orderVersion | Long | 订单版本号 |  |
|  | terminated | Boolean | 是否终止 |  |
|  | createAt | Long | 创建时间 |  |
|  | updatedAt | Long | 更新时间 |  |
|  | expiredAt | Long | 过期时间 |  |
|  | statusDetail | OrderStatusDetailDTO | 状态明细 |  |

OrderStatusDetailDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | buySuccessAt | Long | 购买成功时间 |  |
|  | tradeFinishAt | Long | 消费完成时间 |  |
|  | bookStartAt | Long | 预订开始时间 |  |
|  | bookEndAt | Long | 预订结束时间 |  |
|  | paySuccessAt | Long | 支付成功时间 | 需要查询 PAYMENT_DETAIL(205) |
|  | hasConsumed | Boolean | 是否有消费 |  |
|  | hasPayed | Boolean | 是否有支付 | 需要查询 PAYMENT_DETAIL(205) |
|  | availableQuantity | Integer | 有效消费项数量 |  |

OrderEnvironmentDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | dpPoiId | Long | 点评门店ID |  |
|  | mtPoiId | Long | 美团门店ID |  |
|  | dpCityId | Integer | 点评城市ID |  |
|  | mtCityId | Integer | 美团城市ID |  |
|  | mobileNo | String | 下单手机号 |  |
|  | encryptedMobileNo | String | 加密后的下单手机号 |  |
|  | userIp | String | IP地址 |  |
|  | uuid | String | 设备号 |  |
|  | version | String | 版本号 |  |

OrderRelationDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | parentOrderId | Long | 父订单ID |  |
|  | orderId | Long | 订单ID |  |
|  | isValid | Integer | 是否有效 |  |
|  | relationType | Integer | 搭售类型 | com.sankuai.general.order.querycenter.api.enums.OrderRelationTypeEnum |

OrderItemDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | totalAmount | Long | 总金额，单位：分 |  |
|  | totalQuantity | Integer | 总数量 |  |
|  | orderItemDetails | OrderItemDetailDTO | 商品详情 |  |

OrderItemDetailDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | productId | Long | 平台productId |  |
|  | skuId | Long | 平台SkuId |  |
|  | bizProductId | Long | 业务productId |  |
|  | bizSkuId | Long | 业务SkuId |  |
|  | skuTitle | String | 业务sku名称 |  |
|  | bizCategoryId | String | 业务商品类型 |  |
|  | snapshotId | String | 快照ID |  |
|  | fingerprint | String | 快照指纹 |  |
|  | totalPrice | Long | 总价 |  |
|  | unitPrice | Long | 单价 |  |
|  | quantity | Integer | 份数 |  |
|  | productSource | Integer | 商品来源 | com.meituan.nibtp.trade.client.buy.enums.OrderItemProductSourceEnum

老订单不存在 |
|  | itemSnapshot | OrderItemSnapshotDTO | 快照信息 |  |
|  | extraMap | Map<String, String> | 扩展信息 |  |

OrderItemSnapshotDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | productName | String | 商品名称 |  |
|  | skuName | String | sku名称 |  |
|  | categoryId | Long | 商品类型 |  |
|  | customerId | Long | 客户ID |  |
|  | orderItemRule | OrderItemRuleDTO | 商品规则 |  |
|  | productInfo | Map<String, String> | 商品扩展信息 |  |
|  | skuExtendInfo | Map<String, String> | sku扩展信息 |  |
|  | packageInfo | Map<String, String> | 套餐扩展信息 |  |

OrderItemRuleDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | skuPoiList | List<SkuPoiDTO> | 门店信息 |  |
|  | priceGuaranteeRules | List<PriceGuaranteeRuleDTO> | 价保信息 |  |

SkuPoiDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | poiId | long | 门店ID |  |
|  | cityId | long | 城市ID |  |
|  | isUsable | boolean | 使用中 |  |
|  | isVisible | boolean | 是否可见 |  |

PriceGuaranteeRuleDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | guaranteeId | Long | 规则ID |  |
|  | guaranteeType | Integer | 价保类型 | com.sankuai.mpproduct.trade.api.enums.GuaranteeTypeEnum |
|  | source | PriceGuaranteeSourceDTO | 价保来源 |  |
|  | compensableRule | CompensableRuleDTO | 价保规则 |  |
|  | ruleHit | RuleHitDTO | 价保规则命中信息 |  |
|  | compensationRule | CompensationRuleDTO | 买贵赔承担方规则 |  |

PriceGuaranteeSourceDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | sourceType | Integer | 来源类型 |  |
|  | sourceId | String | 来源ID |  |

CompensableRuleDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | validityRule | ValidityRuleDTO | 有效期信息 |  |

ValidityRuleDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | validityType | Integer | 有效期类型 | 1-区间, 2-多少天内, 3-当天某时间前, 当为1时, 看beginDateTime&endDateTime, 当为2时, 看validityDays, 当为3时, 看beforeTimeToday |
|  | beginDateTime | String | 有效期开始时间 |  |
|  | endDateTime | String | 有效期结束时间 |  |
|  | validityDays | Integer | 多少天内有效/有效天数 |  |
|  | beforeTimeToday | String | 当天某时间前, 如23:59:59 |  |

RuleHitDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | channel | String | 命中的渠道 |  |

CompensationRuleDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | responsibleRuleDTO | ResponsibleRuleDTO | 承担方规则信息 |  |

ResponsibleRuleDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | attributionType | Integer | 赔付承担方规则 |  |

AdditionalFeeDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | skuId | Long | 费用绑定的skuId |  |
|  | amount | Long | 费用金额 |  |

PreAmountDetailDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | payBatchNo | Integer | 支付批次 |  |
|  | amountType | Integer | 资金类型 | com.sankuai.general.order.querycenter.api.enums.AmountTypeEnum |
|  | amountSubType | Integer | 资金业务类型 | com.sankuai.general.order.querycenter.api.enums.AmountSubTypeEnum |
|  | amountDetailType | Integer | 资金细分类型 | com.meituan.nibtp.trade.client.buy.enums.PaymentAmountDetailTypeEnum

老订单不存在 |
|  | amountPayType | Integer | 渠道支付方式 | com.meituan.nibtp.trade.client.buy.enums.PaymentAmountPayMethodEnum

老订单不存在 |
|  | amountId | String | 优惠ID |  |
|  | amountSubId | String | 支付子ID |  |
|  | amountCostType | Integer | 成本承担类型 | com.sankuai.general.order.querycenter.api.enums.AmountCostTypeEnum |
|  | amount | Long | 总金额 |  |
|  | platformAmount | Long | 平台承担预算成本 |  |
|  | merchantAmount | Long | 商家承担预算成本 |  |
|  | extraMap | Map<String, String> | 扩展信息 |  |

PaymentDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | paymentId | String | 支付单ID | 老订单不存在 |
|  | orderPaymentId | String | 订单支付单ID |  |
|  | status | Integer | 支付单状态 | com.meituan.nibtp.trade.client.buy.enums.PaymentStatusEnum

老订单不存在 |
|  | totalAmount | Long | 支付单总金额 | 老订单不存在 |
|  | payBatchNo | Integer | 支付批次 | 老订单不存在 |
|  | payChannel | Integer | 支付渠道 | com.meituan.nibtp.trade.pay.client.enums.PayChannelEnum

老订单不存在 |
|  | createdAt | Long | 创建时间 |  |
|  | outBizPaymentInfo | String | 三方预支付参数 | 老订单不存在 |
|  | paymentDetails | PaymentDetailDTO | 支付明细 |  |
|  | extraMap | Map<String, String> | 扩展信息 |  |

PaymentDetailDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | orderPaymentDetailId | String | 订单支付单明细ID |  |
|  | orderPaymentId | String | 支付单ID |  |
|  | paymentDetailId | String | 支付单明细ID |  |
|  | paymentReceiptId | String | 支付凭证ID |  |
|  | nibAmountType | Integer | 平台资金类型 | com.meituan.nibtp.trade.client.buy.enums.PaymentAmountTypeEnum

老订单不存在 |
|  | amountType | Integer | 资金类型 | com.sankuai.general.order.querycenter.api.enums.AmountTypeEnum |
|  | amountSubType | Integer | 资金业务类型 | com.sankuai.general.order.querycenter.api.enums.AmountSubTypeEnum |
|  | amountDetailType | Integer | 资金细分类型 | com.meituan.nibtp.trade.client.buy.enums.PaymentAmountDetailTypeEnum

老订单不存在 |
|  | amountPayType | Integer | 支付方式 | com.meituan.nibtp.trade.client.buy.enums.PaymentAmountPayMethodEnum

老订单不存在 |
|  | amountId | String | 优惠ID |  |
|  | outerId | String | 外部ID-优惠ID | 老订单不存在 |
|  | amountSubId | String | 支付子ID |  |
|  | amountCostType | Integer | 成本承担类型 | com.sankuai.general.order.querycenter.api.enums.AmountCostTypeEnum |
|  | amount | Long | 总金额 |  |
|  | platformAmount | Long | 平台承担成本预算 |  |
|  | merchantAmount | Long | 商家承担成本预算 |  |
|  | payMode | Integer | 支付模式 | com.sankuai.general.order.querycenter.api.enums.PayModeEnum |
|  | iphPayMerchantNo | String | 业务识别码 |  |
|  | subIphPayMerchantNo | String | 二级商户号 |  |
|  | status | Integer | 状态 | com.meituan.nibtp.trade.client.buy.enums.PaymentDetailStatusEnum |
|  | succeedAt | Long | 支付成功时间 |  |
|  | extraMap | Map<String, String> | 扩展信息 |  |

ReceiptAmountDetailDTO

|  | 字段名 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
|  | receiptId | String | 凭证ID |  |
|  | receiptType | Integer | 凭证类型 | com.sankuai.general.order.querycenter.api.enums.OrderReceiptTypeEnum |
|  | receiptMode | Integer | 凭证分摊模式 | com.sankuai.general.order.querycenter.api.enums.OrderReceiptModeEnum |
|  | amount | Long | 分摊金额 |  |
|  | amountType | Integer | 资金类型 | com.sankuai.general.order.querycenter.api.enums.AmountTypeEnum |
|  | amountSubType | Integer | 资金业务类型 | com.sankuai.general.order.querycenter.api.enums.AmountSubTypeEnum |
|  | amountCostType | Integer | 成本承担类型 | com.sankuai.general.order.querycenter.api.enums.AmountCostTypeEnum |
|  | platformAmount | Long | 平台承担预算成本 |  |
|  | merchantAmount | Long | 商家承担预算成本 |  |

#### 3.3.2 batchQuery

非主库查询

OrderBatchQueryRequest

|  | 字段名 | 类型 | 是否必填 | 描述 | 备注 |
| --- | --- | --- | --- | --- | --- |
|  | uniOrderIds | List<String> | 是 | 订单号 | 支持统一订单号和长订单号，但不支持originalOrderId

最多为20个 |
|  | queryOption | QueryOptionDTO | 否 | 需要查询的数据 | 不传时，仅返回 uniOrderId、longOrderId、bizCode、bizType |

OrderListResponse

|  | 字段名 | 类型 | 描述 |
| --- | --- | --- | --- |
|  | data | List<UniOrderDTO> | 见 OrderResponse |

### 3.4 OrderMasterQueryService

主库查询，参数同3.3

## 4.订单检索

### 4.1 OrderSearchService

ES查询

#### 4.1.1 search

OrderSearchRequest

接口入参有两套查询方式：

1.不带模板信息的，needTemplateSearch=false，orderSearchDetailRequestList 必传

2.带模板信息的，needTemplateSearch=true，orderSearchDetailTemplateRequestList必传，非必要不采用这种方式

这里提供模板查询方式是兼容老的历史查询，可以通模板信息去数据库里动态获取对应bizType和一些ExtraInfo信息的查询

可参考复用入参条件构造工具 OrderSearchAssistBuilderUtils.java

| 字段 | 类型 | 是否必填 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
| needSearchAfter | boolean | 否 | 是否启用search_after查询，默认否 |  |
| sortValues | List<String> | 否 | search_after查询翻页的排序字段（当查询非第一页数据时，必填） | 第一次请求不传，之后每一次请求都带上上一次请求返回中的排序值 |
| orderSearchDetailRequestList | List<OrderSearchDetailRequest> | needTemplateSearch=false时必传，查询条件 or 关系 |  |
| sortFieldRequestList | List<SortFieldRequest> | 否 | 排序字段 |  |
| trackTotalHits | Integer | 否 | 查询之后计算的命中数,不传默认1w |  |
| 模板参数 |
| needTemplateSearch | boolean | 否 | 是否支持模板查询，默认否 |  |
| orderSearchDetailTemplateRequestList | List<OrderSearchDetailTemplateRequest> | needTemplateSearch=true时必传，查询条件 or 关系 |  |
| 分页参数 |
| pageNo | int | 是 | 分页号从1开始，不能小于1 |  |
| pageSize | int | 是 | 1.分页大小不能超过1000

2.needSearchAfter=false时查询数据量不能超过10000（pageNo  * pageSize <= 10000） |  |
| 返回字段参数 |
| returnFields | List<String> | 否 | 如果不传值默认返回订单基本索引字段

如果传值则返回基本索引字段+指定返回字段 | JSON**```
xxxxxxxxxx
```

 ```
PlatformIndexKeyEnum.ORDER_ID
```

```
PlatformIndexKeyEnum.MT_USER_ID
```

```
PlatformIndexKeyEnum.CREATE_TIME
```

```
OrderSearchFieldConstants.ADD_TIME
```

```
PlatformIndexKeyEnum.CUSTOMER_ID
```

```
OrderSearchFieldConstants.CUSTOMER_ID
```

```
PlatformIndexKeyEnum.POI_ID
```

```
OrderSearchFieldConstants.MT_SHOP_ID
``` |

OrderSearchDetailTemplateRequest

| 字段 | 类型 | 是否必填 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
| searchTemplateId | Integer | 是 | 对于查询条件里的通用参数字段进行映射 |  |
| orderSearchDetailRequestList | List<OrderSearchDetailRequest> | 是 | 查询条件 |  |
| sortFieldExtraInfoRequestList | List<SortFieldRequest> | 否 | 扩展模板字段-需要映射-排序条件 |  |
| needExtraInfoSortDefault | boolean | 否 | 排序条件，如果映射失败找不到是否需要走兜底逻辑 |  |

OrderSearchDetailRequest

| 字段 | 类型 | 是否必填 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
| filterZeroAmountOrder | boolean | 否 | 是否过滤掉0元单，默认否 |  |
| filterEmptyMobileNo | boolean | 否 | 是否过滤掉空手机号，默认否 |  |
| excludeDisplayModeList | List<String> | 否 | 排除展示模式列表 |  |
| displayModeList | List<String> | 否 | 展示模式列表 |  |
| platformFlag | Integer | 否 | 平台标识 PlatformFlagEnum |  |
| filterDefaultBizCode | boolean | 否 | 是否过滤掉默认bizcode，默认否 |  |
| 通用参数(PlatformIndexKeyEnum) |
| termSearchRequestList | List<TermSearchRequest> | 否 | 等值查询 | 不能全为空 |
| matchSearchRequestList | List<MatchSearchRequest> | 否 | 模糊查询 |
| rangeSearchRequestList | List<RangeSearchRequest> | 否 | 范围查询 |
| termSearchExtraInfoRequestList | List<TermSearchRequest> | 否 | 模板字段等值查询 | 模板模式用的字段，不能全为空 |
| matchSearchExtraInfoRequestList | List<MatchSearchRequest> | 否 | 模板字段模糊查询 |
| rangeSearchExtraInfoRequestList | List<RangeSearchRequest> | 否 | 模板字段范围查询 |

TermSearchRequest（字段名称采用PlatformIndexKeyEnum枚举）

| 字段 | 类型 | 是否必填 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
| fieldName | String | 是 | 查询字段名称 |  |
| fieldValues | Set<String> | 是 | 查询字段取值 |  |
| include | Boolean | 否 | 是否包含查询字段取值 | true等价于IN查询，false等价于NOT IN查询 |

MatchSearchRequest（字段名称采用PlatformIndexKeyEnum枚举）

| 字段 | 类型 | 是否必填 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
| fieldName | String | 是 | 查询字段名称 |  |
| fieldValue | String | 是 | 查询字段取值 |  |

RangeSearchRequest（字段名称采用PlatformIndexKeyEnum枚举）

| 字段 | 类型 | 是否必填 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
| fieldName | String | 是 | 字段名称 |  |
| from | String | 是 | 开始 | 时间字段，则为毫秒时间戳 |
| to | String | 是 | 结束 |
| includeLower | Boolean | 否 | 开/闭区间 | 默认true |
| includeUpper | Boolean | 否 | 默认false |

SortFieldRequest（字段名称采用PlatformIndexKeyEnum枚举）

| 字段 | 类型 | 是否必填 | 描述 | 备注 |
| --- | --- | --- | --- | --- |
| fieldName | String | 是 |  |  |
| asc | Boolean | 否 | 升序或者降序,默认升序 |  |

OrderSearchResponse

| 字段 | 类型 | 描述 | 备注 |
| --- | --- | --- | --- |
| totalHit | long | 记录条数 |  |
| data | List<Map<String, String>> | 查询结果 | 每个订单号对应一条记录，以Map组织

Map的key为 PlatformIndexKeyEnum.fieldName，Map的value为该字段对应的具体值 |

#### 4.1.2 searchByUserId

同 4.1.1，但查询请求的fieldName中必须包含dpUserId或者mtUserId其一

#### 4.1.3 searchByOrderId

同 4.1.1，但查询请求的fieldName中必须包含unifiedOrderId

Java**```
xxxxxxxxxx
```

```
REFUND_TEMPLATE_ID("refundTemplateId", true, false, false, true),
```

 ```
public enum PlatformIndexKeyEnum {
```

```
​
```

```
ORDER_ID("orderId", true, false, false, true),
```

```
UNIFIED_ORDER_ID("unifiedOrderId", true, false, false, true),
```

```
MT_USER_ID("mtUserId", true, false, false, true),
```

```
DP_USER_ID("dpUserId", true, false, false, true),
```

```
CUSTOMER_ID("customerId", true, false, false, true),
```

```
//点评shopid
```

```
POI_ID("poiId", true, false, false, true),
```

```
DP_SHOP_ID("dpShopId", true, false, false, true),
```

```
BIZ_CODE("bizCode", true, false, false, true),
```

Java**```
xxxxxxxxxx
```

 ```
public enum  UnifiedOrderStatus {
```

```
INIT(0,"初始状态"),
```

```
STOCK_LOCKED(1,"库存锁定"),
```

```
CANCELLED(2,"已取消"),
```

```
STOCK_WAITING_OUTING(3,"待出库"),
```

```
STOCK_TRANSFERED(4,"已出库"),
```

```
STOCK_REACHED(5,"已触达"),
```

```
TRADE_FINISHED(6,"交易完成"),
```

```
STOCK_FAILED(7,"出库失败")
```

```
;
```

```
}
```

Java**```
xxxxxxxxxx
```

```
PAY_TRANSFER_NEED_USER_INFORMATION("0304", "转入代发(强提)，需要用户提供新卡信息", true),
```

 ```
public enum RefundStateCode {
```

```
​
```

```
//未指定状态
```

```
UNSPECIFIED("0000", "未指定状态", false),
```

```
​
```

```
//平台状态
```

```
CREATED("0001", "退款申请已创建",false),
```

```
CANCEL("0002", "退款取消",true),
```

```
CONFIRM("0003", "退款确认, 资金退款中", true),
```

```
COMPLETE("0004", "退款完成",true),
```

```
FAIL("0005", "对不起，退款失败", true),
```
