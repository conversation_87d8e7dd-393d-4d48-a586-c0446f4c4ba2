# FacadeService 在项目中的核心作用

## 1. 架构定位

- 作为一个门面服务(Facade Pattern)，封装了与外部系统的交互
- 统一管理各种RPC调用，是项目与外部服务交互的统一入口

## 2. 主要功能

### 数据查询聚合
- 团单信息查询
- 价格查询
- 商品信息查询
- 用户信息查询
- 门店信息查询

### 异步处理
- 所有方法返回 CompletableFuture
- 支持并发批量查询

### 异常统一处理和降级

### 跨平台适配
- 处理美团和点评两个平台的数据映射
- 统一ID转换
- 数据格式转换

## 3. 技术特点

```java
// 1. 异步调用
CompletableFuture<T> result = facadeService.someMethod();

// 2. 批量处理
CompletableFuture<Map<K,V>> batchResult = facadeService.batchQuery();

// 3. 异常处理
return CompletableFuture.exceptionally(ex -> {
    commonExceptionHandler("methodName", params, ex);
    return fallbackValue;
});
```

## 4. 使用场景

- 被各种 Fetcher 调用获取数据
- 提供统一的数据访问接口
- 处理跨服务调用的复杂性

## 5. 优势

- 降低系统耦合度
- 统一异常处理
- 提高代码复用性
- 便于性能优化和监控

总之，FacadeService 是该项目的核心基础设施之一，承担了外部服务调用的统一入口职责。