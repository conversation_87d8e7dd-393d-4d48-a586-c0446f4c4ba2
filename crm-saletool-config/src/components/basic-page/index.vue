<template>
  <div class="root" v-bind="$attrs">
    <div :class="['container', $slots.bottom ? 'with-bottom-area' : null]">
      <section class="navigator">
        <div class="back-button" @click="backHandlerWrapper">
          <mtd-icon name="mtdicon-left" />
        </div>
        <slot name="title"></slot>
        <div v-if="!$slots.title && title" class="title">{{ title }}</div>
      </section>
      <section class="main-area">
        <slot></slot>
      </section>
    </div>
    <section v-if="$slots.bottom" class="bottom-area">
      <slot name="bottom"></slot>
    </section>
  </div>
</template>

<script>
export default {
  props: {
    backHandler: {
      type: Function,
    },
    title: {
      type: String,
    },
  },
  data() {
    return {}
  },
  methods: {
    backHandlerWrapper() {
      if (this.backHandler) {
        this.backHandler(this)
      } else {
        this.$router.back()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.root {
  height: 100%;
  width: 100%;

  margin: 0px;
  padding: 0px;

  > .container {
    height: 100%;
    padding: 16px;

    &.with-bottom-area {
      height: calc(100% - 56px);
    }

    > .navigator {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 25px;

      > .title {
        font-weight: 500;
        font-size: 16px;
      }

      > .back-button {
        height: 22px;
        width: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        border: 1px solid rgba(0, 0, 0, 0.12);
      }
    }

    > .main-area {
      height: calc(100% - 25px);
    }
  }

  > .bottom-area {
    display: flex;
    position: absolute;
    bottom: 0px;
    height: 56px;
    width: 100%;
    align-items: center;
    background-color: #fff;
    padding: 12px 16px;

    box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.08);
  }
}
</style>
