<template>
  <div class="universal-box">
    <div class="universal-header">
      <div class="universal-title">
        <slot name="title">{{ title }}</slot>
      </div>
      <div class="universal-action">
        <slot name="action"></slot>
      </div>
    </div>
    <div class="universal-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UniversalBox',
  props: {
    title: {
      type: String,
      default: '',
    },
  },
}
</script>
<style lang="scss" scoped>
.universal-box {
  padding: 24px 16px 0;
  display: flex;
  flex-direction: column;

  .universal-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &::before {
      content: '';
      position: absolute;
      height: 16px;
      width: 4px;
      left: -16px;
      background-color: var(--color-primary);
      border-radius: 0px 2px 2px 0px;
    }

    .universal-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 16px;
      letter-spacing: 0px;
    }
  }

  .universal-content {
    flex: 1;
    overflow: auto;
  }
}
</style>
