<template>
  <div class="phone-box">
    <div>{{ displayPhone }}</div>
    <i
      class="mtdicon eye-icon"
      :class="visibility ? 'mtdicon-visibility-off-o' : 'mtdicon-visibility-on-o'"
      @click="changeVisibility"
    ></i>
  </div>
</template>

<script>
// 判断号码是否是手机号
const isMobilephoneNumber = numStr => /^1\d{10}/.test(numStr)

export default {
  name: 'PhoneNum',
  props: {
    phone: {
      type: [String, Number],
      default: '',
    },
    plainText: {
      type: [Function, String],
      default: null,
    },
  },
  data() {
    return {
      visibility: false,
      phoneNum: '',
    }
  },
  computed: {
    displayPhone() {
      return this.visibility ? this.phoneNum : this.getMaskedPhone()
    },
  },
  methods: {
    getMaskedPhone() {
      if (isMobilephoneNumber(this.phone)) {
        return this.phone?.replace(/(\d{3})\d{4}(\d{4})/, '$1 **** $2')
      } else {
        // 座机从第3位开始隐藏
        return this.phone?.replace(/(\d*?-?\d{2})\d{4}(\d+)/, '$1 **** $2')
      }
    },
    async getPhoneNum() {
      if (this.plainText && typeof this.plainText === 'function') {
        this.phoneNum = await this.plainText()
      } else {
        this.phoneNum = this.phone
      }
    },
    async changeVisibility() {
      if (!this.visibility && !this.phoneNum) {
        await this.getPhoneNum()
      }
      this.visibility = !this.visibility
    },
  },
}
</script>

<style lang="scss" scoped>
.phone-box {
  display: flex;
  align-items: center;

  .eye-icon {
    margin-left: 4px;
    cursor: pointer;
  }
}
</style>
