<template>
  <i class="icon-copy mtdicon-copy-o" @click="contentCopy"></i>
</template>
<script>
import { Message } from '@ss/mtd-vue2'
export default {
  components: {},
  props: ['content'],
  data() {
    return {
      copys: '',
    }
  },
  created() {},
  methods: {
    contentCopy() {
      this.copys = this.content
      navigator.clipboard.writeText(this.copys)
      Message.success('复制成功')
    },
  },
}
</script>
<style lang="scss" scoped>
.icon-copy {
  vertical-align: middle;
  font-size: 20px;
  color: #ccc;
  cursor: pointer;
}
</style>
