<template>
  <div class="content">
    <div class="audio-parts">
      <img class="icon-back" src="@/assets/rewind.svg" alt="后退" @click="backs" />
      <div class="control">
        <div v-if="isPlayState" class="control-detail">
          <img class="icon-play" src="@/assets/play.svg" alt="播放" @click="stateChange" />
        </div>
        <div v-if="!isPlayState" class="control-detail">
          <img class="icon-pause" src="@/assets/pause.svg" alt="暂停" @click="stateChange" />
        </div>
      </div>
      <img class="icon-forward" src="@/assets/forward.svg" alt="快进" @click="forwards" />
    </div>

    <div class="mains">
      <div class="now">{{ audioCurrentTimeMin }}:{{ audioCurrentTimeSec }}</div>
      <div class="progress-container">
        <div class="progress">
          <input
            id="audioRange"
            v-model="currentProgress"
            type="range"
            min="0"
            :max="audioDuration"
            class="progress-bar dynamic-gradient"
            @input="onProgressChange"
          />
        </div>
        <slot name="markers" :audio-duration="audioDuration"></slot>
      </div>
      <div class="speed">
        <div class="total">{{ audioTotalTimeMin }}:{{ audioTotalTimeS }}</div>
        <div class="speed-detail">
          <mtd-picker
            v-model="valueSpeed"
            clearable
            placeholder="x1"
            :options="options"
            class="voiceButton"
            @change="onChanges"
          />
        </div>
      </div>
    </div>

    <mtd-button
      type="text"
      ghost
      class="downloadButton"
      :disabled="!audioAddress"
      @click="downloadVoice"
    >
      <mtd-icon name="mtdicon-download-o" />
    </mtd-button>
  </div>
</template>
<script>
import axios from 'axios'
import { errorHandler } from '@/lib/utils'

export default {
  components: {},
  props: {
    value: {
      type: Number,
      default: 0,
    },
    audioAddress: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      currentTime: 0,
      currentProgress: 0, // 当前进度条位置
      audioDuration: 0, // 音频总时长
      test: 0.5,
      isPlayState: false,
      options: [
        { label: 'x0.5', value: 0.5 },
        { label: 'x1', value: 1 },
        { label: 'x1.25', value: 1.25 },
        { label: 'x1.5', value: 1.5 },
        { label: 'x2', value: 2 },
      ],
      valueSpeed: 1,
      audioCurrentTimeMin: '00',
      audioCurrentTimeSec: '00',
      audioTotalTimeMin: '00',
      audioTotalTimeS: '00',
      audio: null,
    }
  },
  watch: {
    audioAddress: {
      handler(newVal) {
        if (newVal) {
          this.loadAudio()
        } else {
          this.resetAudioData()
        }
      },
      immediate: true,
    },
    value(newValue) {
      this.currentProgress = newValue
      this.audio.currentTime = newValue
    },
    currentProgress(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentTime = newValue
        const [start, end] = this.dataTransfer(this.currentProgress)
        this.audioCurrentTimeMin = start
        this.audioCurrentTimeSec = end
        let currentRate = (100 * newValue) / this.audioDuration
        document.documentElement.style.setProperty('--currentRate', currentRate + '%')
        this.$emit('timeChange', this.currentTime)
      }
    },
    $route() {
      this.audio.pause()
      this.isPlayState = false
    },
  },
  beforeDestroy() {
    this.reset()
  },
  methods: {
    reset() {
      this.currentProgress = 0
      this.audio?.pause()
    },
    dataTransfer(message) {
      let audioCurrentTimeSec = Math.floor(message % 60)
      let audioCurrentTimeMin = Math.floor(message / 60)
      return [
        audioCurrentTimeMin.toString().padStart(2, '0'),
        audioCurrentTimeSec.toString().padStart(2, '0'),
      ]
    },
    onChanges() {
      this.audio.playbackRate = this.valueSpeed
    },
    backs() {
      if (this.audio.currentTime >= 10) {
        this.audio.currentTime = this.audio.currentTime - 10
      } else {
        this.audio.currentTime = 0
      }
    },
    forwards() {
      if (this.audio.currentTime < this.audio.duration - 10) {
        this.audio.currentTime = this.audio.currentTime + 10
      } else {
        this.audio.currentTime = this.audio.duration
      }
    },
    sendMessage() {
      this.$emit('timeChange', this.currentTime)
    },
    onProgressChange() {
      this.audio.currentTime = this.currentProgress
    },
    //下载音频
    async downloadVoice() {
      try {
        const response = await axios({
          url: this.audioAddress,
          method: 'get',
          responseType: 'blob',
        })
        this.$mtd.message.success('音频下载任务已提交')

        const blob = new Blob([response.data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = this.audioAddress.split('/').pop() || 'audio'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } catch (error) {
        //
        this.$mtd.message.error('音频下载失败')
      }
    },

    stateChange() {
      if (this.isPlayState == true) {
        this.isPlayState = false
        this.audio.pause()
      } else {
        this.isPlayState = true
        this.audio.play().catch(error => {
          errorHandler(error)
        })
      }
    },
    loadAudio() {
      this.resetAudioData()
      // 加载音频文件
      this.audio = new Audio(this.audioAddress)

      this.audio.addEventListener('loadedmetadata', () => {
        this.audioDuration = Math.ceil(this.audio.duration)
        this.audioTotalTimeS = this.dataTransfer(this.audioDuration)[1]
        this.audioTotalTimeMin = this.dataTransfer(this.audioDuration)[0]
      })
      this.audio.addEventListener('ended', () => {
        // 音频播放结束暂停播放
        this.stateChange()
      })
      this.audio.addEventListener('timeupdate', this.syncProgress)
    },
    //新传入的音频地址为null时重置时间
    resetAudioData() {
      this.currentProgress = 0

      this.audioDuration = 0
      this.audioTotalTimeS = '00'
      this.audioTotalTimeMin = '00'
      document.documentElement.style.setProperty('--currentRate', '0%')
    },
    syncProgress() {
      // 同步进度条与音频播放进度
      this.currentProgress = parseFloat(this.audio.currentTime.toFixed(2))
    },
  },
}
</script>
<style lang="scss" scoped>
.mtd-picker-selected ::v-deep .mtd-picker-values {
  color: #000;
}

.speed-detail {
  margin-top: 2px;
}

:root {
  --currentRate: 0;
  /* 初始百分比，可以根据需要动态更改 */
}

.dynamic-gradient {
  background: linear-gradient(
    to right,
    var(--color-primary, #f9d247) 0%,
    var(--color-primary, #f9d247) var(--currentRate),
    #dcdddf var(--currentRate),
    #dcdddf 100%
  );
}

.mains {
  display: flex;
  width: 95%;
}

.speed {
  display: flex;
}

.total {
  margin-top: 8px;
  font-size: 14px;
  margin-left: 5px;
  font-weight: 500;
}

.now {
  margin-top: 8px;
  width: 45px;
  margin-left: 18px;
  font-weight: 500;
  font-size: 14px;
}

.icon-forward {
  cursor: pointer;
  margin-left: 5px;
}

.audio-parts {
  display: flex;
}

.control {
  margin-top: 5px;
  margin-left: 0;
  margin-right: 0;
}

.icon-back {
  transform: rotate(180deg);
  margin-left: 10px;
  margin-right: 5px;
  cursor: pointer;
}

.icon-play {
  cursor: pointer;
}

.icon-pause {
  cursor: pointer;
}

.content {
  display: flex;
  border: 1px solid #f7f7f7;
  border-radius: 0 0 5px 5px;
}

.progress {
  width: 100%;
}

.progress-bar {
  margin-top: 18px;
  margin-left: 5px;
  flex: 1;
  width: 99%;
  -webkit-appearance: none;
  border-radius: 1px 1px 1px 1px;
}

input[type='range']::-webkit-slider-runnable-track {
  height: 3px;
  border-radius: 1px 1px 1px 1px;
  z-index: 2;
}

input[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  margin-top: -3px;
  width: 10px;
  height: 10px;
  background: white;
  border: 1.5px solid var(--color-primary, #f9d247);
  border-radius: 50% 50% 50% 50%;
}

.progress-container {
  position: relative;
  width: 100%;
}

::v-deep {
  .mtd-btn {
    padding: 0 0 0 3px;
    justify-content: left;
    align-items: center;
  }
}

.downloadButton {
  margin-top: 3px;
}
</style>
