<template>
  <div class="container" v-bind="$attrs">
    <mtd-tag v-for="(item, index) in tags" :key="index" closable @close="removeTag(index)">
      {{ item }}
    </mtd-tag>
    <mtd-button
      v-show="!editing"
      class="button-new-tag"
      dashed
      size="small"
      icon="add"
      @click="handleAddClick"
      >新标签</mtd-button
    >
    <mtd-input
      v-show="editing"
      ref="input"
      v-model="input"
      class="input-new-tag"
      size="small"
      @keyup.enter="createTag"
      @blur="handleBlur"
    />
  </div>
</template>
<script>
export default {
  props: {
    modelValue: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      input: '',
      editing: false,
    }
  },
  methods: {
    handleAddClick() {
      this.editing = true
      this.$nextTick(_ => {
        this.$refs.input.focus()
      })
    },
    createTag() {
      if (!this.input) return

      this.tags.push(this.input)
      this.input = ''
      this.editing = false
    },
    removeTag(index) {
      this.$emit(
        'change',
        this.tags.filter((_, i) => i !== index),
      )
    },
    handleBlur() {
      if (!this.input) {
        this.editing = false
      }
    },
  },
}
</script>
<style lang="scss">
.input-new-tag {
  width: 78px;
  height: 24px;
  margin-left: 40px;
  .mtd-input {
    border-style: dashed;
  }
}
.button-new-tag {
  margin-left: 40px;
  height: 24px;
  > span {
    line-height: 22px;
  }
}
</style>
