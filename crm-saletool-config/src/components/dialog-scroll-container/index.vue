<template>
  <div class="dialog-scroll-container">
    <div ref="scrollContent" class="dialogue-box" :class="{ 'with-time-axis': showAudioPlayer }">
      <div v-if="dataSource.length === 0" class="dialogue-empty-box">
        <img class="empty-icon" :src="emptyIcon" alt="" />
        <span class="empty-text">暂无通话记录</span>
      </div>
      <div v-else>
        <div
          v-for="item in dataSource"
          :key="item.sequenceId"
          :ref="`dialogContent${item.sequenceId}`"
          class="dialogue-item"
          :class="{
            reverse: item.role !== leftAlignedRole,
            active: item.sequenceId === currentActiveSequenceId,
          }"
          @dblclick="currentTime = item.startTime / 1000"
        >
          <slot name="sentence" :item="item"></slot>
        </div>
      </div>
    </div>
    <AudioPlayer
      v-if="showAudioPlayer"
      class="audio-player"
      :audio-address="audioAddress"
      :value.sync="currentTime"
      @timeChange="scrollIntoView"
    >
      <template #markers="{ audioDuration }">
        <slot name="markers" :audio-duration="audioDuration"></slot>
      </template>
      <template #voiceDownload>
        <slot name="voiceDownload"></slot>
      </template>
    </AudioPlayer>
  </div>
</template>

<script>
import { AudioPlayer } from '@/components'
import emptyIcon from '@/assets/empty-icon.png'

export default {
  name: 'DialogScrollContainer',
  components: {
    AudioPlayer,
  },
  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    leftAlignedRole: {
      type: Number,
      required: true,
    },
    audioAddress: {
      type: String,
      default: '',
    },
    sequenceId: {
      type: Number,
      default: 0,
    },
    showAudioPlayer: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      currentActiveSequenceId: this.sequenceId || null,
      currentTime: 0,
      emptyIcon, //传递数据为空时的icon
    }
  },
  watch: {
    currentActiveSequenceId(newValue) {
      this.$emit('update:sequence-id', newValue)
    },
    sequenceId: {
      handler: 'handleSequenceIdChange',
      immediate: true,
    },
    dataSource: {
      handler: 'handleSequenceIdChange',
      immediate: true,
    },
  },
  methods: {
    handleSequenceIdChange() {
      if (this.sequenceId && this.dataSource.length > 0) {
        const activeItem = this.dataSource.find(item => item.sequenceId === this.sequenceId)
        if (activeItem) {
          this.currentTime = activeItem.startTime / 1000
        }
      }
    },
    scrollIntoView(value) {
      const currentTime = value
      //检查当前播放时间是否在当前播放的sequenceId的时间范围内。
      const inTimeRange = item => {
        return currentTime >= item.startTime / 1000 && currentTime <= item.endTime / 1000
      }

      // 如果在当前播放的sequenceId的时间范围内，就不更新currentActiveSequenceId
      const currentActiveItem = this.dataSource.find(
        item => item.sequenceId === this.currentActiveSequenceId,
      )
      if (currentActiveItem && inTimeRange(currentActiveItem)) {
        return
      }
      // 如果不在当前播放的范围内，查找匹配项来更新currentActiveSequenceId
      const messageElement = this.dataSource.find(message => inTimeRange(message))
      if (messageElement && messageElement.sequenceId !== this.currentActiveSequenceId) {
        this.currentActiveSequenceId = messageElement.sequenceId
      }

      if (messageElement) {
        const element = this.$refs[`dialogContent${messageElement.sequenceId}`][0]
        if (element) {
          const parentNode = this.$refs.scrollContent
          const parentHeight = parentNode.clientHeight
          const elementHeight = element.clientHeight
          const scrollTop =
            element.offsetTop - parentNode.offsetTop - parentHeight / 2 + elementHeight / 2
          parentNode.scrollTo({
            top: scrollTop,
            behavior: 'smooth',
          })
        }
      } else {
        this.currentActiveSequenceId = null
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-scroll-container {
  max-height: calc(100vh - 155px);
  display: flex;
  flex-direction: column;

  .audio-player {
    border-top: 0;
    border-color: #e5e5e5;
  }

  .dialogue-box {
    flex: 1;
    overflow: auto;
    padding: 16px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    background: #f8f8f8;
    transition: all 0.3s ease-in-out;

    &.with-time-axis {
      border-bottom: 0;
      border-radius: 8px 8px 0 0;
    }

    .dialogue-empty-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      text-align: center;

      .empty-icon {
        width: 120px;
        height: 105px;
      }

      .empty-text {
        color: #999999;
        margin-top: 10px;
      }
    }
  }

  .dialogue-item {
    margin-bottom: 16px;

    &.active {
      .chat-content .content {
        border: 1px solid var(--chat-content-border, #166ff7);
      }
    }

    .timeline {
      margin-left: 45px;
      text-align: left;
      color: #999;
      font-size: 12px;
    }

    .chat-content {
      margin-bottom: 8px;
      display: flex;
      gap: 8px;

      $chat-content-width: 70%;
      $chat-content-margin: calc(100% - $chat-content-width);

      &.left-align {
        margin-right: $chat-content-margin;
      }

      &.right-align {
        margin-left: $chat-content-margin;
      }
    }

    &.reverse {
      .chat-content {
        flex-direction: row-reverse;
      }
      .timeline {
        margin-right: 45px;
        text-align: right;
      }
    }
  }
}
</style>
