const isProduction = process.env.VUE_APP_ENV === 'production'
// 新版阿波罗重域名
const hostname = isProduction ? '//apollo.meituan.com' : '//apollo.nibcrm.test.sankuai.com'
// 新版阿波罗重定向的地址
export const REDIRECT_URL = `${hostname}/crm-platform/?iurl=${encodeURIComponent(
  btoa(encodeURIComponent(location.href)),
)}`
// 新版阿波罗后台重定向的地址
export const REDIRECT_URL_OM = `${hostname}/crm-om/?iurl=${encodeURIComponent(
  btoa(encodeURIComponent(location.href)),
)}`

export const BIZ_LINE = {
  ZONG: 1007,
}

export const POI_TYPE = {
  MEITUAN: 1,
  D_PLUS: 2,
}

export const ADDRESS_TYPE = {
  PROVINCE: 0,
  CITY: 1,
  COUNTY: 2,
}

export const SHOP_STATUS_OPTIONS = [
  {
    value: 0,
    label: '门店在线，可比',
  },
  {
    value: 1,
    label: '门店在线但违规，不可比',
  },
  {
    value: 2,
    label: '门店不在线，不可比',
  },
  {
    value: 4,
    label: '其他原因，不可比',
  },
]

export const MT_SHOP_STATUS_OPTIONS = [
  {
    value: 0,
    label: '门店不在线（永久关闭/违规侵权）',
  },
  {
    value: 1,
    label: '门店在线（正常营业/门店暂停营业/尚未营业）',
  },
  {
    value: 2,
    label: '其他',
  },
]

export const DEAL_STATUS_OPTIONS = [
  {
    value: 0,
    label: '行业deal状态正常，可比',
  },
  {
    value: 1,
    label: '行业deal不在线，不可比',
  },
  {
    value: 2,
    label: '行业deal状态其他类型，不可比',
  },
]

export const MT_PRODUCT_TYPE = new Map([
  [1, '团购'],
  [2, '泛商品'],
  [3, '标品'],
])

export const BOOK_STATUS_OPTIONS = [
  {
    label: '共有商品价平',
    value: 1,
  },
  {
    label: '共有商品价优',
    value: 2,
  },
  {
    label: '其他',
    value: 0,
  },
]

export const MT_DEAL_STATUS_OPTIONS = [
  {
    value: 0,
    label: '我方商品不在线，不可比',
  },
  {
    value: 1,
    label: '我方商品状态正常，可比',
  },
  {
    value: 2,
    label: '其他原因，不可比',
  },
]

export const PRODUCT_TYPE = {
  HIGH_SALE: 1,
  LOW_SALE: 2,
}

export const DEFALUT_UNIT = ['单位', '个', '分钟', '元', '天', '次']

export const SPECIAL_CAT2_UNIT = [
  {
    cat2Ids: [54, 740],
    units: ['个'],
  },
  {
    cat2Ids: [48, 2635],
    units: ['分钟'],
  },
  {
    cat2Ids: [263, 380],
    units: ['元'],
  },
  {
    cat2Ids: [91],
    units: ['天', '次', '元'],
  },
]

export const MANUAL_ANNOTATION_TYPE = {
  CANNOT_JUDGE: 1, // 无法判断
  SUBMIT: 2, // 正常提交
  NO_HANDLE: 4, // 暂不处理
}

export const INSPECRTION_RESULT = {
  PASS: 1, // 通过
  NOT_PASS: 2, // 不通过
}

export const ANNOTATION_STATUS = {
  TODO: 1,
  FINISH: 2,
  QUESTION: 4,
  EXPIRE: 8,
}

export const ANNOTATION_STATUS_LABEL_ENUMS = {
  [ANNOTATION_STATUS.TODO]: '待标注',
  [ANNOTATION_STATUS.FINISH]: '已标注',
  [ANNOTATION_STATUS.QUESTION]: '无法判断',
  [ANNOTATION_STATUS.EXPIRE]: '标注过期',
}

export const INSPECRTION_STATUS = {
  INITIAL: 1,
  NONEED: 2,
  NOPICK: 4,
  TODO: 8,
  PASS: 16,
  REJECT: 32,
  EXPIRE: 64,
}

export const INSPECRTION_STATUS_LABEL_ENUMS = {
  [INSPECRTION_STATUS.INITIAL]: '未到送检时间',
  [INSPECRTION_STATUS.NONEED]: '无需质检',
  [INSPECRTION_STATUS.NOPICK]: '未抽中质检',
  [INSPECRTION_STATUS.TODO]: '待质检',
  [INSPECRTION_STATUS.PASS]: '质检通过',
  [INSPECRTION_STATUS.REJECT]: '质检不通过',
  [INSPECRTION_STATUS.EXPIRE]: '质检过期',
}

/**
 * 测评类型枚举
 */
export const EVALUATION_TYPE_MAP = {
  3: '训练集',
  4: '测评集',
}

export const SOP_TYPE_ENUMS = {
  PARITY: 3, // 平价
  CHASE_ORDER: 4, // 追单
}

export const SOURCE_ENUMS = {
  MANUAL_UPLOAD: 0,
  OFFLINE: 50,
  REAL_TIME: 100,
  MAKE_UP: 158,
  MAKE_UP_T1_A: 150,
  MAKE_UP_T1_B: 151,
  MAKE_UP_T1_C: 152,
  MAKE_UP_T1_D: 153,
  MAKE_UP_T2_A: 154,
  MAKE_UP_T2_B: 155,
  MAKE_UP_T2_C: 156,
  MAKE_UP_T2_D: 157,
  ALGORITHM_EVALUATION: 2,
}

export const SOURCE_LABEL_ENUMS = {
  [SOURCE_ENUMS.MANUAL_UPLOAD]: '手动上传',
  [SOURCE_ENUMS.OFFLINE]: '离线',
  [SOURCE_ENUMS.REAL_TIME]: '实时',
  [SOURCE_ENUMS.MAKE_UP]: '补单',
  [SOURCE_ENUMS.MAKE_UP_T1_A]: 'T1_1',
  [SOURCE_ENUMS.MAKE_UP_T1_B]: 'T1_2',
  [SOURCE_ENUMS.MAKE_UP_T1_C]: 'T1_3',
  [SOURCE_ENUMS.MAKE_UP_T1_D]: 'T1_4',
  [SOURCE_ENUMS.MAKE_UP_T2_A]: 'T2_1',
  [SOURCE_ENUMS.MAKE_UP_T2_B]: 'T2_2',
  [SOURCE_ENUMS.MAKE_UP_T2_C]: 'T2_3',
  [SOURCE_ENUMS.MAKE_UP_T2_D]: 'T2_4',
  [SOURCE_ENUMS.ALGORITHM_EVALUATION]: '算法测评-测评',
}

export const MATCHSOURCE_ENUMS = {
  ACCURATE_MATCH: 1,
  FUZZY_MATCH: 2,
  WHITELIST_MATCH: 3,
  REALTIME_MATCH: 4,
  BLACKLIST_MATCH: 5,
  SYSTEM_MATCH: -1,
  ALGORITHMIC_EVALUATION: -999,
  MANUALUPLOAD: 11,
  APPEAL: 12,
  CHASE: 13,
  ANNOTATION_CHANGE: 14,
  ANNOTATION_NOT_CH: 15,
  REPORT_ERROR: 16,
}

// 匹配关系来源枚举
export const MATCHSOURCE_LABEL_ENUMS = {
  [MATCHSOURCE_ENUMS.ACCURATE_MATCH]: '精准匹配',
  [MATCHSOURCE_ENUMS.FUZZY_MATCH]: '模糊匹配',
  [MATCHSOURCE_ENUMS.WHITELIST_MATCH]: '白名单',
  [MATCHSOURCE_ENUMS.REALTIME_MATCH]: '实时匹配',
  [MATCHSOURCE_ENUMS.BLACKLIST_MATCH]: '黑名单',
  [MATCHSOURCE_ENUMS.SYSTEM_MATCH]: '系统匹配',
  [MATCHSOURCE_ENUMS.ALGORITHMIC_EVALUATION]: '算法测评链路',
  [MATCHSOURCE_ENUMS.MANUALUPLOAD]: '人工提报',
  [MATCHSOURCE_ENUMS.APPEAL]: '销售申诉',
  [MATCHSOURCE_ENUMS.CHASE]: '一键追单',
  [MATCHSOURCE_ENUMS.ANNOTATION_CHANGE]: '人工匹配',
  [MATCHSOURCE_ENUMS.ANNOTATION_NOT_CH]: '人工匹配',
  [MATCHSOURCE_ENUMS.REPORT_ERROR]: '销售报错',
}
