export function findTreeNode(nodes, value) {
  return nodes.find(node => node.value == value)
}
export function buildTreeNode(datas, node) {
  if (node.childNode == null) {
    return {
      label: node.label,
      value: node.value,
      isLeaf: node.isRootNode,
      children: null,
    }
  }
  return {
    label: node.label,
    value: node.value,
    isLeaf: node.isRootNode,
    children: node.childNode.map(childValue =>
      buildTreeNode(datas, findTreeNode(datas, childValue)),
    ),
  }
}

export function getNodes(valuesList) {
  return (valuesList || []).map(values => values[values.length - 1])
}

export function changeOrg(datas) {
  let listOrg = []
  let dataContent = datas[0]
  while (true) {
    listOrg.push(dataContent.value)
    if (dataContent.children == null) {
      break
    }
    dataContent = dataContent.children[0]
  }
  return [listOrg]
}
