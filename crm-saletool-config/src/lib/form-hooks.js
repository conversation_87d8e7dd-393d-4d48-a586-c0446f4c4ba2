import { onFieldInit, onFieldValueChange, onFormInit, onFormReset } from '@nibfe/dz-form-core'

/**
 * @param {(form: import('@nibfe/dz-form-core').Form) => void} callback
 */
export function onFormInitAndReset(callback) {
  onFormReset(callback)
  onFormInit(callback)
}

/**
 * @param {import('@nibfe/dz-form-core').FormPathPattern} pattern
 * @param {
 *    (
 *      field: import('@nibfe/dz-form-core').GeneralField,
 *      form: import('@nibfe/dz-form-core').Form
 *    )=>void
 * } callback
 */
export function onFieldInitAndValueChange(pattern, callback) {
  onFieldInit(pattern, callback)
  onFieldValueChange(pattern, callback)
}
