import { merge } from 'lodash'
import { reportError } from '@/lib/utils'

/**
 * 对常用灵犀方法的封装
 *
 * 灵犀 API 详见官方文档 https://sky.sankuai.com/docs/data_fe/lx-doc/pages/web5/
 */
export class LXUtils {
  /**
   * @param {string} 通道名称
   * @param {{
   *  cid?: string
   *  valLab?: any,
   *  appnm?: string,
   *  environment?: any,
   *  valLabGenerator?: () => any
   * }} config
   */
  constructor(category, config = {}) {
    this.tracker = window.LXAnalytics && window.LXAnalytics('getTracker', category)

    const { cid, valLab, environment, valLabGenerator, appnm } = config

    appnm && this.tracker?.('set', 'appnm', appnm)

    this.valLabGenerator = valLabGenerator ?? (() => ({}))

    let mergedValLab = valLab ?? {}

    // valLab 经常出现需要异步获取信息的场景，因此这里 pagecase 的创建使用了异步逻辑
    // 具体用法可以参考项目内其他位置的实现
    ;(async () => {
      try {
        const baseValLab = await this.valLabGenerator()
        mergedValLab = merge(valLab ?? {}, baseValLab)
      } catch (error) {
        reportError(error)
      } finally {
        this.pagecase =
          cid === undefined ? undefined : this.tracker?.('pageView', mergedValLab, environment, cid)
      }
    })()
  }

  async mv(bid, valLab = {}, options = {}) {
    try {
      const baseValLab = await this.valLabGenerator(bid, valLab)
      const mergedValLab = merge(valLab, baseValLab)
      this.pagecase?.('moduleView', bid, mergedValLab, options)
    } catch (error) {
      reportError(error)
    }
  }

  async mc(bid, valLab = {}) {
    try {
      const baseValLab = await this.valLabGenerator(bid, valLab)
      const mergedValLab = merge(valLab, baseValLab)
      this.pagecase?.('moduleClick', bid, mergedValLab)
    } catch (error) {
      reportError(error)
    }
  }
}
