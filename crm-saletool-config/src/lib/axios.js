/**
 * Axios Instance
 */

import axios from 'axios'

/**
 * @see https://github.com/axios/axios#request-config
 */

const REMOTE_URL_HOST = process.env.VUE_APP_API_HOST || ''
// const REMOTE_URL_HOST = '//yapi.sankuai.com/thrift/mock/project/45358'

const config = {
  baseURL: REMOTE_URL_HOST,
  timeout: Number(process.env.VUE_APP_HTTP_TIMEOUT),
  withCredentials: true,
}

const instance = axios.create(config)

export default instance
