import { Message } from '@ss/mtd-vue2'
import Big from 'big.js'
import qs from 'querystringify'
import psb from '@nibfe/platform-sdk'
import { cloneDeep } from 'lodash'

/**
 *
 * @param {{
 *  error: Error
 *  level?: 'error' | 'warn'
 *  category?: 'jsError' | 'apiError'
 *  content?: Record<string, any>
 * }} params
 * @returns
 */

export function reportError(params) {
  try {
    const { error, level, category, content } = params

    const options = {
      level: level || 'error',
      category: category || 'jsError',
      tags: content || {},
    }

    // v1.10.0+
    if (window.owl) {
      window.owl('addError', error, options)
      return
    }

    // v1.8.x
    // v1.9.x
    if (window.Owl) {
      window.Owl.addError(error, options)
      return
    }
  } catch (error) {
    // owl 上报失败，理论上不会出现，这里仅作兜底，如有需要增加其他逻辑
    throw new Error(error)
  }
}

/**
 * 仅上报 error，不继续抛出异常或者显示错误信息
 */
export function errorReportOnly(error) {
  reportError({ error })
}

/**
 * 默认异常处理函数
 *
 * @param {any} error
 * @param {ErrorHandlerOptions} options
 */
export function errorHandler(error, options = {}) {
  const { display = true } = options

  reportError({
    error,
  })

  if (display) {
    const messgae = error instanceof Error ? error.message : error

    Message({
      type: 'error',
      message: messgae,
      showClose: true,
    })
  }
}

/**
 * 定制异常处理函数
 *
 * @param {((error: any) => string | number) | string | number} messageGenerator 自定义消息生成器
 * @param {ErrorHandlerOptions} options 其他配置项
 *
 * @returns {(error: any) => void} 异常处理函数
 */
export function errorHandlerGenerator(messageGenerator, options = {}) {
  if (typeof messageGenerator === 'function') {
    return error => errorHandler(messageGenerator(error))
  }

  if (typeof messageGenerator === 'string' || typeof messageGenerator === 'number') {
    return error =>
      errorHandler(
        `${messageGenerator}：${error instanceof Error ? error.message : error}`,
        options,
      )
  }

  return error => errorHandler(error, options)
}

/**
 * 获取 search 参数
 */
export function getUrlSearch() {
  try {
    const index = location.href.indexOf('?')
    if (index == -1) return {}
    const search = location.href.slice(index + 1)
    return qs.parse(search) || {}
  } catch (error) {
    return {}
  }
}

/**
 * try-catch包裹JSON.parse
 */
export const safeJSONParse = (str, defaultValue = null) => {
  try {
    return JSON.parse(str)
  } catch (e) {
    return defaultValue
  }
}

// 阿波罗新框架中通用的跳转
export function jumpUrl({ url = '', target = '_self', platform = 'platform' }) {
  if (!url) return
  psb.invoke('crm.jump', {
    url,
    target,
    platform,
  })
}

/**
 * 适配不同环境的通用跳转函数
 */
export function jumpUrlByEnv({
  path = '',
  autoEnvAdapt = true,
  target = '_self',
  platform = 'platform',
}) {
  const devOpsPathName = 'crm-saletool-config'
  const isLocalEnv = !/apollo\./.test(location.host)
  const isNeedAdaptURL = autoEnvAdapt && !isLocalEnv
  const finalURL = `${isNeedAdaptURL ? `/${devOpsPathName}` : ''}/${path}`

  if (isLocalEnv) {
    window.open(finalURL, target === '_parent' ? '_blank' : '_self')
  } else {
    jumpUrl({ url: finalURL, target, platform })
  }
}

// 主框架关闭当前页面
export function closePage() {
  psb.invoke('closePage')
}

// poiId脱敏
export function encryptPoiId(poiId) {
  let poiIdStr = String(poiId)
  return poiId
    ? `${poiIdStr[0]} ${'*'.repeat(Math.max(poiIdStr.length - 2, 0))}${
        poiIdStr[poiIdStr.length - 1]
      }`
    : ''
}

/**
 * 对数组进行原地冒泡排序，会直接对传入的数组进行操作
 *
 * @template T
 *
 * @param {T[]} arr
 * @param {(a:T, b:T)=>number} compareFn
 */
export function bubbleSort(arr, compareFn) {
  let i = arr.length - 1

  while (i > 0) {
    let pos = 0 // 用于记录最后一次交换的位置

    for (let j = 0; j < i; ++j) {
      if (compareFn(arr[j], arr[j + 1]) > 0) {
        const temp = arr[j]
        arr[j] = arr[j + 1]
        arr[j + 1] = temp

        pos = j // 记录交换的位置
      }
    }

    i = pos // 下一轮排序只需要比较到 pos 位置即可
  }

  return arr
}

/**
 * 根据指定的字段优先级对数组项进行排序
 *
 * @template T 数组项的类型
 *
 * @param {T[]} dataSource 待排序的数组
 * @param {(keyof T)[]} keys 用于排序的字段及优先级
 * @param {boolean} inplace 若为 true，则直接对传入的数组进行原地排序，否则进行深拷贝后再进行排序
 * @returns
 *
 * @example
 * ``` javascript
 * console.log(arrangeOrderByKeys([
 *   {
 *      a: 'a-1',
 *      b: 'b-2',
 *      c: 'some other value'
 *   },
 *   {
 *      a: 'a-2',
 *      b: 'b-2',
 *      c: 'some other value'
 *   },
 *   {
 *      a: 'a-1',
 *      b: 'b-3',
 *      c: 'some other value'
 *   },
 *   {
 *      a: 'a-2',
 *      b: 'b-1',
 *      c: 'some other value'
 *   },
 *   {
 *      a: 'a-1',
 *      b: 'b-1',
 *      c: 'some other value'
 *   },
 * ], ['a', 'b']))
 *
 * // 输出结果如下
 * [
 *  {a: 'a-1', b: 'b-1', c: '...'},
 *  {a: 'a-1', b: 'b-2', c: '...'},
 *  {a: 'a-1', b: 'b-3', c: '...'},
 *  {a: 'a-2', b: 'b-1', c: '...'},
 *  {a: 'a-2', b: 'b-2', c: '...'},
 * ]
 * ```
 */
export function arrangeOrderByKeys(dataSource, keys, inplace = false) {
  const clone = inplace ? dataSource : cloneDeep(dataSource)

  const backwardKeys = Array.from(keys).reverse()

  backwardKeys.forEach(key =>
    bubbleSort(clone, (a, b) => {
      const x = (a[key] ?? '').toString()
      const y = (b[key] ?? '').toString()

      return x.localeCompare(y)
    }),
  )

  return clone
}

/**
 * 根据指定字段的优先级计算相关单元格的 rowspan 结果并回填至对应字段
 * 适用于「表格开头的若干列需要合并单元格」的情形
 * 需要和 mtd-table 的 row-col-span 属性结合使用
 *
 * @template T
 *
 * @param {T[]} dataSource
 * @param {(keyof T)[]} keys
 *
 * @returns {(T & {__rowspan__: {[number]: number}})}
 */
export function calcRowSpanOfKeys(dataSource, keys) {
  /**
   * @param {T[]} arrSlice
   * @param {number} keyIndex
   */
  const calcRowSpan = (arrSlice, keyIndex) => {
    if (arrSlice.length === 0) return

    arrSlice.forEach(item => {
      if (!item.__rowspan__) item.__rowspan__ = {}
      item.__rowspan__[keyIndex] = 0
    })

    arrSlice[0].__rowspan__[keyIndex] = arrSlice.length
  }

  keys.forEach((key, index) => {
    let start = 0,
      end = 0

    while (start < dataSource.length && end <= dataSource.length) {
      if (end === dataSource.length || dataSource[start][key] !== dataSource[end][key]) {
        calcRowSpan(dataSource.slice(start, end), index)
        start = end
      } else {
        ++end
      }
    }
  })

  return dataSource
}
// bigjs 解决浮点数精度丢失问题
export const arithmetic = {
  // 除法
  division(a = 0, b = 0) {
    return new Big(a).div(b).toNumber()
  },
  // 加法
  add(a = 0, b = 0) {
    return new Big(a).plus(b).toNumber()
  },
  // 乘法
  multiple(a = 0, b = 0) {
    return new Big(a).times(b).toNumber()
  },
  // 减法
  reduce(a = 0, b = 0) {
    return new Big(a).minus(b).toNumber()
  },
}

export function copyText(text) {
  if (navigator.clipboard) {
    // clipboard api 复制
    navigator.clipboard.writeText(text)
  } else {
    var textarea = document.createElement('textarea')
    document.body.appendChild(textarea)
    // 隐藏此输入框
    textarea.style.position = 'fixed'
    textarea.style.clip = 'rect(0 0 0 0)'
    textarea.style.top = '10px'
    // 赋值
    textarea.value = text
    // 选中
    textarea.select()
    // 复制
    document.execCommand('copy', true)
    // 移除输入框
    document.body.removeChild(textarea)
  }
}

// 获取当前环境（阿波罗/盘古）
export function getEnv() {
  const isLocal = /local/.test(location.hostname)
  const isBeta = isLocal || /test|51ping/.test(location.hostname)
  const isPanguPlatform = /meishi|crm.sankuai.com/.test(location.hostname)
  const isApolloPlatform = !isPanguPlatform && /dper|apollo/.test(location.hostname)

  return {
    isLocal,
    isBeta,
    isPanguPlatform,
    isApolloPlatform,
  }
}
