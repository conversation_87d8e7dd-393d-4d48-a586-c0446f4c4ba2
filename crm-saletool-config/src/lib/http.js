/**
 * 在这里设置 http拦截器
 */

import instance from './axios'
import { reportError } from './utils'
import { Message } from '@ss/mtd-vue2'

// Add request interceptor
instance.interceptors.request.use(
  // Do something before request is sent
  config => {
    return config
  },
  // Do something with request error
  error => {
    return Promise.reject(error)
  },
)

// Add response interceptor
instance.interceptors.response.use(
  // Do something with response data
  response => {
    return response.data
  },
  // Do something with response error
  error => {
    return Promise.reject(error)
  },
)

export default function http(
  url,
  { method = 'get', data = {}, entire = false },
  needThrowError = true,
) {
  const isGet = method.toLowerCase() === 'get'
  const datas = data
  return instance({
    url,
    method,
    params: isGet ? datas : null,
    data: isGet ? null : datas,
    headers: isGet ? {} : { 'Content-Type': 'application/json' },
  })
    .then(res => {
      // 返回请求的全部内容
      if (entire) {
        return res
      }
      // 没有特殊需求直接返回数据部分
      if (res && res.code === 200) {
        return res.data
      } else {
        Message({
          message: res.msg || res.errorMsg || '请求失败',
          type: 'error',
          dangerouslyUseHTMLString: true,
        })
        if (needThrowError) {
          throw res.msg || res.errorMsg || '请求失败'
        }
      }
    })
    .catch(e => {
      reportError({
        error: '请求异常',
        content: {
          params: data,
          response: e,
        },
      })
      throw e
    })
}
