import http from '@/lib/http'
import { BIZ_LINE, POI_TYPE } from '@/lib/constants'

export default {
  //用户角色查询
  queryUserRole(data) {
    return http('/gateway/bml/guiguzi/annotation/UserTService/queryUserRole', {
      method: 'post',
      data,
    })
  },

  //查询地址列表
  queryAddress(data) {
    return http('/gateway/bml/guiguzi/annotation/CommonTService/queryAddress', {
      method: 'POST',
      data,
    })
  },

  //门店搜索
  searchPoiByCondition(data) {
    return http('/gateway/bml/guiguzi/annotation/CommonTService/queryPoi', {
      method: 'post',
      data: {
        bizLine: BIZ_LINE.ZONG,
        poiType: POI_TYPE.MEITUAN,
        ...data,
      },
    })
  },

  //查询美团门店&deal详情
  queryMtPoiDealDetail(data) {
    return http('/gateway/bml/guiguzi/annotation/CommonTService/queryMtPoiDealDetail', {
      method: 'post',
      data,
    })
  },

  //查询竞对门店&deal详情
  queryComPoiDealDetail(data) {
    return http(
      '/gateway/bml/guiguzi/annotation/ManualAnnotationTService/queryComPoiAndProductList',
      {
        method: 'post',
        data,
      },
    )
  },

  // 获取mt-poi下所有发单的deal
  queryMtPoiDealSopInfo(data) {
    return http('/gateway/bml/guiguzi/annotation/CommonTService/queryMtPoiDealSopInfo', {
      method: 'post',
      data,
    })
  },

  //标注详情查询
  queryManualAnnotationDetail(data) {
    return http('/gateway/bml/guiguzi/annotation/ManualAnnotationTService/queryDetail', {
      data,
      method: 'POST',
    })
  },

  //无匹配关系高销团单接口
  queryNoMatchingRelationHighSaleProductList(data) {
    return http('/gateway/bml/guiguzi/annotation/ManualAnnotationTService/queryFullHighSellingDeal', {
      data,
      method: 'POST',
    })
  },

  //质检详情查询
  queryQualityInspectionDetail(data) {
    return http('/gateway/bml/guiguzi/annotation/QualityInspectionTService/queryDetail', {
      data,
      method: 'POST',
    })
  },

  //标注结果保存
  saveManualAnnotationThriftService(data) {
    return http('/gateway/bml/guiguzi/annotation/ManualAnnotationTService/save', {
      data,
      method: 'POST',
      entire: true,
    })
  },

  //质检结果保存
  saveQualityInspectionThriftService(data) {
    return http('/gateway/bml/guiguzi/annotation/QualityInspectionTService/save', {
      data,
      method: 'POST',
      entire: true,
    })
  },
  // 质检备注查询
  queryInspectionRemarkTree(data) {
    return http(
      '/gateway/bml/guiguzi/annotation/QualityInspectionTService/queryInspectionRemarkTree',
      {
        data,
        method: 'POST',
        entire: true,
      },
    )
  },
  // 操作日志记录条数
  queryCount(data) {
    return http('/gateway/bml/guiguzi/annotation/AnnInsOpLogTService/queryCount', {
      data,
      method: 'POST',
      entire: true,
    })
  },
}
