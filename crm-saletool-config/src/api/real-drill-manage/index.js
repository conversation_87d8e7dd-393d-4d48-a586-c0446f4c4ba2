import http from '@/lib/http'
import axios from "@/lib/axios";

export default {
  //基础信息查询
  queryBaseInformation(data) {
    return axios('/gateway/sk/drill/real/queryDetailBaseInfo', {
      method: 'post',
      data,
    })
  },
  queryDrillInformation(data) {
    return axios('/gateway/sk/drill/real/queryDrillRealDialog', {
      method: 'post',
      data
    })
  },
  queryMentorRate(data) {
    return axios('/gateway/sk/drill/real/queryEvaluationFromManager', {
      method: 'post',
      data,
    })
  },
  queryAudio(data) {
    return axios('/gateway/sk/drill/real/queryDialogVoiceUrl', {
      method: 'post',
      data,
    })
  },
  queryMasterRate(data) {
    return axios('/gateway/sk/drill/real/queryEvaluationFromMentor', {
      method: 'post',
      data,
    })
  },
  editManagerRate(data) {
    return axios('/gateway/sk/drill/real/commitEvaluationFromManager', {
      method: 'post',
      data,
    })
  },
  editMentorRate(data) {
    return axios('/gateway/sk/drill/real/commitEvaluationFromMentor', {
      method: 'post',
      data,
    })
  },
}
