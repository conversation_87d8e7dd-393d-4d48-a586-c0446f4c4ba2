{"componentName": "Page", "id": "node_dockcviv8fo1", "props": {}, "docId": "doclluni9wa", "fileName": "/", "dataSource": {"list": [{"options": {"headers": {"swimlane": "", "Content-Type": "application/json"}, "params": {"bizLine": 1007}, "method": "POST", "isCors": true, "timeout": 5000, "uriPath": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/changeUserStatus`"}, "uri": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/changeUserStatus`"}}, "type": "fetch", "source": "NORMAL_API", "isInit": false, "isAutoHost": false, "id": "changeUserStatus", "desp": "(批量)修改用户工作状态"}, {"options": {"headers": {"swimlane": "", "Content-Type": "application/json"}, "params": {"bizLine": 1007}, "method": "POST", "isCors": true, "timeout": 5000, "uriPath": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/deleteUser`"}, "uri": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/deleteUser`"}}, "type": "fetch", "source": "NORMAL_API", "isInit": false, "isAutoHost": false, "id": "deleteUser", "desp": "(批量)删除用户"}, {"options": {"headers": {"swimlane": "", "Content-Type": "application/json"}, "params": {"groupKey": 2, "bizLine": 1007}, "method": "POST", "isCors": true, "timeout": 5000, "uriPath": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/replaceUser`"}, "uri": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/replaceUser`"}}, "type": "fetch", "source": "NORMAL_API", "isInit": false, "isAutoHost": false, "id": "replaceUser", "desp": "新增/修改用户"}, {"options": {"headers": {"swimlane": "", "Content-Type": "application/json"}, "params": {"bizLine": 1007}, "method": "POST", "isCors": true, "timeout": 5000, "uriPath": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/InspectionTService/queryInspectionConfig`"}, "uri": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/InspectionTService/queryInspectionConfig`"}}, "type": "fetch", "source": "NORMAL_API", "isInit": false, "isAutoHost": false, "id": "queryInspectionConfig", "desp": "查询质检配置"}, {"options": {"headers": {"swimlane": "", "Content-Type": "application/json"}, "params": {"bizLine": 1007, "inspectionConfigType": 1}, "method": "POST", "isCors": true, "timeout": 5000, "uriPath": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/InspectionTService/replaceInspectionConfig`"}, "uri": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/InspectionTService/replaceInspectionConfig`"}}, "type": "fetch", "source": "NORMAL_API", "isInit": false, "isAutoHost": false, "id": "replaceInspectionConfig", "desp": "新增/修改质检配置"}]}, "state": {"HOST": {"type": "JSExpression", "value": "''"}, "INSPECTIONRECORDPATH": {"type": "JSExpression", "value": "''"}, "envParams": {"type": "JSExpression", "value": "[{\n  \"name\": \"HOST\",\n  \"value\": {\n    \"development\": \"//apollo.nibcrm.test.sankuai.com\",\n    \"test\": \"//apollo.nibcrm.test.sankuai.com\",\n    \"production\": \"//apollo.meituan.com\",\n    \"mock\": \"\"\n  },\n  \"description\": \"域名\"\n}, {\n  \"name\": \"INSPECTIONRECORDPATH\",\n  \"value\": {\n    \"development\": \"/inspection-record/index.html#/\",\n    \"test\": \"/crm-saletool-config/inspection-record/index.html#/\",\n    \"production\": \"/crm-saletool-config/inspection-record/index.html#/\",\n    \"mock\": \"\"\n  }\n}]"}, "type": {"type": "JSExpression", "value": "\"inspection\""}, "searchFilter": {"type": "JSExpression", "value": "{\n  \"groupKey\": this.utils.getPageConstant('GROUPKEY').inspection,\n  \"bizLine\": this.utils.getPageConstant('BIZLINE').general\n}"}, "tableModel": {"type": "JSExpression", "value": "{}"}, "controlQuery": {"type": "JSExpression", "value": "true"}, "tableSelection": {"type": "JSExpression", "value": "[]"}, "isFuture": {"type": "JSExpression", "value": "false"}, "isTodayOrFuture": {"type": "JSExpression", "value": "true"}, "peopleModal": {"type": "JSExpression", "value": "{\n  \"isShow\": false,\n  \"title\": \"添加人员\"\n}"}, "peopleFormInitValues": {"type": "JSExpression", "value": "{}"}, "taskModal": {"type": "JSExpression", "value": "{\n  \"isShow\": false\n}"}, "inspectionConfigList": {"type": "JSExpression", "value": "[// {\n  //   \"startHour\": 0,\n  //   \"endHour\": 0,\n  //   \"percent\": 0\n  // }\n]"}, "taskFormItem": {"type": "JSExpression", "value": "[]"}}, "css": ".opColHeader {\n  /* display: flex; */\n  justify-content: center;\n}\n\n.opBtn {\n  width: 30px;\n  margin-right: 0;\n}\n\n.mtd-btn.mtd-btn-text {\n  color: #166ff7\n}\n\n/* 单元格垂直居中 */\n.mtd-table td {\n  vertical-align: middle !important;\n}\n\n/* 操作列的水平居中 */\n.mtd-table-cell {\n  justify-content: center;\n}\n\n/* 操作按钮右边距去除 */\n.pro-table-container .action-column .action-button {\n  margin-right: 0 !important;\n}\n\n/* 修复多行文本水平居中问题 */\n.pro-table-container .cell .content {\n  justify-content: center;\n}\n\n\n/* 修复多选列表头偏移的问题 */\n.mtd-table-column-selection .mtd-table-cell {\n  padding-right: 0;\n}\n\n/* 修复对话框中单选框菜单弹出时展示需滚动问题 */\n/* .mtd-modal-content-wrapper {\n  overflow: visible !important;\n} */", "lifeCycles": {"created": {"type": "JSFunction", "value": "function created() {\n  // this.constants.env是页面容器在运行时默认会注入的变量。\n  this.initEnv(this.constants.appEnv);\n}"}, "mounted": {"type": "JSFunction", "value": "function mounted() {\n  console.log('mounted'); // 默认选择当天\n\n  const today = new Date().toLocaleDateString('sv');\n  this.searchFilterForm().setValuesIn('dateKey', today);\n  this.setState({\n    searchFilter: Object.assign({}, this.state.searchFilter, {\n      dateKey: this.transformDateStr2Int(today)\n    })\n  });\n  console.log('searchFilter:', this.state.searchFilter);\n  window.Vue.nextTick(() => {\n    this.setState({\n      controlQuery: !this.state.controlQuery\n    });\n  });\n}"}}, "methods": {"searchFilterForm": {"type": "JSFunction", "value": "function searchFilterForm() {\n  return this.$('searchFilterForm').form;\n}"}, "checkPeopleForm": {"type": "JSFunction", "value": "function checkPeopleForm() {\n  return this.$('checkPeopleForm').form;\n}"}, "taskForm": {"type": "JSFunction", "value": "function taskForm() {\n  return this.$('taskForm').form;\n}"}, "initEnv": {"type": "JSFunction", "value": "function initEnv(appEnv) {\n  console.log('appEnv', appEnv);\n  const {\n    envParams\n  } = this.state || {};\n  envParams.forEach(item => {\n    if (!!item.name) {\n      this.setState({\n        [item.name]: item.value[appEnv || \"test\"]\n      });\n    }\n  });\n}"}, "changeWorkStatus": {"type": "JSFunction", "value": "function changeWorkStatus(onDuty, row) {\n  // console.log('changeWorkStatus:', onDuty, row)\n  if (!onDuty) {\n    this.$mtd.confirm({\n      title: `是否确认设置${row.userName || ''}/${row.userMis || ''}为休息状态`,\n      message: `休息状态将不会参与分单`,\n      width: '430px',\n      showCancelButton: true,\n      onCancel: () => {\n        // 取消，则仍然为上岗状态\n        row.onDuty = true;\n      },\n      onOk: () => {\n        this.callChangeWorkStatus(onDuty, [row.id]);\n      }\n    }).catch(() => {});\n  } else {\n    this.callChangeWorkStatus(onDuty, [row.id]);\n  }\n}"}, "callChangeWorkStatus": {"type": "JSFunction", "value": "function callChangeWorkStatus(onDuty, ids) {\n  const params = {\n    onDuty,\n    ids\n  };\n  this.fetch('changeUserStatus', params).then(data => {\n    this.toastMsg('修改人员工作状态成功', 'success'); // 如果筛选项里筛选了在岗状态，那么表格就必须刷新\n\n    if (this.state.searchFilter.onDuty != null) {\n      setTimeout(() => {\n        this.setState({\n          controlQuery: !this.state.controlQuery\n        });\n      }, 1000);\n    }\n  }).catch(err => {\n    console.log(err);\n  });\n}"}, "batchChangeWorkStatus": {"type": "JSFunction", "value": "function batchChangeWorkStatus(ev, params) {\n  // console.log('this.state.tableSelection', this.state.tableSelection)\n  const op = params.op;\n  const ids = this.state.tableSelection.map(row => row.id);\n\n  if (op == 'rest') {\n    this.$mtd.confirm({\n      title: `是否确认设置所选成员为休息状态`,\n      message: `休息状态将不会参与分单`,\n      width: '430px',\n      showCancelButton: true,\n      onOk: () => {\n        this.state.tableSelection.map(row => {\n          row.onDuty = false;\n        }); // 确认休息/确认上岗的情况下，清空选中行，否则保留\n\n        this.state.tableSelection = [];\n        this.callChangeWorkStatus(false, ids);\n      }\n    }).catch(() => {});\n  } else if (op == 'work') {\n    this.state.tableSelection.map(row => {\n      row.onDuty = true;\n    });\n    this.state.tableSelection = [];\n    this.callChangeWorkStatus(true, ids);\n  }\n}"}, "indexOfSelection": {"type": "JSFunction", "value": "function indexOfSelection(row, selection) {\n  return selection.map(s => s.id).indexOf(row.id);\n}"}, "clickEdit": {"type": "JSFunction", "value": "function clickEdit(ev) {\n  // console.log('click edit', ev.$index, ev.row)\n  const {\n    userMis,\n    role,\n    id\n  } = ev.row;\n  this.setState({\n    peopleFormInitValues: {\n      userMis,\n      role,\n      id\n    },\n    peopleModal: {\n      isShow: true,\n      title: \"编辑人员\"\n    }\n  });\n  window.Vue.nextTick(() => {\n    this.checkPeopleForm().setValues(this.state.peopleFormInitValues);\n    this.checkPeopleForm().setFieldState('userMis', {\n      'pattern': 'readPretty'\n    });\n  });\n}"}, "clickDelete": {"type": "JSFunction", "value": "function clickDelete(ev) {\n  // console.log('click delete', ev.$index)\n  // console.log('row data', ev.row)\n  this.$mtd.confirm({\n    title: '确认删除',\n    message: `确认删除成员${ev.row.userName || ''}/${ev.row.userMis || ''}`,\n    width: '430px',\n    showCancelButton: true,\n    onOk: () => {\n      const params = {\n        ids: [ev.row.id]\n      };\n      this.fetch('deleteUser', params).then(data => {\n        this.toastMsg('删除人员成功', 'success');\n        setTimeout(() => {\n          this.setState({\n            controlQuery: !this.state.controlQuery\n          });\n        }, 1000);\n      }).catch(err => {\n        console.log(err);\n      });\n    }\n  }).catch(err => {\n    console.log(err);\n  });\n}"}, "clickAddBtn": {"type": "JSFunction", "value": "function clickAddBtn() {\n  this.setState({\n    peopleModal: {\n      isShow: true,\n      title: \"添加人员\"\n    }\n  });\n  window.Vue.nextTick(() => {\n    this.checkPeopleForm().setValues({}, 'overwrite');\n    this.checkPeopleForm().setFieldState('userMis', {\n      'pattern': 'editable'\n    });\n  });\n}"}, "clickPeopleModalOk": {"type": "JSFunction", "value": "function clickPeopleModalOk(ev) {\n  this.checkPeopleForm().validate().then(async () => {\n    const formValues = this.checkPeopleForm().getValuesIn('*');\n    console.log('formily values:', formValues);\n    const params = formValues;\n    params.dateKey = this.state.searchFilter.dateKey;\n    params.userMis = params.userMis && params.userMis.trim();\n    await this.fetch('replaceUser', params).then(data => {\n      this.toastMsg(`${this.state.peopleModal.title}信息成功`, 'success');\n    }).catch(err => {\n      console.log(err);\n    });\n    window.Vue.nextTick(() => {\n      this.setState({\n        peopleModal: {\n          isShow: false\n        }\n      });\n    });\n    setTimeout(() => {\n      this.setState({\n        controlQuery: !this.state.controlQuery\n      });\n    }, 1000);\n  }).catch(err => {\n    console.log('validate error:', err);\n  });\n}"}, "clickPeopleModalClose": {"type": "JSFunction", "value": "function clickPeopleModalClose(ev) {\n  this.setState({\n    peopleModal: {\n      isShow: false\n    }\n  });\n}"}, "clickTaskManage": {"type": "JSFunction", "value": "function clickTaskManage() {\n  this.setState({\n    taskModal: {\n      isShow: true\n    }\n  });\n  window.Vue.nextTick(() => {\n    this.queryInspectionConfig();\n  });\n}"}, "queryInspectionConfig": {"type": "JSFunction", "value": "function queryInspectionConfig() {\n  const params = {\n    dateKey: this.state.searchFilter.dateKey\n  };\n  this.fetch('queryInspectionConfig', params).then(data => {\n    if (data == null || data.hourPercentList == null) {\n      this.setState({\n        inspectionConfigList: []\n      });\n    } else {\n      const {\n        id,\n        hourPercentList\n      } = data;\n      if (id) this.taskForm().setValuesIn('id', id);\n\n      if (hourPercentList) {\n        this.setState({\n          inspectionConfigList: hourPercentList\n        });\n      }\n    }\n\n    this.transformInspectionToForm(this.state.inspectionConfigList);\n  }).catch(err => {\n    console.log(err);\n    this.setState({\n      inspectionConfigList: []\n    });\n    this.transformInspectionToForm(this.state.inspectionConfigList);\n  });\n}"}, "clickTaskModalClose": {"type": "JSFunction", "value": "function clickTaskModalClose(ev) {\n  this.setState({\n    taskModal: {\n      isShow: false\n    }\n  });\n}"}, "addTask": {"type": "JSFunction", "value": "function addTask() {\n  const taskFormItem = this.state.taskFormItem;\n  const inspectionTask = {\n    timeRangeProp: `timeRange${this.getRandomString()}`,\n    timeRangeDefaultValue: [],\n    percentProp: `percent${this.getRandomString()}`,\n    percentDefaultValue: 0\n  };\n  taskFormItem.push(inspectionTask); // console.log('after add task:', taskFormItem)\n\n  this.setState({\n    taskFormItem\n  });\n}"}, "delTask": {"type": "JSFunction", "value": "function delTask(event, params) {\n  const taskFormItem = this.state.taskFormItem;\n  const {\n    taskIndex\n  } = params;\n  const [deletedItem] = taskFormItem.splice(taskIndex, 1); // console.log('after delete task:', taskFormItem)\n  // console.log('deleted item:', deletedItem)\n  // 调用formily接口彻底删除无用字段\n\n  const form = this.taskForm();\n  form.query(deletedItem.timeRangeProp).take().destroy();\n  form.query(deletedItem.percentProp).take().destroy();\n  this.setState({\n    taskFormItem\n  });\n}"}, "transformInspectionToForm": {"type": "JSFunction", "value": "function transformInspectionToForm(inspectionConfigList) {\n  const taskFormItem = [];\n\n  if (Array.isArray(inspectionConfigList)) {\n    inspectionConfigList.map(item => {\n      const inspectionTask = {\n        timeRangeProp: `timeRange${this.getRandomString()}`,\n        timeRangeDefaultValue: [this.transformHour2Str(item.startHour), this.transformHour2Str(item.endHour)],\n        percentProp: `percent${this.getRandomString()}`,\n        percentDefaultValue: item.percent\n      };\n      taskFormItem.push(inspectionTask);\n    });\n  } // console.log('transformed taskFormItem:', taskFormItem)\n  // 调用formily接口删除除了id之外的表单字段\n\n\n  this.taskForm().clearFormGraph('*(!id)');\n  this.setState({\n    taskFormItem\n  });\n}"}, "transformFormToInspection": {"type": "JSFunction", "value": "function transformFormToInspection() {\n  const inspectionConfigList = [];\n\n  if (Array.isArray(this.state.taskFormItem)) {\n    const form = this.$('taskForm').form;\n    const formValues = JSON.parse(JSON.stringify(form.getValuesIn('*')));\n    console.log('formValues:', formValues);\n    this.state.taskFormItem.map(item => {\n      if (formValues[item.timeRangeProp] && formValues[item.timeRangeProp].length == 2) {\n        const inspectionTask = {\n          startHour: parseInt(formValues[item.timeRangeProp][0]),\n          endHour: parseInt(formValues[item.timeRangeProp][1]),\n          percent: formValues[item.percentProp]\n        };\n        inspectionConfigList.push(inspectionTask);\n      }\n    });\n  }\n\n  return inspectionConfigList;\n}"}, "timeRangeValidator": {"type": "JSFunction", "value": "function timeRangeValidator(value, rule, ctx) {\n  // console.log('validator:', value, rule, ctx)\n  if (value.length < 2) return '';\n  const [startTime, endTime] = value;\n  let res = '';\n  if (startTime >= endTime) res = '开始时间必须小于结束时间';\n  return res;\n}"}, "clickTaskModalOk": {"type": "JSFunction", "value": "function clickTaskModalOk(ev) {\n  if (!this.state.isTodayOrFuture) {\n    this.setState({\n      taskModal: {\n        isShow: false\n      }\n    });\n    return;\n  }\n\n  this.taskForm().validate().then(() => {\n    const data = this.transformFormToInspection();\n    console.log('data to submit:', data);\n\n    if (this.checkOverlap(data.map(item => [item.startHour, item.endHour]))) {\n      this.$mtd.message.error('多个质检任务的时间段相互之间不允许重叠');\n    } else {\n      const params = {\n        hourPercentList: data\n      };\n      const id = this.taskForm().getValuesIn('id');\n      if (id) params.id = id;\n      this.fetch('replaceInspectionConfig', params).then(data => {\n        this.toastMsg('修改质检配置成功', 'success');\n      }).catch(err => {\n        console.log(err);\n      });\n      this.setState({\n        taskModal: {\n          isShow: false\n        }\n      });\n    }\n  }).catch(err => {\n    console.log('validate error:', err);\n  });\n}"}, "fixRequestParams": {"type": "JSFunction", "value": "function fixRequestParams(formValues) {\n  formValues.dateKey = this.transformDateStr2Int(formValues.dateKey);\n  if (formValues.hasOwnProperty('onDuty') && formValues.onDuty === \"\") formValues.onDuty = null;\n  if (formValues.hasOwnProperty('needInspection') && formValues.needInspection === \"\") formValues.needInspection = null;\n  if (formValues.hasOwnProperty('nameMis') && formValues.nameMis) formValues.nameMis = formValues.nameMis.trim();\n  return formValues;\n}"}, "clickFormSearchBtn": {"type": "JSFunction", "value": "function clickFormSearchBtn() {\n  const form = this.searchFilterForm();\n  let formValues = JSON.parse(JSON.stringify(form.getValuesIn('*')));\n  formValues = this.fixRequestParams(formValues); // 上方筛选项\n\n  console.log('formValues:', formValues);\n  this.setState({\n    searchFilter: Object.assign({}, this.state.searchFilter, formValues)\n  });\n  console.log('search filters:', this.state.searchFilter);\n  this.checkFuture();\n  this.checkTodayOrFuture();\n  window.Vue.nextTick(() => {\n    this.setState({\n      controlQuery: !this.state.controlQuery\n    });\n  });\n}"}, "get14DayLater": {"type": "JSFunction", "value": "function get14DayLater() {\n  const now = new Date();\n  const timeOffset = 1000 * 3600 * 24 * 14;\n  const future = new Date(now.getTime() + timeOffset);\n  return future;\n}"}, "disabledDate": {"type": "JSFunction", "value": "function disabledDate(date) {\n  // 从14天后往前推最多选到90天\n  const future = this.get14DayLater();\n  const minDateTime = new Date(future - 1000 * 3600 * 24 * 90);\n  return date < minDateTime || date > future;\n}"}, "checkFuture": {"type": "JSFunction", "value": "function checkFuture() {\n  // 判断质检日期是未来日期\n  // 拿到当天的日期字符串\n  const today = new Date().toLocaleDateString('sv');\n  const date = this.searchFilterForm().getValuesIn('dateKey');\n  this.setState({\n    isFuture: date > today\n  }); // console.log('isFuture:', this.state.isFuture)\n}"}, "checkTodayOrFuture": {"type": "JSFunction", "value": "function checkTodayOrFuture() {\n  // 判断质检日期是否是当天或未来\n  const today = new Date().toLocaleDateString('sv');\n  const date = this.searchFilterForm().getValuesIn('dateKey');\n  this.setState({\n    isTodayOrFuture: date >= today\n  }); // console.log('isTodayOrFuture:', this.state.isTodayOrFuture)\n}"}, "afterReload": {"type": "JSFunction", "value": "function afterReload(list, response) {\n  console.log('afterReload', list, response);\n\n  if (response.code !== 200) {\n    let errorMsg = '请求失败';\n\n    if (response && (response.msg || response.message || response.errorMsg)) {\n      errorMsg = response.msg || response.message || response.errorMsg;\n    }\n\n    this.$mtd.message.error(errorMsg);\n  }\n\n  this.setState({\n    tableModel: list\n  });\n  window.Vue.nextTick(() => {\n    this.formatTableData();\n  });\n  console.log('tableModel', this.state.tableModel);\n}"}, "formatTableData": {"type": "JSFunction", "value": "function formatTableData() {\n  this.state.tableModel.dataSource.map(row => {\n    row.dateKey = this.transformInt2DateStr(row.dateKey);\n  });\n  console.log('dataSource after wash:', this.state.tableModel.dataSource);\n}"}, "getRandomString": {"type": "JSFunction", "value": "function getRandomString() {\n  return Math.random().toString().replace(/^0\\./, '');\n}"}, "checkOverlap": {"type": "JSFunction", "value": "function checkOverlap(arr) {\n  arr.sort((a, b) => {\n    if (a[0] == b[0]) return a[1] - b[1];\n    return a[0] - b[0];\n  });\n\n  for (let i = 1; i < arr.length; i++) {\n    if (arr[i - 1][1] > arr[i][0]) {\n      return true;\n    }\n  }\n\n  return false;\n}"}, "transformDateStr2Int": {"type": "JSFunction", "value": "function transformDateStr2Int(dateStr) {\n  if (!dateStr) return 0; // yyyy-MM-dd字符串转成yyyyMMdd数字\n\n  return parseInt(dateStr.replaceAll('-', '')) || 0;\n}"}, "transformInt2DateStr": {"type": "JSFunction", "value": "function transformInt2DateStr(dateInt) {\n  const dateStr = dateInt.toString();\n  return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`;\n}"}, "transformHour2Str": {"type": "JSFunction", "value": "function transformHour2Str(hour) {\n  if (hour == null || hour < 0 || hour > 24) return '';\n  const prefix = hour < 10 ? '0' : '';\n  return `${prefix}${hour}:00`;\n}"}, "toastMsg": {"type": "JSFunction", "value": "function toastMsg(content, type) {\n  this.$mtd.message({\n    message: content,\n    type: type\n  });\n}"}, "onGroupChange": {"type": "JSFunction", "value": "function onGroupChange(group) {\n  if (group === 'annotation') {\n    window.location.hash = '#/annotation';\n  }\n}"}, "openInspectionRecord": {"type": "JSFunction", "value": "function openInspectionRecord() {\n  window.location.href = `${window.location.origin}${this.state.INSPECTIONRECORDPATH}?date=${this.searchFilterForm().getValuesIn('dateKey')}`;\n}"}, "fetch": {"type": "JSFunction", "value": "function fetch(apiName, data, entire = false, needThrowError = true) {\n  return this.dataSourceMap[apiName].load(data).then(res => {\n    // 返回请求的全部内容\n    if (entire) {\n      return res;\n    } // 没有特殊需求直接返回数据部分\n\n\n    if (res && res.code === 200) {\n      return res.data;\n    } else {\n      let errorMsg = '请求失败';\n\n      if (res && (res.msg || res.message || res.errorMsg)) {\n        errorMsg = res.msg || res.message || res.errorMsg;\n      }\n\n      this.$mtd.message.error(errorMsg);\n\n      if (needThrowError) {\n        throw errorMsg;\n      }\n    }\n  }).catch(e => {\n    console.log(e);\n    throw e;\n  });\n}"}, "requestFailed": {"type": "JSFunction", "value": "function requestFailed(response) {\n  console.log('request failed', response);\n  this.$mtd.message.error('请求失败');\n}"}}, "originCode": "class LowcodeComponent extends Component {\n  state = {\n    \"HOST\": '',\n    \"INSPECTIONRECORDPATH\": '',\n    envParams: [\n      {\n        \"name\": \"HOST\",\n        \"value\": {\n          \"development\": \"//apollo.nibcrm.test.sankuai.com\",\n          \"test\": \"//apollo.nibcrm.test.sankuai.com\",\n          \"production\": \"//apollo.meituan.com\",\n          \"mock\": \"\"\n        },\n        \"description\": \"域名\"\n      },\n      {\n        \"name\": \"INSPECTIONRECORDPATH\",\n        \"value\": {\n          \"development\": \"/inspection-record/index.html#/\",\n          \"test\": \"/crm-saletool-config/inspection-record/index.html#/\",\n          \"production\": \"/crm-saletool-config/inspection-record/index.html#/\",\n          \"mock\": \"\"\n        }\n      }\n    ],\n    \"type\": \"inspection\",\n    // 顶部筛选条件\n    \"searchFilter\": {\n      \"groupKey\": this.utils.getPageConstant('GROUPKEY').inspection,\n      \"bizLine\": this.utils.getPageConstant('BIZLINE').general\n    },\n    // 质检员列表表格数据模型\n    \"tableModel\": {},\n    // 控制质检员列表表格刷新\n    \"controlQuery\": true,\n    // 质检员列表表格选中项\n    \"tableSelection\": [],\n    // 判断当天和未来14天/历史页面\n    \"isFuture\": false,\n    \"isTodayOrFuture\": true,\n    // 人员添加/编辑弹窗\n    \"peopleModal\": {\n      \"isShow\": false,\n      \"title\": \"添加人员\"\n    },\n    // 人员编辑表单初始值\n    \"peopleFormInitValues\": {},\n    // 质检任务配置弹窗\n    \"taskModal\": {\n      \"isShow\": false,\n    },\n    // 质检任务配置列表\n    \"inspectionConfigList\": [\n      // {\n      //   \"startHour\": 0,\n      //   \"endHour\": 0,\n      //   \"percent\": 0\n      // }\n    ],\n    // 质检任务配置表单项\n    \"taskFormItem\": [],\n  }\n\n  get searchFilterForm() {\n    return this.$('searchFilterForm').form\n  }\n\n  get checkPeopleForm() {\n    return this.$('checkPeopleForm').form\n  }\n\n  get taskForm() {\n    return this.$('taskForm').form\n  }\n\n  created() {\n    // this.constants.env是页面容器在运行时默认会注入的变量。\n    this.initEnv(this.constants.appEnv)\n  }\n\n  initEnv(appEnv) {\n    console.log('appEnv', appEnv)\n    const { envParams } = this.state || {}\n    envParams.forEach(item => {\n      if (!!item.name) {\n        this.setState({ [item.name]: item.value[appEnv || \"test\"] })\n      }\n    })\n  }\n\n  mounted() {\n    console.log('mounted');\n    // 默认选择当天\n    const today = new Date().toLocaleDateString('sv')\n    this.searchFilterForm().setValuesIn('dateKey', today)\n    this.setState({\n      searchFilter: Object.assign({}, this.state.searchFilter, { dateKey: this.transformDateStr2Int(today) })\n    })\n    console.log('searchFilter:', this.state.searchFilter)\n    window.Vue.nextTick(() => {\n      this.setState({\n        controlQuery: !this.state.controlQuery\n      })\n    })\n  }\n\n  // 修改在岗状态\n  changeWorkStatus(onDuty, row) {\n    // console.log('changeWorkStatus:', onDuty, row)\n    if (!onDuty) {\n      this.$mtd.confirm({\n        title: `是否确认设置${row.userName||''}/${row.userMis||''}为休息状态`,\n        message: `休息状态将不会参与分单`,\n        width: '430px',\n        showCancelButton: true,\n        onCancel: () => {\n          // 取消，则仍然为上岗状态\n          row.onDuty = true\n        },\n        onOk: () => {\n          this.callChangeWorkStatus(onDuty, [row.id])\n        }\n      }).catch(() => { });\n    } else {\n      this.callChangeWorkStatus(onDuty, [row.id])\n    }\n\n  }\n\n  callChangeWorkStatus(onDuty, ids) {\n    const params = {\n      onDuty,\n      ids\n    }\n    this.fetch('changeUserStatus', params).then(data => {\n      this.toastMsg('修改人员工作状态成功', 'success')\n      // 如果筛选项里筛选了在岗状态，那么表格就必须刷新\n      if (this.state.searchFilter.onDuty != null) {\n        setTimeout(() => {\n          this.setState({\n            controlQuery: !this.state.controlQuery\n          })\n        }, 1000)\n      }\n    }).catch(err => {\n      console.log(err)\n    })\n  }\n\n  // 批量修改在岗状态\n  batchChangeWorkStatus(ev, params) {\n    // console.log('this.state.tableSelection', this.state.tableSelection)\n    const op = params.op\n    const ids = this.state.tableSelection.map(row => row.id)\n    if (op == 'rest') {\n      this.$mtd.confirm({\n        title: `是否确认设置所选成员为休息状态`,\n        message: `休息状态将不会参与分单`,\n        width: '430px',\n        showCancelButton: true,\n        onOk: () => {\n          this.state.tableSelection.map(row => {\n            row.onDuty = false\n          })\n          // 确认休息/确认上岗的情况下，清空选中行，否则保留\n          this.state.tableSelection = []\n          this.callChangeWorkStatus(false, ids)\n        }\n      }).catch(() => { });\n    } else if (op == 'work') {\n      this.state.tableSelection.map(row => {\n        row.onDuty = true\n      })\n      this.state.tableSelection = []\n      this.callChangeWorkStatus(true, ids)\n    }\n  }\n\n  indexOfSelection(row, selection) {\n    return selection.map(s => s.id).indexOf(row.id)\n  }\n\n  // 编辑、删除、添加人员\n  clickEdit(ev) {\n    // console.log('click edit', ev.$index, ev.row)\n    const { userMis, role, id } = ev.row\n    this.setState({\n      peopleFormInitValues: { userMis, role, id },\n      peopleModal: {\n        isShow: true,\n        title: \"编辑人员\"\n      }\n    })\n\n    window.Vue.nextTick(() => {\n      this.checkPeopleForm().setValues(this.state.peopleFormInitValues)\n      this.checkPeopleForm().setFieldState('userMis', {\n        'pattern': 'readPretty'\n      })\n    })\n  }\n  clickDelete(ev) {\n    // console.log('click delete', ev.$index)\n    // console.log('row data', ev.row)\n    this.$mtd.confirm({\n      title: '确认删除',\n      message: `确认删除成员${ev.row.userName||''}/${ev.row.userMis||''}`,\n      width: '430px',\n      showCancelButton: true,\n      onOk: () => {\n        const params = { ids: [ev.row.id] }\n        this.fetch('deleteUser', params).then(data => {\n          this.toastMsg('删除人员成功', 'success')\n          setTimeout(() => {\n            this.setState({\n              controlQuery: !this.state.controlQuery\n            })\n          }, 1000)\n        }).catch(err => {\n          console.log(err)\n        })\n      }\n    }).catch((err) => {\n      console.log(err)\n    });\n  }\n\n  clickAddBtn() {\n    this.setState({\n      peopleModal: {\n        isShow: true,\n        title: \"添加人员\"\n      }\n    })\n    window.Vue.nextTick(() => {\n      this.checkPeopleForm().setValues({}, 'overwrite')\n      this.checkPeopleForm().setFieldState('userMis', {\n        'pattern': 'editable'\n      })\n    })\n  }\n\n\n  // 人员配置弹窗\n  clickPeopleModalOk(ev) {\n    this.checkPeopleForm().validate().then(async () => {\n      const formValues = this.checkPeopleForm().getValuesIn('*')\n      console.log('formily values:', formValues)\n      const params = formValues\n      params.dateKey = this.state.searchFilter.dateKey\n      params.userMis = params.userMis && params.userMis.trim()\n      await this.fetch('replaceUser', params).then(data => {\n        this.toastMsg(`${this.state.peopleModal.title}信息成功`, 'success')\n      }).catch(err => {\n        console.log(err)\n      })\n\n      window.Vue.nextTick(() => {\n        this.setState({\n          peopleModal: {\n            isShow: false\n          }\n        })\n      })\n      setTimeout(() => {\n        this.setState({\n          controlQuery: !this.state.controlQuery\n        })\n      }, 1000)\n    }).catch(err => {\n      console.log('validate error:', err)\n    })\n  }\n\n  clickPeopleModalClose(ev) {\n    this.setState({\n      peopleModal: {\n        isShow: false\n      }\n    })\n  }\n\n  // 质检任务配置弹窗\n  clickTaskManage(){\n    this.setState({\n      taskModal: {\n        isShow: true\n      }\n    })\n    window.Vue.nextTick(()=>{\n      this.queryInspectionConfig()\n    })\n  }\n\n  queryInspectionConfig() {\n    const params = {dateKey: this.state.searchFilter.dateKey}\n    this.fetch('queryInspectionConfig', params).then(data => {\n      if (data == null || data.hourPercentList == null) {\n        this.setState({\n          inspectionConfigList: []\n        })\n      }\n      else {\n        const { id, hourPercentList } = data\n        if (id) this.taskForm().setValuesIn('id', id)\n        if (hourPercentList) {\n          this.setState({\n            inspectionConfigList: hourPercentList\n          })\n        }\n      }\n      this.transformInspectionToForm(this.state.inspectionConfigList)\n    }).catch(err => {\n      console.log(err)\n      this.setState({\n        inspectionConfigList: []\n      })\n      this.transformInspectionToForm(this.state.inspectionConfigList)\n    })\n  }\n\n  clickTaskModalClose(ev) {\n    this.setState({\n      taskModal: {\n        isShow: false\n      }\n    })\n  }\n\n\n  addTask() {\n    const taskFormItem = this.state.taskFormItem\n    const inspectionTask = {\n      timeRangeProp: `timeRange${this.getRandomString()}`,\n      timeRangeDefaultValue: [],\n      percentProp: `percent${this.getRandomString()}`,\n      percentDefaultValue: 0,\n    }\n    taskFormItem.push(inspectionTask)\n    // console.log('after add task:', taskFormItem)\n    this.setState({\n      taskFormItem\n    })\n  }\n\n  delTask(event, params) {\n    const taskFormItem = this.state.taskFormItem\n    const { taskIndex } = params\n    const [deletedItem] = taskFormItem.splice(taskIndex, 1)\n    // console.log('after delete task:', taskFormItem)\n    // console.log('deleted item:', deletedItem)\n    // 调用formily接口彻底删除无用字段\n    const form = this.taskForm()\n    form.query(deletedItem.timeRangeProp).take().destroy()\n    form.query(deletedItem.percentProp).take().destroy()\n    this.setState({\n      taskFormItem\n    })\n  }\n\n  // 将inspectionConfigList转成form格式\n  transformInspectionToForm(inspectionConfigList) {\n    const taskFormItem = []\n    if (Array.isArray(inspectionConfigList)) {\n      inspectionConfigList.map(item => {\n        const inspectionTask = {\n          timeRangeProp: `timeRange${this.getRandomString()}`,\n          timeRangeDefaultValue: [this.transformHour2Str(item.startHour), this.transformHour2Str(item.endHour)],\n          percentProp: `percent${this.getRandomString()}`,\n          percentDefaultValue: item.percent\n        }\n        taskFormItem.push(inspectionTask)\n      })\n    }\n    // console.log('transformed taskFormItem:', taskFormItem)\n    // 调用formily接口删除除了id之外的表单字段\n    this.taskForm().clearFormGraph('*(!id)')\n    this.setState({\n      taskFormItem\n    })\n  }\n\n  // 将form格式数据转成inspectionConfigList\n  transformFormToInspection() {\n    const inspectionConfigList = []\n    if (Array.isArray(this.state.taskFormItem)) {\n      const form = this.$('taskForm').form\n      const formValues = JSON.parse(JSON.stringify(form.getValuesIn('*')))\n      console.log('formValues:', formValues)\n      this.state.taskFormItem.map(item => {\n        if (formValues[item.timeRangeProp] && formValues[item.timeRangeProp].length == 2) {\n          const inspectionTask = {\n            startHour: parseInt(formValues[item.timeRangeProp][0]),\n            endHour: parseInt(formValues[item.timeRangeProp][1]),\n            percent: formValues[item.percentProp]\n          }\n          inspectionConfigList.push(inspectionTask)\n        }\n      })\n    }\n    return inspectionConfigList\n  }\n\n  timeRangeValidator(value, rule, ctx) {\n    // console.log('validator:', value, rule, ctx)\n    if (value.length < 2) return ''\n    const [startTime, endTime] = value\n    let res = ''\n    if (startTime >= endTime) res = '开始时间必须小于结束时间'\n    return res\n  }\n\n  clickTaskModalOk(ev) {\n    if (!this.state.isTodayOrFuture) {\n      this.setState({\n        taskModal: {\n          isShow: false\n        }\n      })\n      return\n    }\n    this.taskForm().validate().then(() => {\n      const data = this.transformFormToInspection()\n      console.log('data to submit:', data)\n      if (this.checkOverlap(data.map(item => [item.startHour, item.endHour]))) {\n        this.$mtd.message.error('多个质检任务的时间段相互之间不允许重叠')\n      } else {\n        const params = {hourPercentList: data}\n        const id = this.taskForm().getValuesIn('id')\n        if (id) params.id = id\n        this.fetch('replaceInspectionConfig', params).then(data => {\n          this.toastMsg('修改质检配置成功', 'success')\n        }).catch(err => {\n          console.log(err)\n        })\n        this.setState({\n          taskModal: {\n            isShow: false\n          }\n        })\n      }\n      \n    }).catch(err => {\n      console.log('validate error:', err)\n    })\n  }\n\n  fixRequestParams(formValues) {\n    formValues.dateKey = this.transformDateStr2Int(formValues.dateKey)\n    if (formValues.hasOwnProperty('onDuty') && formValues.onDuty === \"\")\n      formValues.onDuty = null\n    if (formValues.hasOwnProperty('needInspection') && formValues.needInspection === \"\")\n      formValues.needInspection = null\n    if (formValues.hasOwnProperty('nameMis') && formValues.nameMis)\n      formValues.nameMis = formValues.nameMis.trim()\n    return formValues\n  }\n\n  // 筛选与搜索\n  clickFormSearchBtn() {\n    const form = this.searchFilterForm()\n    let formValues = JSON.parse(JSON.stringify(form.getValuesIn('*')))\n    formValues = this.fixRequestParams(formValues)\n    // 上方筛选项\n    console.log('formValues:', formValues)\n    this.setState({\n      searchFilter: Object.assign({}, this.state.searchFilter, formValues)\n    })\n    console.log('search filters:', this.state.searchFilter)\n    this.checkFuture()\n    this.checkTodayOrFuture()\n    window.Vue.nextTick(() => {\n      this.setState({\n        controlQuery: !this.state.controlQuery\n      })\n    })\n  }\n\n  get14DayLater() {\n    const now = new Date()\n    const timeOffset = 1000 * 3600 * 24 * 14\n    const future = new Date(now.getTime() + timeOffset)\n    return future\n  }\n\n  disabledDate(date) {\n    // 从14天后往前推最多选到90天\n    const future = this.get14DayLater()\n    const minDateTime = new Date(future - 1000 * 3600 * 24 * 90)\n    return date < minDateTime || date > future\n  }\n\n  checkFuture() {\n    // 判断质检日期是未来日期\n    // 拿到当天的日期字符串\n    const today = new Date().toLocaleDateString('sv')\n    const date = this.searchFilterForm().getValuesIn('dateKey')\n    this.setState({\n      isFuture: date > today\n    })\n    // console.log('isFuture:', this.state.isFuture)\n  }\n\n  checkTodayOrFuture() {\n    // 判断质检日期是否是当天或未来\n    const today = new Date().toLocaleDateString('sv')\n    const date = this.searchFilterForm().getValuesIn('dateKey')\n    this.setState({\n      isTodayOrFuture: date >= today\n    })\n    // console.log('isTodayOrFuture:', this.state.isTodayOrFuture)\n  }\n\n  // 传入结束时间(HH:mm)，判断质检任务是否可编辑或删除\n  // allowEditOrDel(endTime) {\n  //   // 历史日期不可编辑或删除\n  //   if (!this.state.isFuture) return false\n  //   if (endTime == '') return true\n    // const endHour = parseInt(endTime.split(':')[0])\n    // const curHour = new Date().getHours()\n    // return !(this.checkExactToday() && endHour <= curHour)\n  // }\n\n  afterReload(list, response) {\n    console.log('afterReload', list, response)\n    if (response.code !== 200) {\n      let errorMsg = '请求失败'\n      if (response && (response.msg || response.message || response.errorMsg)) {\n        errorMsg = response.msg || response.message || response.errorMsg\n      }\n      this.$mtd.message.error(errorMsg)\n    }\n\n    this.setState({\n      tableModel: list\n    })\n    window.Vue.nextTick(() => {\n      this.formatTableData()\n    })\n    console.log('tableModel', this.state.tableModel)\n  }\n  // 洗数据\n  formatTableData() {\n    this.state.tableModel.dataSource.map(row => {\n      row.dateKey = this.transformInt2DateStr(row.dateKey)\n    })\n    console.log('dataSource after wash:', this.state.tableModel.dataSource)\n  }\n\n  getRandomString() {\n    return Math.random().toString().replace(/^0\\./, '')\n  }\n\n  checkOverlap(arr) {\n    arr.sort((a, b) => {\n      if (a[0] == b[0]) return a[1] - b[1]\n      return a[0] - b[0]\n    })\n    for (let i=1; i<arr.length; i++) {\n      if (arr[i-1][1] > arr[i][0]) {\n        return true\n      }\n    }\n    return false\n  }\n\n  // 工具函数\n  transformDateStr2Int(dateStr) {\n    if (!dateStr) return 0\n    // yyyy-MM-dd字符串转成yyyyMMdd数字\n    return parseInt(dateStr.replaceAll('-', '')) || 0\n  }\n\n  transformInt2DateStr(dateInt) {\n    const dateStr = dateInt.toString()\n    return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`\n  }\n\n  transformHour2Str(hour) {\n    if (hour == null || hour < 0 || hour > 24) return ''\n    const prefix = hour < 10 ? '0' : ''\n    return `${prefix}${hour}:00` \n  }\n\n  toastMsg(content, type) {\n    this.$mtd.message({\n      message: content,\n      type: type\n    })\n  }\n\n  onGroupChange(group) {\n    if (group === 'annotation') {\n      window.location.hash = '#/annotation'\n    }\n  }\n\n  openInspectionRecord() {\n    window.location.href = `${window.location.origin}${this.state.INSPECTIONRECORDPATH}?date=${this.searchFilterForm().getValuesIn('dateKey')}`\n  }\n\n  fetch(apiName, data, entire = false, needThrowError = true) {\n    return this.dataSourceMap[apiName].load(data)\n      .then(res => {\n        // 返回请求的全部内容\n        if (entire) {\n          return res\n        }\n        // 没有特殊需求直接返回数据部分\n        if (res && res.code === 200) {\n          return res.data\n        } else {\n          let errorMsg = '请求失败'\n          if (res && (res.msg || res.message || res.errorMsg)) {\n            errorMsg = res.msg || res.message || res.errorMsg\n          }\n          this.$mtd.message.error(errorMsg)\n          if (needThrowError) {\n            throw errorMsg\n          }\n        }\n      })\n      .catch(e => {\n        console.log(e)\n        throw e\n      })\n  }\n\n  requestFailed(response) {\n    console.log('request failed', response)\n    this.$mtd.message.error('请求失败')\n  }\n\n  // testFormily(e){\n  //   const form = this.$('formilyTest').form\n  //   const formValues = JSON.parse(JSON.stringify(form.getValuesIn('*')))\n  //   console.log('formily Values:', formValues)\n  // }\n}", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "pageDataSource": {"list": [{"id": "HOST", "desp": "接口域名，可配置test环境和production环境，配置示例：https://***.sankuai.com", "type": "constant", "sort": 0, "fixed": "top", "isMultiple": true, "fixedValues": ["test", "production"]}, {"id": "HEADERS", "desp": "接口请求头，配置后会自动带入到接口配置中，支持自行修改，access-token默认从this.utils.getSSOToken()获取。", "type": "constant", "sort": 1, "fixed": "top", "isMultiple": true, "fixedValues": ["swimlane"], "valueObject": {"swimlane": "", "access-token": {"type": "JSExpression", "value": "this.utils.getSSOToken()"}, "Content-Type": "application/json"}}, {"type": "constant", "isMultiple": false, "id": "ONDUTYOPTION", "valueSingle": {"type": "JSFunction", "value": "[\n  {\n    \"label\": \"在岗\",\n    \"value\": true\n  },\n  {\n    \"label\": \"休息\",\n    \"value\": false\n  }\n]"}}, {"type": "constant", "isMultiple": false, "id": "ROLEOPTION", "valueSingle": {"type": "JSFunction", "value": "[\n  {\n    \"label\": \"组长\",\n    \"value\": 1\n  },\n  {\n    \"label\": \"组员\",\n    \"value\": 2\n  }\n]"}}, {"type": "constant", "isMultiple": false, "id": "GROUPKEY", "desp": "所属组，1-标注；2-质检", "valueSingle": {"type": "JSFunction", "value": "{\n  \"annotation\": 1,\n  \"inspection\": 2\n}"}}, {"type": "constant", "isMultiple": false, "id": "BIZLINE", "desp": "业务线，1006-餐；1007-综", "valueSingle": {"type": "JSFunction", "value": "{\n  \"food\": 1006,\n  \"general\": 1007\n}"}}]}, "constants": {"HOST": "", "HEADERS": {"swimlane": "", "access-token": {"type": "JSExpression", "value": "this.utils.getSSOToken()"}, "Content-Type": "application/json"}, "ONDUTYOPTION": [{"label": "在岗", "value": true}, {"label": "休息", "value": false}], "ROLEOPTION": [{"label": "组长", "value": 1}, {"label": "组员", "value": 2}], "GROUPKEY": {"annotation": 1, "inspection": 2}, "BIZLINE": {"food": 1006, "general": 1007}}, "utils": {}, "children": [{"componentName": "DmvModal", "id": "node_oclllqqlum9", "props": {"title": {"type": "JSExpression", "value": "this.state.peopleModal.title", "mock": "标题"}, "context": {"type": "JSExpression", "value": "this"}, "value": {"type": "JSExpression", "value": "this.state.peopleModal.isShow", "mock": true}, "placement": "center", "mask": true, "mask-closable": false, "closable": true, "destroy-on-close": false, "width": "", "append-to-container": true, "footer": {"align": "center", "size": ""}, "style": {}, "buttons": [{"text": "确定", "type": "primary", "onClick": {"type": "JSFunction", "value": "function(){ return this.clickPeopleModalOk.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "disabled": {"type": "JSExpression", "value": "this.$('').form.invalid"}, "hidden": false, "_unsafe_MixedSetter_disabled_select": "VariableSetter"}], "__events": {"eventDataList": [{"type": "componentEvent", "name": "onOk", "relatedEventName": "clickPeopleModalOk"}, {"type": "componentEvent", "name": "onClose", "relatedEventName": "clickPeopleModalClose"}], "eventList": [{"name": "onOk", "template": "function(e){console.log(e)}", "disabled": true}, {"name": "onCancel", "template": "function(e){console.log(e)}", "disabled": false}, {"name": "onClose", "template": "function(e){console.log(e)}", "disabled": true}]}, "onOk": {"type": "JSFunction", "value": "function(){this.clickPeopleModalOk.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "ref": "peopleModal", "onClose": {"type": "JSFunction", "value": "function(){this.clickPeopleModalClose.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "doclllqr6qs", "hidden": true, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyForm", "id": "node_oclllqqluma", "props": {"showColon": false, "labelPosition": "right", "appContext": {"type": "JSExpression", "value": "this"}, "rules": {"type": "JSExpression", "value": "this.state.rules"}, "labelWidth": 80, "ref": "checkPeopleForm", "style": {}}, "docId": "doclllqr6qs", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyInput", "id": "node_ocllqcawfq1", "props": {"schemaProps": {"title": "主键id", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false}, "x-component-props": {"genre": "不设置", "size": "不设置", "clearable": false, "readonly": false, "showCount": false, "isHidenWhenCollapsed": false, "type": "text"}, "name": "id", "default": ""}, "style": {}, "ref": "dmvformilyinput-9ca9b08c"}, "docId": "docllqcawfq", "hidden": false, "title": "", "isLocked": false, "condition": false, "conditionGroup": ""}, {"componentName": "DmvFormilyInput", "id": "node_oclllqqlumb", "props": {"style": {"width": "180px"}, "schemaProps": {"title": "mis号", "description": "", "default": "", "required": true, "x-component-props": {"isHidenWhenCollapsed": false, "size": "不设置", "genre": "不设置", "clearable": false, "type": "text"}, "x-decorator-props": {"labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "name": "userMis", "x-validator": [{"required": true}]}, "_unsafe_MixedSetter_schemaProps_select": "ObjectSetter", "ref": "dmvformilyinput-7b7139bf", "_unsafe_MixedSetter_schemaProps": {"x-component-props_select": "ObjectSetter"}}, "docId": "doclllqr6qs", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilySelect", "id": "node_oclllqqlumc", "props": {"schemaProps": {"title": "身份", "required": true, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"isHidenWhenCollapsed": false, "size": "不设置", "icon": "down", "clearable": false, "closable": true, "filterable": false, "autoClearQuery": false, "debounce": 0, "multiple": false, "appendToContainer": true, "placeholder": "请选择", "loadingText": "搜索中", "showCheckbox": false, "reserveKeyword": false, "allowCreate": false, "show-select-all": false}, "name": "role", "_unsafe_MixedSetter_enum_select": "ExpressionSetter", "enum": {"type": "JSExpression", "value": "this.utils.getPageConstant('ROLEOPTION')"}, "x-validator": [{"required": true}]}, "style": {"zIndex": 999}, "ref": "dmvformilyselect-03be31e0"}, "docId": "doclllqr6qs", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}, {"componentName": "DmvModal", "id": "node_ocllup9v4j1", "props": {"title": "质检任务配置", "context": {"type": "JSExpression", "value": "this"}, "value": {"type": "JSExpression", "value": "this.state.taskModal.isShow", "mock": true}, "placement": "center", "mask": true, "mask-closable": false, "closable": true, "destroy-on-close": false, "width": "", "append-to-container": true, "footer": {"align": "center", "size": "large"}, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClose", "relatedEventName": "clickTaskModalClose"}], "eventList": [{"name": "onOk", "template": "function(e){console.log(e)}", "disabled": false}, {"name": "onCancel", "template": "function(e){console.log(e)}", "disabled": false}, {"name": "onClose", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClose": {"type": "JSFunction", "value": "function(){this.clickTaskModalClose.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "buttons": [{"text": "确定", "type": "primary", "onClick": {"type": "JSFunction", "value": "function(){ return this.clickTaskModalOk.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "disabled": false, "hidden": false}], "ref": "dmvmodal-c50c8290"}, "docId": "docllup9xsw", "hidden": true, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvContainer", "id": "node_ocllutsowf1", "props": {"direction": "horizontal", "ref": "dmvcontainer-10ceb248"}, "docId": "docllvnkj3w", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyForm", "id": "node_ocllutsowf4", "props": {"showColon": false, "labelPosition": "right", "appContext": {"type": "JSExpression", "value": "this"}, "rules": {"type": "JSExpression", "value": "this.state.rules"}, "labelWidth": 120, "ref": "taskForm"}, "docId": "docllvnkj3w", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyInput", "id": "node_oclmev0c1y1", "props": {"schemaProps": {"title": "id", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false}, "x-component-props": {"genre": "不设置", "size": "不设置", "clearable": false, "readonly": false, "showCount": false, "isHidenWhenCollapsed": false, "type": "text"}, "name": "id", "x-display": "hidden", "default": ""}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvContainer", "id": "node_ocllutsowfw", "props": {"direction": "vertical", "ref": "dmvcontainer-787b4b90", "_unsafe_MixedSetter____loop____select": "VariableSetter", "style": {"backgroundColor": "rgba(155,155,155,0.2)", "borderRadius": "9px", "marginTop": "10px", "paddingLeft": "10px", "paddingRight": "10px"}}, "docId": "docllvnkj3w", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "loop": {"type": "JSExpression", "value": "this.state.taskFormItem"}, "loopArgs": ["taskFormItem", "taskFormItemIndex"], "children": [{"componentName": "DmvContainer", "id": "node_ocllvp6mts1", "props": {"direction": "horizontal", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}, "ref": "dmvcontainer-cb6a2ca1"}, "docId": "docllvp6rwb", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvGroupTitle", "id": "node_ocllutsowfc", "props": {"title": {"type": "JSExpression", "value": "`质检任务${this.taskFormItemIndex+1}`", "mock": "质检任务"}, "type": "FirstLevel", "ref": "dmvgrouptitle-790fa721", "style": {"backgroundColor": "rgba(155,155,155,0)"}}, "docId": "docllvnkj3w", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvButton", "id": "node_ocllvp6mts2", "props": {"type": "text", "children": "主按钮", "text": "移除", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "style": {"display": "flex", "alignItems": "center"}, "ref": "dmvbutton-572d1800", "_unsafe_MixedSetter____condition____select": "VariableSetter", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "delTask", "paramStr": "{\n \t \"taskIndex\":this.taskFormItemIndex \n}"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.delTask.apply(this,Array.prototype.slice.call(arguments).concat([{\n \t \"taskIndex\":this.taskFormItemIndex \n}])) }"}}, "docId": "docllvp6rwb", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.state.isTodayOrFuture && this.taskFormItemIndex>0", "mock": true}, "conditionGroup": ""}]}, {"componentName": "DmvFormilyTimePicker", "id": "node_ocllutsowfs", "props": {"schemaProps": {"title": "标注完成时间", "required": true, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"size": "small", "isHidenWhenCollapsed": false, "type": "timerange", "format": "HH:mm", "clearable": false, "placement": "bottom-start", "steps": [1, 60], "disabledHours": [], "disabledMinutes": [], "disabledSeconds": [], "appendToContainer": true, "value-format": "HH:mm"}, "name": {"type": "JSExpression", "value": "this.taskFormItem.timeRangeProp"}, "default": {"type": "JSExpression", "value": "this.taskFormItem.timeRangeDefaultValue"}, "x-validator": [{"triggerType": "onBlur", "validator": {"type": "JSFunction", "value": "function(){ return this.timeRangeValidator.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "required": true}], "x-pattern": {"type": "JSExpression", "value": "this.state.isTodayOrFuture? 'editable': 'disabled'", "mock": "editable"}}, "ref": "dmvformilytimepicker-4f4e5533", "style": {"width": "200px"}}, "docId": "docllvnkj3w", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilyInputNumber", "id": "node_ocllutsowf10", "props": {"schemaProps": {"title": "质检比例(%)", "required": true, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"size": "small", "isHidenWhenCollapsed": false, "precision": 0, "step": 1, "controls": true, "controlsPosition": "不设置", "allowEmpty": false, "max": 100, "min": 0}, "name": {"type": "JSExpression", "value": "this.taskFormItem.percentProp"}, "default": {"type": "JSExpression", "value": "this.taskFormItem.percentDefaultValue"}, "x-pattern": {"type": "JSExpression", "value": "this.state.isTodayOrFuture ? 'editable' : 'readPretty'", "mock": "editable"}}, "ref": "dmvformilyinputnumber-9ede1cf2", "style": {"width": "200px"}}, "docId": "docllvnkj3w", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}]}, {"componentName": "DmvContainer", "id": "node_ocllvp6mts4", "props": {"direction": "horizontal", "style": {"display": "flex", "justifyContent": "center"}, "ref": "dmvcontainer-d4b66860"}, "docId": "docllvp6rwb", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvButton", "id": "node_ocllvp6mts3", "props": {"type": "text", "children": "主按钮", "text": "添加一组", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "style": {"display": "flex", "justifyContent": "center", "alignItems": "center"}, "ref": "dmvbutton-27ef8aa6", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "addTask"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.addTask.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "docId": "docllvp6rwb", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.state.isTodayOrFuture", "mock": true}, "conditionGroup": ""}]}]}, {"componentName": "DmvContainer", "id": "node_oclluni4ki1", "props": {"direction": "horizontal"}, "docId": "doclluni9wa", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvRadioButton", "id": "node_oclluni4ki2", "props": {"style": {"width": 200, "marginTop": "20px", "marginLeft": "20px", "marginBottom": "20px", "marginRight": "20px"}, "options": [{"label": "标注组", "value": "annotation"}, {"label": "质检组", "value": "inspection"}], "v-model:modelValue": {"type": "JSExpression", "value": "this.state.type"}, "checked": true, "size": "large", "type": "fill", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onInput", "relatedEventName": "onGroupChange"}], "eventList": [{"name": "onInput", "template": "", "disabled": true}]}, "onInput": {"type": "JSFunction", "value": "function(){this.onGroupChange.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "doclluni9wa", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}, {"componentName": "DmvFormilyForm", "id": "node_ocllqccdf4bf", "props": {"showColon": false, "labelPosition": "right", "appContext": {"type": "JSExpression", "value": "this"}, "rules": {"type": "JSExpression", "value": "this.state.rules"}, "labelWidth": 100, "style": {"marginLeft": "20px", "marginRight": "20px", "backgroundColor": "rgba(155,155,155,0.27)", "display": "flex", "alignItems": "center", "paddingTop": "10px"}, "ref": "searchFilterForm"}, "docId": "docllqccdf4", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilySpace", "id": "node_ocllqccdf4bg", "props": {"schemaProps": {"title": "", "x-component-props": {"isHidenWhenCollapsed": false, "wrap": false}, "description": "", "required": false, "x-decorator-props": {"labelWidthAuto": true}}, "ref": "searchFilterForm"}, "docId": "docllqccdf4", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyDatePicker", "id": "node_ocllp3j16faj", "props": {"schemaProps": {"title": "质检日期", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true}, "x-component-props": {"size": "不设置", "isHidenWhenCollapsed": false, "type": "date", "clearable": false, "placement": "bottom-start", "showBtnNow": true, "splitPanels": true, "showWeekNumbers": false, "appendToContainer": true, "weekStart": 1, "multiple": false, "disabledDate": {"type": "JSFunction", "value": "function(){ return this.disabledDate.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "valueFormat": {"type": "JSExpression", "value": "'yyyy-MM-dd'", "mock": ""}}, "name": "<PERSON><PERSON><PERSON>"}}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilySelect", "id": "node_ocllp3j16fak", "props": {"schemaProps": {"title": "在岗状态", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true}, "x-component-props": {"isHidenWhenCollapsed": false, "size": "不设置", "icon": "down", "clearable": true, "closable": true, "filterable": false, "autoClearQuery": false, "debounce": 0, "multiple": false, "appendToContainer": false, "placeholder": "请选择", "loadingText": "搜索中", "showCheckbox": false, "reserveKeyword": false, "allowCreate": false, "show-select-all": false}, "enum": {"type": "JSExpression", "value": "this.utils.getPageConstant('ONDUTYOPTION')"}, "name": "onDuty", "_unsafe_MixedSetter_enum_select": "ExpressionSetter"}}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilyInput", "id": "node_oclmelyf8h1", "props": {"schemaProps": {"title": "质检员", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"genre": "不设置", "size": "不设置", "clearable": false, "readonly": false, "showCount": false, "isHidenWhenCollapsed": false, "type": "text"}, "name": "nameMis", "default": ""}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvButton", "id": "node_ocllp3j16fan", "props": {"type": "primary", "children": "主按钮", "text": "搜索", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "clickFormSearchBtn"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.clickFormSearchBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}, {"componentName": "DmvContainer", "id": "node_oclmis0vny2", "props": {"direction": "horizontal", "style": {"marginTop": "10px", "marginBottom": "10px"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvContainer", "id": "node_oclmis0z3y1", "props": {"direction": "horizontal", "style": {"display": "flex", "flexDirection": "row", "justifyContent": "flex-start", "marginLeft": "8px"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvButton", "id": "node_ocllkt70gqdk", "props": {"type": "text", "children": "主按钮", "text": "批量在岗", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": {"type": "JSExpression", "value": "this.state.tableSelection.length == 0", "mock": false}, "ref": "dmvbutton-b2c9dcf3", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "batchChangeWorkStatus", "paramStr": "{\n \t \"op\": \"work\"\n}"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.batchChangeWorkStatus.apply(this,Array.prototype.slice.call(arguments).concat([{\n \t \"op\": \"work\"\n}])) }"}, "_unsafe_MixedSetter____condition____select": "VariableSetter", "style": {}}, "docId": "docllkt70gq", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.state.isFuture", "mock": true}, "conditionGroup": ""}, {"componentName": "DmvButton", "id": "node_ocllkt70gqe0", "props": {"type": "text", "children": "主按钮", "text": "批量休息", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": {"type": "JSExpression", "value": "this.state.tableSelection.length == 0", "mock": false}, "ref": "dmvbutton-c8d7047c", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "batchChangeWorkStatus", "paramStr": "{\n \t \"op\":\"rest\" \n}"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.batchChangeWorkStatus.apply(this,Array.prototype.slice.call(arguments).concat([{\n \t \"op\":\"rest\" \n}])) }"}, "_unsafe_MixedSetter____condition____select": "VariableSetter", "style": {"marginLeft": "10px"}}, "docId": "docllkt70gq", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.state.isFuture", "mock": true}, "conditionGroup": ""}]}, {"componentName": "DmvContainer", "id": "node_oclmis0xdx1", "props": {"direction": "horizontal", "style": {"width": "30%", "display": "flex", "flexDirection": "row", "justifyContent": "flex-end", "marginRight": "20px"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvButton", "id": "node_ocllkv3mqf1o", "props": {"type": "", "children": "主按钮", "text": "添加人员", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "style": {"alignItems": "center", "display": "flex", "marginRight": "10px"}, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "clickAddBtn"}], "eventList": [{"name": "onClick", "template": "", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.clickAddBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "ref": "dmvbutton-5caea7e5", "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "docId": "docllkv417s", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.state.isFuture", "mock": true}, "conditionGroup": ""}, {"componentName": "DmvButton", "id": "node_oclmis0z3y2", "props": {"type": "primary", "children": "主按钮", "text": "质检任务配置", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "style": {"display": "flex", "alignItems": "center", "flexDirection": "row-reverse", "marginRight": "10px"}, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "clickTaskManage"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "ref": "dmvbutton-e0f531d5", "onClick": {"type": "JSFunction", "value": "function(){this.clickTaskManage.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "docllkv417s", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvButton", "id": "node_oclluni9wa2", "props": {"type": "text", "children": "主按钮", "text": "质检记录", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "ref": "dmvbutton-b2c9dcf3", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "openInspectionRecord"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "_unsafe_MixedSetter____condition____select": "VariableSetter", "style": {"paddingLeft": "0", "paddingRight": "0"}, "onClick": {"type": "JSFunction", "value": "function(){this.openInspectionRecord.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "docllkt70gq", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}, {"componentName": "DmvContainer", "id": "node_oclluni9wa3", "props": {"direction": "horizontal", "style": {"paddingLeft": "20px", "paddingRight": "20px"}}, "docId": "docllunil2y", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "VpdmProTable", "id": "node_oclluni9wa4", "props": {"listParams": {"listType": "listProps", "listPropsType": "props", "listProps": {"request": {"method": "post", "url": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/queryUser`", "mock": "https://yapi.sankuai.com/mock/35239/people/label"}, "responseMap": {"dataSource": "data.records", "total": "data.total"}, "params": {"type": "JSExpression", "value": "this.state.searchFilter"}, "requestMap": {"currentPage": "page"}}, "autoSearch": false, "afterReload": {"type": "JSFunction", "value": "function(){ return this.afterReload.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "searchWatchProp": {"type": "JSExpression", "value": "this.state.controlQuery"}, "requestFailed": {"type": "JSFunction", "value": "function(){ return this.requestFailed.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}}, "columnArray": [{"label": "姓名", "prop": "nameMis", "content": [{"contextProp": "userName", "type": "text", "suffix": "/", "tipEnabled": false, "tip": ""}, {"contextProp": "userMis", "type": "text"}], "width": "150", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}, {"label": "身份", "width": "150", "overflowType": "不设置", "content": [{"type": "obj", "contextProp": "role", "formatter": {"1": "组长", "2": "组员"}, "tipEnabled": false, "tip": ""}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "role"}, {"label": "质检日期", "prop": "<PERSON><PERSON><PERSON>", "content": [{"contextProp": "<PERSON><PERSON><PERSON>", "type": "text"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "当日分单量", "tip": "", "width": "", "content": [{"contextProp": "assignCount", "type": "amount", "formatter": "YYYY/MM/DD HH:mm:ss"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "assignCount"}, {"label": "当日完成量", "tip": "", "width": "", "content": [{"contextProp": "finishCount", "type": "amount", "formatter": "YYYY/MM/DD HH:mm:ss", "anthorProp": "endTime", "dividerline": "~"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "finishCount"}, {"label": "当日未完成量", "width": "", "content": [{"contextProp": "todoCount", "type": "amount"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "todoCount"}, {"label": "在岗状态", "prop": "onDuty", "content": [{"contextProp": "onDuty", "type": "switch", "tipEnabled": false}], "width": "100", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}], "pagination": {"enabled": true, "showQuickJumper": true, "showSizeChanger": true, "showTotal": true, "currentPage": 1, "pageSize": 5, "size": "不设置", "pager-count": 7}, "actionColumn": {"enabled": true, "visibleButtonCount": 1, "styleType": "text-primary", "width": "120", "labelClass": "opColHeader", "buttonArray": [{"label": "编辑", "eventType": "func", "url": "https://dev.sankuai.com/code/home", "target": "_blank", "type": "text", "buttonClass": "opBtn", "func": {"type": "JSFunction", "value": "function(){ return this.clickEdit.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "size": "large"}, {"label": "删除", "eventType": "func", "type": "text", "func": {"type": "JSFunction", "value": "function(){ return this.clickDelete.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "size": "large", "buttonClass": "opBtn"}], "_unsafe_MixedSetter_buttonArray_select": "ArraySetter"}, "table": {"size": "不设置", "showHeader": true, "horizontalVirtual": false, "emptyText": "暂无数据", "loadingMessage": "正在加载中", "orderEnabled": false, "checkboxEnabled": true, "reserveSelection": true, "rowClass": "", "cellClass": "", "headerRowClass": "", "headerCellClass": "", "indexOfSelection": {"type": "JSFunction", "value": "function(){ return this.indexOfSelection.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "style": {"width": "100%"}, "selection": {"type": "JSExpression", "value": "this.state.tableSelection"}, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onSwitchChange", "relatedEventName": "changeWorkStatus"}], "eventList": [{"name": "onSelect", "template": "function(selection,row){}", "disabled": false}, {"name": "onSelect-all", "template": "function(selection){}", "disabled": false}, {"name": "onSwitchChange", "template": "function(val,row){}", "disabled": true}, {"name": "onInputChange", "template": "function(val,row){}", "disabled": false}]}, "onSwitchChange": {"type": "JSFunction", "value": "function(){this.changeWorkStatus.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "ref": "checkPeopleTable", "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "docId": "docllunil2y", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.state.isFuture", "mock": true}, "conditionGroup": ""}]}, {"componentName": "DmvContainer", "id": "node_ocllup9mtz1", "props": {"direction": "horizontal", "style": {"marginLeft": "20px", "marginRight": "20px"}}, "docId": "docllunil2y", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "VpdmProTable", "id": "node_ocllup9mtz2", "props": {"listParams": {"listType": "listProps", "listPropsType": "props", "listProps": {"request": {"method": "post", "url": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/queryUser`", "mock": "https://yapi.sankuai.com/mock/35239/people/label"}, "responseMap": {"dataSource": "data.records", "total": "data.total"}, "params": {"type": "JSExpression", "value": "this.state.searchFilter"}, "requestMap": {"currentPage": "page"}}, "autoSearch": false, "searchWatchProp": {"type": "JSExpression", "value": "this.state.controlQuery"}, "afterReload": {"type": "JSFunction", "value": "function(){ return this.afterReload.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "requestFailed": {"type": "JSFunction", "value": "function(){ return this.requestFailed.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}}, "columnArray": [{"label": "姓名", "prop": "nameMis", "content": [{"contextProp": "userName", "type": "text", "suffix": "/", "tipEnabled": false, "tip": ""}, {"contextProp": "userMis", "type": "text"}], "width": "150", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}, {"label": "身份", "width": "150", "overflowType": "不设置", "content": [{"type": "obj", "contextProp": "role", "formatter": {"1": "组长", "2": "组员"}, "tipEnabled": false, "tip": ""}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "role"}, {"label": "质检日期", "prop": "<PERSON><PERSON><PERSON>", "content": [{"contextProp": "<PERSON><PERSON><PERSON>", "type": "text"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "当日分单量", "tip": "", "width": "", "content": [{"contextProp": "assignCount", "type": "amount", "formatter": "YYYY/MM/DD HH:mm:ss"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "assignCount"}, {"label": "当日完成量", "tip": "", "width": "", "content": [{"contextProp": "finishCount", "type": "amount", "formatter": "YYYY/MM/DD HH:mm:ss", "anthorProp": "endTime", "dividerline": "~"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "finishCount"}, {"label": "当日未完成量", "width": "", "content": [{"contextProp": "todoCount", "type": "amount"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "todoCount"}, {"label": "在岗状态", "prop": "onDuty", "content": [{"contextProp": "onDuty", "type": "obj", "tipEnabled": false, "formatter": {"true": "在岗", "false": "休息"}}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}], "pagination": {"enabled": true, "showQuickJumper": true, "showSizeChanger": true, "showTotal": true, "currentPage": 1, "pageSize": 5, "size": "不设置", "pager-count": 7}, "actionColumn": {"enabled": false, "visibleButtonCount": 1, "styleType": "text-primary", "width": "120", "labelClass": "opColHeader", "buttonArray": [{"label": "编辑", "eventType": "func", "url": "https://dev.sankuai.com/code/home", "target": "_blank", "type": "text", "buttonClass": "opBtn", "func": {"type": "JSFunction", "value": "function(){ return this.clickEdit.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "size": "large"}, {"label": "删除", "eventType": "func", "type": "text", "func": {"type": "JSFunction", "value": "function(){ return this.clickDelete.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "size": "large", "buttonClass": "opBtn"}], "_unsafe_MixedSetter_buttonArray_select": "ArraySetter"}, "table": {"size": "不设置", "showHeader": true, "horizontalVirtual": false, "emptyText": "暂无数据", "loadingMessage": "正在加载中", "orderEnabled": false, "checkboxEnabled": false, "reserveSelection": true, "rowClass": "", "cellClass": "", "headerRowClass": "", "headerCellClass": ""}, "style": {"width": "100%"}, "selection": {"type": "JSExpression", "value": "this.state.tableSelection"}, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onSwitchChange", "relatedEventName": "changeWorkStatus"}], "eventList": [{"name": "onSelect", "template": "function(selection,row){}", "disabled": false}, {"name": "onSelect-all", "template": "function(selection){}", "disabled": false}, {"name": "onSwitchChange", "template": "function(val,row){}", "disabled": true}, {"name": "onInputChange", "template": "function(val,row){}", "disabled": false}]}, "onSwitchChange": {"type": "JSFunction", "value": "function(){this.changeWorkStatus.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "ref": "checkPeopleTable", "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "docId": "docllunil2y", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "!this.state.isFuture", "mock": true}, "conditionGroup": ""}]}, {"componentName": "DmvFormilyForm", "id": "node_ocllxjoshc1", "props": {"showColon": false, "labelPosition": "right", "appContext": {"type": "JSExpression", "value": "this"}, "rules": {"type": "JSExpression", "value": "this.state.rules"}, "labelWidth": 80, "ref": "formilyTest"}, "docId": "docllxjoshc", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyArrayItems", "id": "node_ocllxjoshc2", "props": {"schemaProps": {"title": "", "description": "", "required": false, "x-decorator-props": {"labelWidthAuto": false}, "name": "name"}, "ref": "dmvformilyarrayitems-29fc3179"}, "docId": "docllxjoshc", "hidden": false, "title": "", "isLocked": false, "condition": false, "conditionGroup": "", "children": [{"componentName": "DmvFormilySpace", "id": "node_ocllxjoshc3", "props": {"schemaProps": {"x-component-props": {"align": "center"}}}, "docId": "docllxjoshc", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyTimePicker", "id": "node_ocllxjoshcb", "props": {"schemaProps": {"title": "标注时间", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false}, "x-component-props": {"size": "不设置", "isHidenWhenCollapsed": false, "type": "timerange", "format": "HH:mm", "clearable": false, "placement": "bottom-start", "steps": [1, 60], "disabledHours": [], "disabledMinutes": [], "disabledSeconds": [], "appendToContainer": true, "value-format": "HH:mm"}, "x-validator": [{"triggerType": "onBlur", "validator": {"type": "JSFunction", "value": "function(){ return this.timeRangeValidator.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "required": true}], "name": "date"}, "ref": "dmvformilytimepicker-3fdbf853"}, "docId": "docllxjp6ui", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilyArrayBaseRemove", "id": "node_ocllxjoshc5", "props": {"schemaProps": {"x-component-props": {"title": "删除"}}, "ref": "dmvformilyarraybaseremove-910d6c75"}, "docId": "docllxjoshc", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}, {"componentName": "DmvFormilyInputNumber", "id": "node_ocllxjoshcc", "props": {"schemaProps": {"title": "质检比例", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false}, "x-component-props": {"size": "不设置", "isHidenWhenCollapsed": false, "precision": 1, "step": 1, "controls": true, "controlsPosition": "不设置", "allowEmpty": false}, "name": "ratio"}}, "docId": "docllxjp6ui", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilyArrayBaseAddition", "id": "node_ocllxjoshc8", "props": {"schemaProps": {"x-component-props": {"title": "增加一组"}}, "ref": "dmvformilyarraybaseaddition-a5109013"}, "docId": "docllxjoshc", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}, {"componentName": "DmvButton", "id": "node_ocllxjoshcd", "props": {"type": "primary", "children": "主按钮", "text": "主按钮", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "testFormily"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.testFormily.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "ref": "dmvbutton-439fc9f8"}, "docId": "docllxjp6ui", "hidden": false, "title": "", "isLocked": false, "condition": false, "conditionGroup": ""}]}