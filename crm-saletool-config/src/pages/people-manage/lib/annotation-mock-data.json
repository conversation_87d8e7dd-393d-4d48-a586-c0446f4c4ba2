{"componentName": "Page", "id": "node_dockcviv8fo1", "props": {}, "docId": "doclldr7n01", "fileName": "/", "dataSource": {"list": [{"options": {"headers": {"swimlane": ""}, "params": {"groupKey": 1, "bizLine": 1007}, "method": "POST", "isCors": true, "timeout": 5000, "uriPath": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/replaceUser`"}, "uri": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/replaceUser`"}}, "type": "fetch", "source": "NORMAL_API", "isInit": false, "isAutoHost": false, "id": "replaceUser", "desp": "新增/修改用户"}, {"options": {"headers": {"swimlane": "", "Content-Type": "application/json"}, "params": {"bizLine": 1007}, "method": "POST", "isCors": true, "timeout": 5000, "uriPath": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/deleteUser`"}, "uri": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/deleteUser`"}}, "type": "fetch", "source": "NORMAL_API", "isInit": false, "isAutoHost": false, "id": "deleteUser", "desp": "(批量)删除用户"}, {"options": {"headers": {"swimlane": "", "Content-Type": "application/json"}, "params": {"bizLine": 1007}, "method": "POST", "isCors": true, "timeout": 5000, "uriPath": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/changeUserStatus`"}, "uri": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/changeUserStatus`"}}, "type": "fetch", "source": "NORMAL_API", "isInit": false, "isAutoHost": false, "id": "changeUserStatus", "desp": "(批量)修改用户工作状态"}]}, "state": {"HOST": {"type": "JSExpression", "value": "''"}, "envParams": {"type": "JSExpression", "value": "[{\n  \"name\": \"HOST\",\n  \"value\": {\n    \"development\": \"//apollo.nibcrm.test.sankuai.com\",\n    \"test\": \"//apollo.nibcrm.test.sankuai.com\",\n    \"production\": \"//apollo.meituan.com\",\n    \"mock\": \"\"\n  },\n  \"description\": \"域名\"\n} // {\n//   \"name\": \"HOST\",\n//   \"value\": {\n//     \"development\": \"//yapi.sankuai.com/thrift/mock/project/45358\",\n//     \"test\": \"//yapi.sankuai.com/thrift/mock/project/45358\",\n//     \"production\": \"\",\n//     \"mock\": \"\"\n//   },\n//   \"description\": \"域名\"\n// },\n]"}, "type": {"type": "JSExpression", "value": "\"annotation\""}, "tableModel": {"type": "JSExpression", "value": "{}"}, "controlQuery": {"type": "JSExpression", "value": "true"}, "tableSelection": {"type": "JSExpression", "value": "[]"}, "searchFilter": {"type": "JSExpression", "value": "{\n  \"groupKey\": this.utils.getPageConstant('GROUPKEY').annotation,\n  \"bizLine\": this.utils.getPageConstant('BIZLINE').general\n}"}, "isFuture": {"type": "JSExpression", "value": "false"}, "peopleModal": {"type": "JSExpression", "value": "{\n  \"isShow\": false,\n  \"title\": \"添加人员\"\n}"}, "peopleFormInitValues": {"type": "JSExpression", "value": "{}"}}, "css": ".opColHeader {\n  /* display: flex; */\n  justify-content: center;\n}\n\n.opBtn {\n  width: 30px;\n  margin-right: 0;\n}\n\n.mtd-btn.mtd-btn-text {\n  color: #166ff7\n}\n\n/* 单元格垂直居中 */\n.mtd-table td {\n  vertical-align: middle !important;\n}\n\n/* 操作列的水平居中 */\n.mtd-table-cell {\n  justify-content: center;\n}\n\n/* 操作按钮右边距去除 */\n.pro-table-container .action-column .action-button {\n  margin-right: 0 !important;\n}\n\n/* 修复多行文本水平居中问题 */\n.pro-table-container .cell .content {\n  justify-content: center;\n}\n\n\n/* 修复多选列表头偏移的问题 */\n.mtd-table-column-selection .mtd-table-cell {\n  padding-right: 0;\n}", "lifeCycles": {"created": {"type": "JSFunction", "value": "function created() {\n  // this.constants.env是页面容器在运行时默认会注入的变量。\n  this.initEnv(this.constants.appEnv);\n}"}, "mounted": {"type": "JSFunction", "value": "function mounted() {\n  console.log('mounted'); // 默认选择当天\n\n  const today = new Date().toLocaleDateString('sv');\n  this.searchFilterForm().setValuesIn('dateKey', today);\n  this.setState({\n    searchFilter: Object.assign({}, this.state.searchFilter, {\n      dateKey: this.transformDateStr2Int(today)\n    }),\n    isFuture: false\n  }); // console.log('searchFilter:', this.state.searchFilter)\n\n  window.Vue.nextTick(() => {\n    this.setState({\n      controlQuery: !this.state.controlQuery\n    });\n  });\n}"}}, "methods": {"searchFilterForm": {"type": "JSFunction", "value": "function searchFilterForm() {\n  return this.$('searchFilterForm').form;\n}"}, "labelPeopleForm": {"type": "JSFunction", "value": "function labelPeopleForm() {\n  return this.$('labelPeopleForm').form;\n}"}, "initEnv": {"type": "JSFunction", "value": "function initEnv(appEnv) {\n  console.log('appEnv', appEnv);\n  const {\n    envParams\n  } = this.state || {};\n  envParams.forEach(item => {\n    if (!!item.name) {\n      this.setState({\n        [item.name]: item.value[appEnv || \"test\"]\n      });\n    }\n  });\n}"}, "changeWorkStatus": {"type": "JSFunction", "value": "function changeWorkStatus(onDuty, row) {\n  // console.log('changeWorkStatus:', onDuty, row)\n  if (!onDuty) {\n    this.$mtd.confirm({\n      title: `是否确认设置${row.userName || ''}/${row.userMis || ''}为休息状态`,\n      message: `休息状态将不会参与分单`,\n      width: '430px',\n      showCancelButton: true,\n      onCancel: () => {\n        // 取消，则仍然为上岗状态\n        row.onDuty = true;\n      },\n      onOk: () => {\n        this.callChangeWorkStatus(onDuty, [row.id]);\n      }\n    }).catch(() => {});\n  } else {\n    this.callChangeWorkStatus(onDuty, [row.id]);\n  }\n}"}, "callChangeWorkStatus": {"type": "JSFunction", "value": "function callChangeWorkStatus(onDuty, ids) {\n  const params = {\n    onDuty,\n    ids\n  };\n  this.fetch('changeUserStatus', params).then(data => {\n    this.toastMsg('修改人员工作状态成功', 'success'); // 如果筛选项里筛选了在岗状态，那么表格就必须刷新\n\n    if (this.state.searchFilter.onDuty != null) {\n      setTimeout(() => {\n        this.setState({\n          controlQuery: !this.state.controlQuery\n        });\n      }, 1000);\n    }\n  }).catch(err => {\n    console.log(err);\n  });\n}"}, "batchChangeWorkStatus": {"type": "JSFunction", "value": "function batchChangeWorkStatus(ev, params) {\n  // console.log('this.state.tableSelection', this.state.tableSelection)\n  const op = params.op;\n  const ids = this.state.tableSelection.map(row => row.id);\n\n  if (op == 'rest') {\n    this.$mtd.confirm({\n      title: `是否确认设置所选成员为休息状态`,\n      message: `休息状态将不会参与分单`,\n      width: '430px',\n      showCancelButton: true,\n      onOk: () => {\n        this.state.tableSelection.map(row => {\n          row.onDuty = false;\n        }); // 确认休息/确认上岗的情况下，清空选中行，否则保留\n\n        this.state.tableSelection = [];\n        this.callChangeWorkStatus(false, ids);\n      }\n    }).catch(() => {});\n  } else if (op == 'work') {\n    this.state.tableSelection.map(row => {\n      row.onDuty = true;\n    });\n    this.state.tableSelection = [];\n    this.callChangeWorkStatus(true, ids);\n  }\n}"}, "indexOfSelection": {"type": "JSFunction", "value": "function indexOfSelection(row, selection) {\n  return selection.map(s => s.id).indexOf(row.id);\n}"}, "clickEdit": {"type": "JSFunction", "value": "function clickEdit(ev) {\n  // console.log('click edit', ev.$index, ev.row)\n  const {\n    userMis,\n    role,\n    planCount,\n    needInspection,\n    id\n  } = ev.row;\n  this.setState({\n    peopleFormInitValues: {\n      userMis,\n      role,\n      planCount,\n      needInspection,\n      id\n    },\n    peopleModal: {\n      isShow: true,\n      title: \"编辑人员\"\n    }\n  });\n  window.Vue.nextTick(() => {\n    this.labelPeopleForm().setValues(this.state.peopleFormInitValues);\n    this.labelPeopleForm().setFieldState('userMis', {\n      'pattern': 'readPretty'\n    });\n  });\n}"}, "clickDelete": {"type": "JSFunction", "value": "function clickDelete(ev) {\n  // console.log('click delete', ev.$index)\n  // console.log('row data', ev.row)\n  this.$mtd.confirm({\n    title: '确认删除',\n    message: `确认删除成员${ev.row.userName || ''}/${ev.row.userMis || ''}`,\n    width: '430px',\n    showCancelButton: true,\n    onOk: () => {\n      const params = {\n        ids: [ev.row.id]\n      };\n      this.fetch('deleteUser', params).then(data => {\n        this.toastMsg('删除人员成功', 'success');\n        setTimeout(() => {\n          this.setState({\n            controlQuery: !this.state.controlQuery\n          });\n        }, 1000);\n      }).catch(err => {\n        console.log(err);\n      });\n    }\n  }).catch(err => {\n    console.log(err);\n  });\n}"}, "clickAddBtn": {"type": "JSFunction", "value": "function clickAddBtn() {\n  this.setState({\n    peopleModal: {\n      isShow: true,\n      title: \"添加人员\"\n    }\n  });\n  window.Vue.nextTick(() => {\n    this.labelPeopleForm().setValues({\n      needInspection: false\n    }, 'overwrite');\n    this.labelPeopleForm().setFieldState('userMis', {\n      'pattern': 'editable'\n    });\n  });\n}"}, "clickPeopleModalOk": {"type": "JSFunction", "value": "function clickPeopleModalOk(ev) {\n  this.labelPeopleForm().validate().then(async () => {\n    const formValues = this.labelPeopleForm().getValuesIn('*');\n    console.log('formily values:', formValues);\n    const params = formValues;\n    params.dateKey = this.state.searchFilter.dateKey;\n    params.userMis = params.userMis && params.userMis.trim();\n    await this.fetch('replaceUser', params).then(data => {\n      this.toastMsg(`${this.state.peopleModal.title}信息成功`, 'success');\n    }).catch(err => {\n      console.log(err);\n    });\n    window.Vue.nextTick(() => {\n      this.setState({\n        peopleModal: {\n          isShow: false\n        }\n      });\n    });\n    setTimeout(() => {\n      this.setState({\n        controlQuery: !this.state.controlQuery\n      });\n    }, 1000);\n  }).catch(err => {\n    console.log('validate error:', err);\n  });\n}"}, "clickPeopleModalClose": {"type": "JSFunction", "value": "function clickPeopleModalClose(ev) {\n  this.setState({\n    peopleModal: {\n      isShow: false\n    }\n  });\n}"}, "fixRequestParams": {"type": "JSFunction", "value": "function fixRequestParams(formValues) {\n  formValues.dateKey = this.transformDateStr2Int(formValues.dateKey);\n  if (formValues.hasOwnProperty('onDuty') && formValues.onDuty === \"\") formValues.onDuty = null;\n  if (formValues.hasOwnProperty('needInspection') && formValues.needInspection === \"\") formValues.needInspection = null;\n  if (formValues.hasOwnProperty('nameMis') && formValues.nameMis) formValues.nameMis = formValues.nameMis.trim();\n  return formValues;\n}"}, "clickFormSearchBtn": {"type": "JSFunction", "value": "function clickFormSearchBtn() {\n  const form = this.searchFilterForm();\n  let formValues = JSON.parse(JSON.stringify(form.getValuesIn('*')));\n  formValues = this.fixRequestParams(formValues); // 上方筛选项\n\n  console.log('formValues:', formValues);\n  this.setState({\n    searchFilter: Object.assign({}, this.state.searchFilter, formValues)\n  });\n  console.log('search filters:', this.state.searchFilter);\n  this.checkFuture();\n  window.Vue.nextTick(() => {\n    this.setState({\n      controlQuery: !this.state.controlQuery\n    });\n  });\n}"}, "get14DayLater": {"type": "JSFunction", "value": "function get14DayLater() {\n  const now = new Date();\n  const timeOffset = 1000 * 3600 * 24 * 14;\n  const future = new Date(now.getTime() + timeOffset);\n  return future;\n}"}, "disabledDate": {"type": "JSFunction", "value": "function disabledDate(date) {\n  // 从14天后往前推最多选到90天\n  const future = this.get14DayLater();\n  const minDateTime = new Date(future - 1000 * 3600 * 24 * 90);\n  return date < minDateTime || date > future;\n}"}, "checkFuture": {"type": "JSFunction", "value": "function checkFuture() {\n  // 判断标注日期是未来日期\n  // 拿到当天的日期字符串\n  const today = new Date().toLocaleDateString('sv');\n  const date = this.searchFilterForm().getValuesIn('dateKey');\n  this.setState({\n    isFuture: date > today\n  }); // console.log('isFuture:', this.state.isFuture)\n}"}, "afterReload": {"type": "JSFunction", "value": "function afterReload(list, response) {\n  console.log('afterReload', list, response);\n\n  if (response.code !== 200) {\n    let errorMsg = '请求失败';\n\n    if (response && (response.msg || response.message || response.errorMsg)) {\n      errorMsg = response.msg || response.message || response.errorMsg;\n    }\n\n    this.$mtd.message.error(errorMsg);\n  }\n\n  this.setState({\n    tableModel: list\n  });\n  window.Vue.nextTick(() => {\n    this.formatTableData();\n  });\n  console.log('tableModel', this.state.tableModel);\n}"}, "formatTableData": {"type": "JSFunction", "value": "function formatTableData() {\n  this.state.tableModel.dataSource.map(row => {\n    row.roleDesc = this.formatRole(row.role) + this.formatOutsourcing(row.outsourcing);\n    row.dateKey = this.transformInt2DateStr(row.dateKey);\n  });\n  console.log('dataSource after wash:', this.state.tableModel.dataSource);\n}"}, "transformDateStr2Int": {"type": "JSFunction", "value": "function transformDateStr2Int(dateStr) {\n  if (!dateStr) return 0; // yyyy-MM-dd字符串转成yyyyMMdd数字\n\n  return parseInt(dateStr.replaceAll('-', '')) || 0;\n}"}, "transformInt2DateStr": {"type": "JSFunction", "value": "function transformInt2DateStr(dateInt) {\n  const dateStr = dateInt.toString();\n  return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`;\n}"}, "formatRole": {"type": "JSFunction", "value": "function formatRole(role) {\n  return role == this.utils.getPageConstant('ROLEENUM').leader ? \"组长\" : \"组员\";\n}"}, "formatOutsourcing": {"type": "JSFunction", "value": "function formatOutsourcing(outsourcing) {\n  return outsourcing ? \"(外包)\" : \"\";\n}"}, "toastMsg": {"type": "JSFunction", "value": "function toastMsg(content, type) {\n  this.$mtd.message({\n    message: content,\n    type: type\n  });\n}"}, "onGroupChange": {"type": "JSFunction", "value": "function onGroupChange(group) {\n  if (group === 'inspection') {\n    window.location.hash = '#/inspection';\n  }\n}"}, "fetch": {"type": "JSFunction", "value": "function fetch(apiName, data, entire = false, needThrowError = true) {\n  return this.dataSourceMap[apiName].load(data).then(res => {\n    // 返回请求的全部内容\n    if (entire) {\n      return res;\n    } // 没有特殊需求直接返回数据部分\n\n\n    if (res && res.code === 200) {\n      return res.data;\n    } else {\n      let errorMsg = '请求失败';\n\n      if (res && (res.msg || res.message || res.errorMsg)) {\n        errorMsg = res.msg || res.message || res.errorMsg;\n      }\n\n      this.$mtd.message.error(errorMsg);\n\n      if (needThrowError) {\n        throw errorMsg;\n      }\n    }\n  }).catch(e => {\n    console.log(e);\n    throw e;\n  });\n}"}, "requestFailed": {"type": "JSFunction", "value": "function requestFailed(response) {\n  console.log('request failed', response);\n  this.$mtd.message.error('请求失败');\n}"}}, "originCode": "class LowcodeComponent extends Component {\n  state = {\n    \"HOST\": '',\n    envParams: [\n      {\n        \"name\": \"HOST\",\n        \"value\": {\n          \"development\": \"//apollo.nibcrm.test.sankuai.com\",\n          \"test\": \"//apollo.nibcrm.test.sankuai.com\",\n          \"production\": \"//apollo.meituan.com\",\n          \"mock\": \"\"\n        },\n        \"description\": \"域名\"\n      },\n      // {\n      //   \"name\": \"HOST\",\n      //   \"value\": {\n      //     \"development\": \"//yapi.sankuai.com/thrift/mock/project/45358\",\n      //     \"test\": \"//yapi.sankuai.com/thrift/mock/project/45358\",\n      //     \"production\": \"\",\n      //     \"mock\": \"\"\n      //   },\n      //   \"description\": \"域名\"\n      // },\n    ],\n    // 标注组/质检组\n    \"type\": \"annotation\",\n    // 标注员列表表格数据模型\n    \"tableModel\": {},\n    // 控制标注员列表表格刷新\n    \"controlQuery\": true,\n    // 标注员列表表格选中项\n    \"tableSelection\": [],\n    // 顶部筛选条件\n    \"searchFilter\": {\n      \"groupKey\": this.utils.getPageConstant('GROUPKEY').annotation,\n      \"bizLine\": this.utils.getPageConstant('BIZLINE').general,\n    },\n    // 判断未来/历史页面\n    \"isFuture\": false,\n    // 人员添加/编辑弹窗\n    \"peopleModal\": {\n      \"isShow\": false,\n      \"title\": \"添加人员\"\n    },\n    // 人员编辑表单初始值\n    \"peopleFormInitValues\": {},\n  }\n\n  get searchFilterForm() {\n    return this.$('searchFilterForm').form\n  }\n\n  get labelPeopleForm() {\n    return this.$('labelPeopleForm').form\n  }\n\n  created() {\n    // this.constants.env是页面容器在运行时默认会注入的变量。\n    this.initEnv(this.constants.appEnv)\n  }\n\n  initEnv(appEnv) {\n    console.log('appEnv', appEnv)\n    const { envParams } = this.state || {}\n    envParams.forEach(item => {\n      if (!!item.name) {\n        this.setState({ [item.name]: item.value[appEnv || \"test\"] })\n      }\n    })\n  }\n\n  mounted() {\n    console.log('mounted');\n    // 默认选择当天\n    const today = new Date().toLocaleDateString('sv')\n    this.searchFilterForm().setValuesIn('dateKey', today)\n    this.setState({\n      searchFilter: Object.assign({}, this.state.searchFilter, {dateKey: this.transformDateStr2Int(today)}),\n      isFuture: false,\n    })\n    // console.log('searchFilter:', this.state.searchFilter)\n    window.Vue.nextTick(() => {\n      this.setState({\n        controlQuery: !this.state.controlQuery\n      })\n    })\n  }\n  \n  // 修改在岗状态\n  changeWorkStatus(onDuty, row) {\n    // console.log('changeWorkStatus:', onDuty, row)\n    if (!onDuty) {\n      this.$mtd.confirm({\n        title: `是否确认设置${row.userName||''}/${row.userMis||''}为休息状态`,\n        message: `休息状态将不会参与分单`,\n        width: '430px',\n        showCancelButton: true,\n        onCancel: () => {\n          // 取消，则仍然为上岗状态\n          row.onDuty = true\n        },\n        onOk: () => {\n          this.callChangeWorkStatus(onDuty, [row.id])\n        }\n      }).catch(() => { });\n    } else {\n      this.callChangeWorkStatus(onDuty, [row.id])\n    }\n    \n  }\n\n  callChangeWorkStatus(onDuty, ids) {\n    const params = {\n      onDuty,\n      ids\n    }\n    this.fetch('changeUserStatus', params).then(data => {\n      this.toastMsg('修改人员工作状态成功', 'success')\n      // 如果筛选项里筛选了在岗状态，那么表格就必须刷新\n      if (this.state.searchFilter.onDuty != null) {\n        setTimeout(() => {\n          this.setState({\n            controlQuery: !this.state.controlQuery\n          })\n        }, 1000)\n      }\n    }).catch(err => {\n      console.log(err)\n    })\n  }\n\n  // 批量修改在岗状态\n  batchChangeWorkStatus(ev, params) {\n    // console.log('this.state.tableSelection', this.state.tableSelection)\n    const op = params.op\n    const ids = this.state.tableSelection.map(row => row.id)\n    if (op == 'rest') {\n      this.$mtd.confirm({\n        title: `是否确认设置所选成员为休息状态`,\n        message: `休息状态将不会参与分单`,\n        width: '430px',\n        showCancelButton: true,\n        onOk: () => {\n          this.state.tableSelection.map(row => {\n            row.onDuty = false\n          })\n          // 确认休息/确认上岗的情况下，清空选中行，否则保留\n          this.state.tableSelection = []\n          this.callChangeWorkStatus(false, ids)\n        }\n      }).catch(() => { });\n    } else if (op == 'work') {\n      this.state.tableSelection.map(row => {\n        row.onDuty = true\n      })\n      this.state.tableSelection = []\n      this.callChangeWorkStatus(true, ids)\n    }   \n  }\n\n  indexOfSelection(row, selection) {\n    return selection.map(s => s.id).indexOf(row.id)\n  }\n\n\n  // 编辑、删除、添加人员\n  clickEdit(ev) {\n    // console.log('click edit', ev.$index, ev.row)\n    const {userMis, role, planCount, needInspection, id} = ev.row\n    this.setState({\n      peopleFormInitValues: {userMis, role, planCount, needInspection, id},\n      peopleModal: {\n        isShow: true,\n        title: \"编辑人员\"\n      }\n    })\n\n    window.Vue.nextTick(()=>{\n      this.labelPeopleForm().setValues(this.state.peopleFormInitValues)\n      this.labelPeopleForm().setFieldState('userMis', {\n        'pattern': 'readPretty'\n      })\n    })\n  }\n  clickDelete(ev) {\n    // console.log('click delete', ev.$index)\n    // console.log('row data', ev.row)\n    this.$mtd.confirm({\n      title: '确认删除',\n      message: `确认删除成员${ev.row.userName||''}/${ev.row.userMis||''}`,\n      width: '430px',\n      showCancelButton: true,\n      onOk: () => {\n        const params = {ids: [ev.row.id]}\n        this.fetch('deleteUser', params).then(data => {\n          this.toastMsg('删除人员成功', 'success')\n          setTimeout(()=>{\n            this.setState({\n              controlQuery: !this.state.controlQuery\n            })\n          }, 1000)\n        }).catch(err => {\n          console.log(err)\n        })\n      }\n    }).catch((err) => {\n      console.log(err)\n    });\n  }\n  clickAddBtn() {\n    this.setState({\n      peopleModal: {\n        isShow: true,\n        title: \"添加人员\"\n      }\n    })\n    window.Vue.nextTick(() => {\n      this.labelPeopleForm().setValues({needInspection: false}, 'overwrite')\n      this.labelPeopleForm().setFieldState('userMis', {\n        'pattern': 'editable'\n      })\n    })\n  }\n\n\n  // 人员配置弹窗\n  clickPeopleModalOk(ev) {\n    this.labelPeopleForm().validate().then(async ()=>{\n      const formValues = this.labelPeopleForm().getValuesIn('*')\n      console.log('formily values:', formValues)\n      const params = formValues\n      params.dateKey = this.state.searchFilter.dateKey\n      params.userMis = params.userMis && params.userMis.trim()\n      await this.fetch('replaceUser', params).then(data => {\n        this.toastMsg(`${this.state.peopleModal.title}信息成功`, 'success')\n      }).catch(err => {\n        console.log(err)\n      })\n\n      window.Vue.nextTick(() => {\n        this.setState({\n          peopleModal: {\n            isShow: false\n          }\n        })\n      })\n      setTimeout(() => {\n        this.setState({\n          controlQuery: !this.state.controlQuery\n        })\n      }, 1000)\n      \n    }).catch(err=>{\n      console.log('validate error:', err)\n    })\n  }\n\n  clickPeopleModalClose(ev) {\n    this.setState({\n      peopleModal: {\n        isShow: false\n      }\n    })\n  }\n\n  fixRequestParams(formValues) {\n    formValues.dateKey = this.transformDateStr2Int(formValues.dateKey)\n    if (formValues.hasOwnProperty('onDuty') && formValues.onDuty === \"\")\n      formValues.onDuty = null\n    if (formValues.hasOwnProperty('needInspection') && formValues.needInspection === \"\")\n      formValues.needInspection = null\n    if (formValues.hasOwnProperty('nameMis') && formValues.nameMis)\n      formValues.nameMis = formValues.nameMis.trim()\n    return formValues\n  }\n\n  // 筛选与搜索\n  clickFormSearchBtn() {\n    const form = this.searchFilterForm()\n    let formValues = JSON.parse(JSON.stringify(form.getValuesIn('*')))\n    formValues = this.fixRequestParams(formValues)\n    // 上方筛选项\n    console.log('formValues:', formValues)\n    this.setState({\n      searchFilter: Object.assign({}, this.state.searchFilter, formValues)\n    })\n    console.log('search filters:', this.state.searchFilter)\n    this.checkFuture()\n    window.Vue.nextTick(() => {\n      this.setState({\n        controlQuery: !this.state.controlQuery\n      })\n    })\n  }\n\n  get14DayLater() {\n    const now = new Date()\n    const timeOffset = 1000 * 3600 * 24 * 14\n    const future = new Date(now.getTime() + timeOffset)\n    return future\n  }\n\n\tdisabledDate(date){\n    // 从14天后往前推最多选到90天\n    const future = this.get14DayLater()\n    const minDateTime = new Date(future - 1000 * 3600 * 24 * 90)\n    return date < minDateTime || date > future\n\t}\n\n  checkFuture() {\n    // 判断标注日期是未来日期\n    // 拿到当天的日期字符串\n    const today = new Date().toLocaleDateString('sv')\n    const date = this.searchFilterForm().getValuesIn('dateKey')\n    this.setState({\n      isFuture: date > today\n    })\n    // console.log('isFuture:', this.state.isFuture)\n  }\n\n\tafterReload(list, response) {\n    console.log('afterReload', list, response)\n    if (response.code !== 200) {\n      let errorMsg = '请求失败'\n      if (response && (response.msg || response.message || response.errorMsg)) {\n        errorMsg = response.msg || response.message || response.errorMsg\n      }\n      this.$mtd.message.error(errorMsg)\n    }\n\n    this.setState({\n      tableModel: list\n    })\n    window.Vue.nextTick(() => {\n      this.formatTableData()\n    })\n    console.log('tableModel', this.state.tableModel)\n\t}\n  // 洗数据\n  formatTableData() {\n    this.state.tableModel.dataSource.map(row => {\n      row.roleDesc = this.formatRole(row.role) + this.formatOutsourcing(row.outsourcing)\n      row.dateKey = this.transformInt2DateStr(row.dateKey)\n    })\n    console.log('dataSource after wash:', this.state.tableModel.dataSource)\n  }\n\n  // 工具函数\n  transformDateStr2Int(dateStr) {\n    if (!dateStr) return 0\n    // yyyy-MM-dd字符串转成yyyyMMdd数字\n    return parseInt(dateStr.replaceAll('-', '')) || 0\n  }\n\n  transformInt2DateStr(dateInt) {\n    const dateStr = dateInt.toString()\n    return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`\n  }\n\n  formatRole(role) {\n    return role == this.utils.getPageConstant('ROLEENUM').leader ? \"组长\" : \"组员\"\n  }\n\n  formatOutsourcing(outsourcing) {\n    return outsourcing ? \"(外包)\" : \"\"\n  }\n\n  toastMsg(content, type) {\n    this.$mtd.message({\n      message: content,\n      type: type\n    })\n  }\n\n\tonGroupChange(group){\n    if (group === 'inspection') {\n      window.location.hash = '#/inspection'\n    }\n\t}\n\n  fetch(apiName, data, entire = false, needThrowError = true) {\n    return this.dataSourceMap[apiName].load(data)\n      .then(res => {\n        // 返回请求的全部内容\n        if (entire) {\n          return res\n        }\n        // 没有特殊需求直接返回数据部分\n        if (res && res.code === 200) {\n          return res.data\n        } else {\n          let errorMsg = '请求失败'\n          if (res && (res.msg || res.message || res.errorMsg)) {\n            errorMsg = res.msg || res.message || res.errorMsg\n          }\n          this.$mtd.message.error(errorMsg)\n          if (needThrowError) {\n            throw errorMsg\n          }\n        }\n      })\n      .catch(e => {\n        console.log(e)\n        throw e\n      })\n  }\n\n\trequestFailed(response){\n    console.log('request failed', response)\n    this.$mtd.message.error('请求失败')\n\t}\n}", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "pageDataSource": {"list": [{"id": "HOST", "desp": "接口域名，可配置test环境和production环境，配置示例：https://***.sankuai.com", "type": "constant", "sort": 0, "fixed": "top", "isMultiple": true, "fixedValues": ["test", "production"]}, {"id": "HEADERS", "desp": "接口请求头，配置后会自动带入到接口配置中，支持自行修改，access-token默认从this.utils.getSSOToken()获取。", "type": "constant", "sort": 1, "fixed": "top", "isMultiple": true, "fixedValues": ["swimlane"], "valueObject": {"swimlane": "", "access-token": {"type": "JSExpression", "value": "this.utils.getSSOToken()"}, "Content-Type": "application/json"}}, {"type": "constant", "isMultiple": false, "id": "ONDUTYOPTION", "valueSingle": {"type": "JSFunction", "value": "[\n  {\n    \"label\": \"在岗\",\n    \"value\": true\n  },{\n    \"label\": \"休息\",\n    \"value\": false\n  }\n]"}}, {"type": "constant", "isMultiple": false, "id": "NEEDINSPECTIONOPTION", "valueSingle": {"type": "JSFunction", "value": "[\n  {\n    \"label\": \"是\",\n    \"value\": true\n  },\n  {\n    \"label\": \"否\",\n    \"value\": false\n  }\n]"}}, {"type": "constant", "isMultiple": false, "id": "ROLEOPTION", "valueSingle": {"type": "JSFunction", "value": "[\n  {\n    \"label\": \"组长\",\n    \"value\": 1\n  },\n  {\n    \"label\": \"组员\",\n    \"value\": 2\n  }\n]"}}, {"type": "constant", "isMultiple": false, "id": "BUOPTION", "valueSingle": {"type": "JSFunction", "value": "[\n  {\n    \"label\": \"全部\",\n    \"value\": 0\n  },\n  {\n    \"label\": \"休娱\",\n    \"value\": 1\n  },\n  {\n    \"label\": \"综发\",\n    \"value\": 2\n  },\n  {\n    \"label\": \"丽人\",\n    \"value\": 3\n  },\n  {\n    \"label\": \"LE\",\n    \"value\": 4\n  },\n  {\n    \"label\": \"商运\",\n    \"value\": 5\n  },\n  {\n    \"label\": \"医疗\",\n    \"value\": 6\n  },\n  {\n    \"label\": \"医美\",\n    \"value\": 7\n  }\n]"}}, {"type": "constant", "isMultiple": false, "id": "GROUPKEY", "desp": "所属组，1-标注；2-质检", "valueSingle": {"type": "JSFunction", "value": "{\n  \"annotation\": 1,\n  \"inspection\": 2\n}"}}, {"type": "constant", "isMultiple": false, "id": "BIZLINE", "desp": "业务线，1006-餐；1007-综", "valueSingle": {"type": "JSFunction", "value": "{\n  \"food\": 1006,\n  \"general\": 1007\n}"}}, {"type": "constant", "isMultiple": false, "id": "ROLEENUM", "valueSingle": {"type": "JSFunction", "value": "{\n  \"leader\": 1,\n  \"member\": 2\n}"}}]}, "constants": {"HOST": "", "HEADERS": {"swimlane": "", "access-token": {"type": "JSExpression", "value": "this.utils.getSSOToken()"}, "Content-Type": "application/json"}, "ONDUTYOPTION": [{"label": "在岗", "value": true}, {"label": "休息", "value": false}], "NEEDINSPECTIONOPTION": [{"label": "是", "value": true}, {"label": "否", "value": false}], "ROLEOPTION": [{"label": "组长", "value": 1}, {"label": "组员", "value": 2}], "BUOPTION": [{"label": "全部", "value": 0}, {"label": "休娱", "value": 1}, {"label": "综发", "value": 2}, {"label": "丽人", "value": 3}, {"label": "LE", "value": 4}, {"label": "商运", "value": 5}, {"label": "医疗", "value": 6}, {"label": "医美", "value": 7}], "GROUPKEY": {"annotation": 1, "inspection": 2}, "BIZLINE": {"food": 1006, "general": 1007}, "ROLEENUM": {"leader": 1, "member": 2}}, "utils": {}, "children": [{"componentName": "DmvModal", "id": "node_oclllqqlum9", "props": {"title": {"type": "JSExpression", "value": "this.state.peopleModal.title", "mock": "标题"}, "context": {"type": "JSExpression", "value": "this"}, "value": {"type": "JSExpression", "value": "this.state.peopleModal.isShow", "mock": true}, "placement": "center", "mask": true, "mask-closable": false, "closable": true, "destroy-on-close": false, "width": "", "append-to-container": true, "footer": {"align": "center", "size": "large"}, "style": {}, "buttons": [{"text": "确定", "type": "primary", "onClick": {"type": "JSFunction", "value": "function(){ return this.clickPeopleModalOk.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "disabled": {"type": "JSExpression", "value": "this.$('').form.invalid"}, "hidden": false, "_unsafe_MixedSetter_disabled_select": "VariableSetter"}], "__events": {"eventDataList": [{"type": "componentEvent", "name": "onOk", "relatedEventName": "clickPeopleModalOk"}, {"type": "componentEvent", "name": "onClose", "relatedEventName": "clickPeopleModalClose"}], "eventList": [{"name": "onOk", "template": "function(e){console.log(e)}", "disabled": true}, {"name": "onCancel", "template": "function(e){console.log(e)}", "disabled": false}, {"name": "onClose", "template": "function(e){console.log(e)}", "disabled": true}]}, "onOk": {"type": "JSFunction", "value": "function(){this.clickPeopleModalOk.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "ref": "dmvmodal-3ca03d51", "onClose": {"type": "JSFunction", "value": "function(){this.clickPeopleModalClose.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "_unsafe_MixedSetter____condition____select": "BoolSetter"}, "docId": "doclllqr6qs", "hidden": true, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyForm", "id": "node_oclllqqluma", "props": {"showColon": false, "labelPosition": "right", "appContext": {"type": "JSExpression", "value": "this"}, "rules": {"type": "JSExpression", "value": "this.state.rules"}, "labelWidth": 80, "ref": "labelPeopleForm"}, "docId": "doclllqr6qs", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyInput", "id": "node_ocllqcawfq1", "props": {"schemaProps": {"title": "主键id", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false}, "x-component-props": {"genre": "不设置", "size": "不设置", "clearable": false, "readonly": false, "showCount": false, "isHidenWhenCollapsed": false, "type": "text"}, "name": "id", "default": ""}, "style": {}, "ref": "dmvformilyinput-9ca9b08c"}, "docId": "docllqcawfq", "hidden": false, "title": "", "isLocked": false, "condition": false, "conditionGroup": ""}, {"componentName": "DmvFormilyInput", "id": "node_oclllqqlumb", "props": {"style": {"width": "180px"}, "schemaProps": {"title": "mis号", "description": "", "default": "", "required": true, "x-component-props": {"isHidenWhenCollapsed": false, "size": "不设置", "genre": "不设置", "clearable": false, "type": "text"}, "x-decorator-props": {"labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "name": "userMis", "x-validator": [{"required": true}]}, "_unsafe_MixedSetter_schemaProps_select": "ObjectSetter", "ref": "dmvformilyinput-7b7139bf", "_unsafe_MixedSetter_schemaProps": {"x-component-props_select": "ObjectSetter"}}, "docId": "doclllqr6qs", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilySelect", "id": "node_oclllqqlumc", "props": {"schemaProps": {"title": "身份", "required": true, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false}, "x-component-props": {"isHidenWhenCollapsed": false, "size": "不设置", "icon": "down", "clearable": false, "closable": true, "filterable": false, "autoClearQuery": false, "debounce": 0, "multiple": false, "appendToContainer": false, "placeholder": "请选择", "loadingText": "搜索中", "showCheckbox": false, "reserveKeyword": false, "allowCreate": false, "show-select-all": false}, "name": "role", "_unsafe_MixedSetter_enum_select": "ExpressionSetter", "enum": {"type": "JSExpression", "value": "this.utils.getPageConstant('ROLEOPTION')"}, "x-validator": [{"required": true}]}}, "docId": "doclllqr6qs", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilySwitch", "id": "node_oclllqrfgc3", "props": {"schemaProps": {"title": "是否需质检", "required": true, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": true, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"size": "不设置", "isHidenWhenCollapsed": false, "loading": false, "disabled": false}, "default": {"type": "JSExpression", "value": "false", "mock": true}, "name": "needInspection"}}, "docId": "doclllqrmc7", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilyInputNumber", "id": "node_ocllne5jeh3", "props": {"schemaProps": {"title": "设置日单量", "required": true, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": true}, "x-component-props": {"size": "不设置", "isHidenWhenCollapsed": false, "min": 0, "precision": 0, "step": 1, "controls": true, "controlsPosition": "不设置", "allowEmpty": false}, "name": "planCount"}}, "docId": "docllne5ojo", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}, {"componentName": "DmvModal", "id": "node_ocllomm4wr1", "props": {"title": "业务配置", "context": {"type": "JSExpression", "value": "this"}, "value": {"type": "JSExpression", "value": "this.state.buModal.isShow", "mock": true}, "placement": "center", "mask": true, "mask-closable": false, "closable": true, "destroy-on-close": false, "width": "", "append-to-container": true, "footer": {"align": "center", "size": ""}, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClose", "relatedEventName": "clickBuModalClose"}, {"type": "componentEvent", "name": "onOk", "relatedEventName": "clickBuModalOk"}], "eventList": [{"name": "onOk", "template": "function(e){console.log(e)}", "disabled": true}, {"name": "onCancel", "template": "function(e){console.log(e)}", "disabled": false}, {"name": "onClose", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClose": {"type": "JSFunction", "value": "function(){this.clickBuModalClose.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "buttons": [{"text": "确定", "type": "primary", "onClick": {"type": "JSFunction", "value": "function(){ return this.clickBuModalOk.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "disabled": false, "hidden": false}], "onOk": {"type": "JSFunction", "value": "function(){this.clickBuModalOk.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "ref": "dmvmodal-d88da8dd"}, "docId": "docllomm4wr", "hidden": true, "title": "", "isLocked": false, "condition": false, "conditionGroup": "", "children": [{"componentName": "VpdmProTable", "id": "node_ocllomm4wr2", "props": {"listParams": {"listType": "listProps", "listPropsType": "props", "listProps": {"autoSearch": false, "request": {"method": "get", "url": "https://yapi.sankuai.com/mock/35239/bu/manage", "responseMap": {"dataSource": "data"}, "params": {"type": "JSExpression", "value": "{date: this.state.searchFilter.date}"}}, "afterReload": {"type": "JSFunction", "value": "function(){ return this.buTableAfterReload.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "searchWatchProp": {"type": "JSExpression", "value": "this.state.buTableControlQuery"}}}, "columnArray": [{"label": "业务", "prop": "bu", "content": [{"contextProp": "bu", "type": "text"}], "width": "100", "resizable": false, "slot": false}, {"label": "当日占比(%)", "prop": "ratio", "content": [{"contextProp": "ratio", "type": "input"}], "width": "200", "resizable": false, "slot": false}, {"label": "预估单量", "prop": "estimate", "content": [{"contextProp": "estimate", "type": "text"}], "width": "200", "header-align": "center", "align": "center", "resizable": false, "slot": false}], "pagination": {"enabled": false, "showQuickJumper": true, "showSizeChanger": true, "showTotal": true, "currentPage": 1, "pageSize": 5, "size": "不设置", "pager-count": 7}, "actionColumn": {"enabled": false, "visibleButtonCount": 1, "styleType": "text-primary", "width": "250", "buttonArray": [{"label": "详情", "eventType": "link", "url": "https://dev.sankuai.com/code/home", "target": "_blank", "type": "text-primary", "buttonClass": "buttonClass"}, {"label": "function", "eventType": "func", "type": "text-primary", "func": {"type": "JSExpression", "value": "function o(){return qt(e,n,t,r||this,arguments)}"}}]}, "table": {"size": "不设置", "showHeader": true, "horizontalVirtual": false, "emptyText": "暂无数据", "loadingMessage": "正在加载中", "showSummary": true, "sumText": "总计", "rowClass": "", "cellClass": "", "headerRowClass": "", "headerCellClass": ""}, "cell-default-2": "", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onInputChange", "relatedEventName": "ratioInputChange"}], "eventList": [{"name": "onSelect", "template": "function(selection,row){}", "disabled": false}, {"name": "onSelectAll", "template": "function(selection){}", "disabled": false}, {"name": "onSwitchChange", "template": "function(val,row){}", "disabled": false}, {"name": "onInputChange", "template": "function(val,row){}", "disabled": true}]}, "onInputChange": {"type": "JSFunction", "value": "function(){this.ratioInputChange.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "ref": "buManageTable", "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "docId": "docllomm4wr", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.state.isFuture", "mock": true}, "conditionGroup": ""}, {"componentName": "VpdmProTable", "id": "node_oclludraik1", "props": {"listParams": {"listType": "listProps", "listPropsType": "props", "listProps": {"autoSearch": false, "request": {"method": "get", "url": "https://yapi.sankuai.com/mock/35239/bu/manage", "responseMap": {"dataSource": "data"}, "params": {"type": "JSExpression", "value": "{ date: this.state.searchFilter.date }"}}, "searchWatchProp": {"type": "JSExpression", "value": "this.state.buTableControlQuery"}}}, "columnArray": [{"label": "业务", "prop": "bu", "content": [{"contextProp": "bu", "type": "text"}], "width": "100", "resizable": false, "slot": false, "header-align": "center", "align": "center"}, {"label": "当日占比(%)", "prop": "ratio", "content": [{"contextProp": "ratio", "type": "text"}], "width": "200", "resizable": false, "slot": false, "header-align": "center", "align": "center"}, {"label": "预估单量", "prop": "estimate", "content": [{"contextProp": "estimate", "type": "text"}], "width": "200", "header-align": "center", "align": "center", "resizable": false, "slot": false}], "pagination": {"enabled": false, "showQuickJumper": true, "showSizeChanger": true, "showTotal": true, "currentPage": 1, "pageSize": 5, "size": "不设置", "pager-count": 7}, "actionColumn": {"enabled": false, "visibleButtonCount": 1, "styleType": "text-primary", "width": "250", "buttonArray": [{"label": "详情", "eventType": "link", "url": "https://dev.sankuai.com/code/home", "target": "_blank", "type": "text-primary", "buttonClass": "buttonClass"}, {"label": "function", "eventType": "func", "type": "text-primary", "func": {"type": "JSExpression", "value": "function o(){return qt(e,n,t,r||this,arguments)}"}}]}, "table": {"size": "不设置", "showHeader": true, "horizontalVirtual": false, "emptyText": "暂无数据", "loadingMessage": "正在加载中", "showSummary": true, "sumText": "总计", "rowClass": "", "cellClass": "", "headerRowClass": "", "headerCellClass": ""}, "cell-default-2": "", "__events": {"eventDataList": [], "eventList": [{"name": "onSelect", "template": "function(selection,row){}", "disabled": false}, {"name": "onSelect-all", "template": "function(selection){}", "disabled": false}, {"name": "onSwitchChange", "template": "function(val,row){}", "disabled": false}, {"name": "onInputChange", "template": "function(val,row){}", "disabled": true}]}, "ref": "buManageTableHistory", "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "docId": "doclluds08c", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "!this.state.isFuture", "mock": true}, "conditionGroup": ""}]}, {"componentName": "DmvContainer", "id": "node_oclldr78noi", "props": {"direction": "horizontal"}, "docId": "doclldr7n01", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvRadioButton", "id": "node_ocllkpohpuf", "props": {"style": {"width": 200, "marginLeft": "20px", "marginRight": "20px", "marginTop": "20px", "marginBottom": "20px"}, "options": [{"label": "标注组", "value": "annotation"}, {"label": "质检组", "value": "inspection"}], "v-model:modelValue": {"type": "JSExpression", "value": "this.state.type"}, "checked": true, "size": "large", "type": "fill", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onInput", "relatedEventName": "onGroupChange"}], "eventList": [{"name": "onInput", "template": "", "disabled": true}]}, "onInput": {"type": "JSFunction", "value": "function(){this.onGroupChange.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "docllkpolkb", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}, {"componentName": "DmvFormilyForm", "id": "node_ocllqccdf4bf", "props": {"showColon": false, "labelPosition": "right", "appContext": {"type": "JSExpression", "value": "this"}, "rules": {"type": "JSExpression", "value": "this.state.rules"}, "labelWidth": 100, "style": {"marginLeft": "20px", "marginRight": "20px", "backgroundColor": "rgba(155,155,155,0.27)", "paddingTop": "10px", "paddingBottom": "10px"}, "ref": "searchFilterForm"}, "docId": "docllqccdf4", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilySpace", "id": "node_ocllqccdf4bg", "props": {"schemaProps": {"title": "", "x-component-props": {"isHidenWhenCollapsed": false, "wrap": false}, "description": "", "required": false, "x-decorator-props": {"labelWidthAuto": true}}, "ref": "dmvformilyspace-abc7097d"}, "docId": "docllqccdf4", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyDatePicker", "id": "node_ocllp3j16faj", "props": {"schemaProps": {"title": "分单日期", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"size": "不设置", "isHidenWhenCollapsed": false, "type": "date", "clearable": false, "placement": "bottom-start", "showBtnNow": true, "splitPanels": true, "showWeekNumbers": false, "appendToContainer": true, "weekStart": 1, "multiple": false, "disabledDate": {"type": "JSFunction", "value": "function(){ return this.disabledDate.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "valueFormat": {"type": "JSExpression", "value": "'yyyy-MM-dd'", "mock": ""}}, "name": "<PERSON><PERSON><PERSON>"}, "ref": "dmvformilydatepicker-4a0bcc16"}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilySelect", "id": "node_ocllp3j16fak", "props": {"schemaProps": {"title": "在岗状态", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"isHidenWhenCollapsed": false, "size": "不设置", "icon": "down", "clearable": true, "closable": true, "filterable": false, "autoClearQuery": false, "debounce": 0, "multiple": false, "appendToContainer": false, "placeholder": "请选择", "loadingText": "搜索中", "showCheckbox": false, "reserveKeyword": false, "allowCreate": false, "show-select-all": false}, "enum": {"type": "JSExpression", "value": "this.utils.getPageConstant('ONDUTYOPTION')"}, "name": "onDuty", "_unsafe_MixedSetter_enum_select": "ExpressionSetter"}}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilySelect", "id": "node_ocllp3j16fal", "props": {"schemaProps": {"title": "是否需质检", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "class": "", "labelStyle": {"type": "JSExpression", "value": "{width:'100px'}"}, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"isHidenWhenCollapsed": false, "size": "不设置", "icon": "down", "clearable": true, "closable": true, "filterable": false, "autoClearQuery": false, "debounce": 0, "multiple": false, "appendToContainer": false, "placeholder": "请选择", "loadingText": "搜索中", "showCheckbox": false, "reserveKeyword": false, "allowCreate": false, "show-select-all": false}, "enum": {"type": "JSExpression", "value": "this.utils.getPageConstant('NEEDINSPECTIONOPTION')"}, "name": "needInspection", "_unsafe_MixedSetter_enum_select": "ExpressionSetter"}, "style": {}, "ref": "dmvformilyselect-c519ed33"}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}, {"componentName": "DmvFormilySpace", "id": "node_ocllqccdf4bj", "props": {"schemaProps": {"title": "", "x-component-props": {"isHidenWhenCollapsed": false, "wrap": false}, "description": "", "required": false, "x-decorator-props": {"labelWidthAuto": true}}}, "docId": "docllqccdf4", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyInput", "id": "node_oclmea32j81", "props": {"schemaProps": {"title": "标注员", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"genre": "不设置", "size": "不设置", "clearable": false, "readonly": false, "showCount": false, "isHidenWhenCollapsed": false, "type": "text"}, "name": "nameMis", "default": ""}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvButton", "id": "node_ocllp3j16fan", "props": {"type": "primary", "children": "主按钮", "text": "搜索", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "clickFormSearchBtn"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.clickFormSearchBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}, {"componentName": "DmvGridRow", "id": "node_oclllq9ggq3", "props": {"type": "flex", "justify": "", "style": {"paddingLeft": "20px", "paddingRight": "20px"}, "ref": "dmvgridrow-9565442a"}, "docId": "doclllq9ggq", "hidden": false, "title": "", "isLocked": false, "condition": false, "conditionGroup": "", "children": [{"componentName": "DmvGridCol", "id": "node_oclllq9ggq4", "props": {"span": 20}, "docId": "doclllq9ggq", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvTabs", "id": "node_ocllkorhgw1j", "props": {"value": "50344", "style": {"marginLeft": "0px", "height": "50px"}, "v-model:modelValue": {"type": "JSExpression", "value": "this.state.searchFilter.bu"}, "type": "", "size": "large", "line-size": {"type": "JSExpression", "value": "", "mock": 3}, "_unsafe_MixedSetter_items_select": "ArraySetter", "items": [{"value": "all", "label": "全部"}, {"value": "xiuyu", "label": "休娱"}, {"value": "64788", "label": "综发"}, {"value": "liren", "label": "丽人"}, {"value": "10201", "label": "LE"}, {"value": "20861", "label": "商运"}, {"value": "78854", "label": "医疗"}, {"value": "41596", "label": "医美"}], "ref": "dmvtabs-5a787ed9", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onTab-click", "relatedEventName": "clickBuTab"}], "eventList": [{"name": "onInput", "template": "function(data){console.log(data)}", "disabled": false}, {"name": "onTab-click", "template": "function(e,tab){console.log(e)}", "disabled": true}, {"name": "onTab-remove", "template": "function(e,target){console.log(e)}", "disabled": false}, {"name": "onTab-add", "template": "function(e){console.log(e)}", "disabled": false}, {"name": "onPrev-click", "template": "function(e){console.log(e)}", "disabled": false}, {"name": "onNext-click", "template": "function(e){console.log(e)}", "disabled": false}]}, "onTab-click": {"type": "JSFunction", "value": "function(){this.clickBuTab.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "docllkosgra", "hidden": false, "title": "", "isLocked": false, "condition": false, "conditionGroup": "", "children": [{"componentName": "DmvTabPane", "id": "node_ocllkpolkb6f", "props": {"value": "all", "label": "全部"}, "docId": "docllkpolkb", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvTabPane", "id": "node_ocllkpolkb6h", "props": {"value": "xiuyu", "label": "休娱"}, "docId": "docllkpolkb", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvTabPane", "id": "node_ocllu9ow8b1", "props": {"value": "64788", "label": "综发"}, "docId": "docllu9oyoa", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvTabPane", "id": "node_ocllkpolkb6g", "props": {"value": "liren", "label": "丽人"}, "docId": "docllkpolkb", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvTabPane", "id": "node_ocllu9ow8b2", "props": {"value": "10201", "label": "LE"}, "docId": "docllu9oyoa", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvTabPane", "id": "node_ocllu9ow8b3", "props": {"value": "20861", "label": "商运"}, "docId": "docllu9oyoa", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvTabPane", "id": "node_ocllu9ow8b4", "props": {"value": "78854", "label": "医疗"}, "docId": "docllu9oyoa", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvTabPane", "id": "node_ocllu9ow8b5", "props": {"value": "41596", "label": "医美"}, "docId": "docllu9oyoa", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}, {"componentName": "DmvGridCol", "id": "node_oclllq9ggq5", "props": {"span": 2, "style": {"display": "flex", "flexDirection": "row-reverse", "justifyContent": "space-between"}, "ref": "dmvgridcol-fe5ad8cb"}, "docId": "doclllq9ggq", "hidden": false, "title": "", "isLocked": false, "condition": false, "conditionGroup": "", "children": [{"componentName": "DmvButton", "id": "node_ocllkv3mqf1n", "props": {"type": "primary", "children": "主按钮", "text": "业务配置", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "style": {"display": "flex", "alignItems": "center", "flexDirection": "row-reverse"}, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "clickBuManage"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.clickBuManage.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "ref": "dmvbutton-e0f531d5"}, "docId": "docllkv417s", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}, {"componentName": "DmvGridCol", "id": "node_oclllq9ggq6", "props": {"span": 2, "style": {"display": "flex", "flexDirection": "row-reverse", "justifyContent": "space-between"}, "ref": "dmvgridcol-809b181a", "_unsafe_MixedSetter____condition____select": "BoolSetter"}, "docId": "doclllq9ggq", "hidden": false, "title": "", "isLocked": false, "condition": false, "conditionGroup": ""}]}, {"componentName": "DmvContainer", "id": "node_oclldr7c0p6", "props": {"direction": "horizontal", "ref": "dmvcontainer-69232632", "style": {"marginTop": "10px", "marginBottom": "10px", "marginLeft": "8px", "marginRight": "20px"}, "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "docId": "doclldr7n01", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.state.isFuture", "mock": true}, "conditionGroup": "", "children": [{"componentName": "DmvContainer", "id": "node_oclluk2fn51", "props": {"direction": "horizontal", "ref": "dmvcontainer-621c88fd"}, "docId": "docllkt70gq", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvButton", "id": "node_ocllkt70gqdk", "props": {"type": "text", "children": "主按钮", "text": "批量在岗", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": {"type": "JSExpression", "value": "this.state.tableSelection.length == 0", "mock": false}, "ref": "dmvbutton-b2c9dcf3", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "batchChangeWorkStatus", "paramStr": "{\n \t \"op\": \"work\"\n}"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.batchChangeWorkStatus.apply(this,Array.prototype.slice.call(arguments).concat([{\n \t \"op\": \"work\"\n}])) }"}}, "docId": "docllkt70gq", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvButton", "id": "node_ocllkt70gqe0", "props": {"type": "text", "children": "主按钮", "text": "批量休息", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": {"type": "JSExpression", "value": "this.state.tableSelection.length == 0", "mock": false}, "ref": "dmvbutton-c8d7047c", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "batchChangeWorkStatus", "paramStr": "{\n \t \"op\":\"rest\" \n}"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.batchChangeWorkStatus.apply(this,Array.prototype.slice.call(arguments).concat([{\n \t \"op\":\"rest\" \n}])) }"}}, "docId": "docllkt70gq", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}, {"componentName": "DmvContainer", "id": "node_oclmhn8rbw1", "props": {"direction": "horizontal", "style": {"display": "flex", "flexDirection": "row", "justifyContent": "flex-end"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvButton", "id": "node_ocllkv3mqf1o", "props": {"type": "", "children": "主按钮", "text": "添加人员", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "style": {}, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "clickAddBtn"}], "eventList": [{"name": "onClick", "template": "", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.clickAddBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "ref": "dmvbutton-5caea7e5", "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "docId": "docllkv417s", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.state.isFuture", "mock": true}, "conditionGroup": ""}]}]}, {"componentName": "DmvContainer", "id": "node_oclm64l4mq1", "props": {"direction": "horizontal", "ref": "dmvcontainer-621c88fd", "style": {"marginLeft": "20px", "marginRight": "20px", "display": "flex", "flexDirection": "column"}}, "docId": "docllkt70gq", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "VpdmProTable", "id": "node_oclm64l4mq2", "props": {"listParams": {"listType": "listProps", "listPropsType": "props", "listProps": {"request": {"method": "post", "url": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/queryUser`", "mock": ""}, "responseMap": {"dataSource": "data.records", "total": "data.total"}, "params": {"type": "JSExpression", "value": "this.state.searchFilter"}, "requestMap": {"currentPage": "page"}}, "autoSearch": false, "searchWatchProp": {"type": "JSExpression", "value": "this.state.controlQuery"}, "afterReload": {"type": "JSFunction", "value": "function(){ return this.afterReload.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "requestFailed": {"type": "JSFunction", "value": "function(){ return this.requestFailed.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}}, "columnArray": [{"label": "姓名", "prop": "nameMis", "content": [{"contextProp": "userName", "type": "text", "suffix": "/", "tipEnabled": false}, {"contextProp": "userMis", "type": "text"}], "width": "100", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}, {"label": "身份", "width": "150", "overflowType": "不设置", "content": [{"type": "text", "contextProp": "roleDesc", "tipEnabled": false}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "roleDesc"}, {"label": "分单日期", "prop": "<PERSON><PERSON><PERSON>", "content": [{"contextProp": "<PERSON><PERSON><PERSON>", "type": "text", "formatter": "YYYY-MM-DD", "tipEnabled": false, "tip": ""}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "设置日单量", "sortable": false, "prop": "planCount", "width": "", "content": [{"contextProp": "planCount", "type": "amount"}], "filterable": false, "resizable": false, "slot": false, "header-align": "center", "align": "center"}, {"label": "是否需质检", "content": [{"contextProp": "needInspection", "type": "obj", "formatter": {"true": "是", "false": "否"}, "objClass": {"0": "styleA", "1": "styleB", "2": "styleC", "6": "styleD"}, "tipEnabled": false}], "resizable": false, "slot": false, "width": "", "header-align": "center", "align": "center", "sortable": false, "filterable": false, "prop": "needInspection"}, {"label": "当日分单量", "tip": "", "width": "", "content": [{"contextProp": "assignCount", "type": "amount", "formatter": "YYYY/MM/DD HH:mm:ss"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "assignCount"}, {"label": "当日完成量", "tip": "", "width": "", "content": [{"contextProp": "finishCount", "type": "amount", "formatter": "YYYY/MM/DD HH:mm:ss", "anthorProp": "endTime", "dividerline": "~"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "finishCount"}, {"label": "当日未完成量", "width": "", "content": [{"contextProp": "todoCount", "type": "amount"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "todoCount"}, {"label": "当日存疑量", "width": "", "content": [{"contextProp": "questionCount", "type": "amount", "url": "https://dev.sankuai.com/code/home", "target": "_blank", "params": ["creator", "endTime"]}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "questionCount"}, {"label": "在岗状态", "prop": "onDuty", "content": [{"contextProp": "onDuty", "type": "switch", "tipEnabled": false, "tip": ""}], "width": "100", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}], "pagination": {"enabled": true, "showQuickJumper": true, "showSizeChanger": true, "showTotal": true, "currentPage": 1, "pageSize": 5, "size": "不设置", "pager-count": 7}, "actionColumn": {"enabled": true, "visibleButtonCount": 1, "styleType": "text-primary", "width": "120", "labelClass": "opColHeader", "buttonArray": [{"label": "编辑", "eventType": "func", "url": "https://dev.sankuai.com/code/home", "target": "_blank", "type": "text", "buttonClass": "opBtn", "func": {"type": "JSFunction", "value": "function(){ return this.clickEdit.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "size": "large"}, {"label": "删除", "eventType": "func", "type": "text", "func": {"type": "JSFunction", "value": "function(){ return this.clickDelete.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "size": "large", "buttonClass": "opBtn"}], "_unsafe_MixedSetter_buttonArray_select": "ArraySetter", "className": ""}, "table": {"size": "不设置", "showHeader": true, "horizontalVirtual": false, "emptyText": "暂无数据", "loadingMessage": "正在加载中", "rowClass": "", "cellClass": "", "headerRowClass": "", "headerCellClass": "", "orderEnabled": false, "checkboxEnabled": true, "expandEnabled": false, "striped": false, "bordered": false, "reserveSelection": true, "showSummary": false, "sumText": "合计", "orderWidth": "", "indexOfSelection": {"type": "JSFunction", "value": "function(){ return this.indexOfSelection.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "style": {"marginLeft": "0px", "width": "100%"}, "ref": "labelPeopleTable222", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onSwitchChange", "relatedEventName": "changeWorkStatus"}], "eventList": [{"name": "onSelect", "template": "function(selection,row){}", "disabled": false}, {"name": "onSelect-all", "template": "function(selection){}", "disabled": false}, {"name": "onSwitchChange", "template": "function(val,row){}", "disabled": true}, {"name": "onInputChange", "template": "function(val,row){}", "disabled": false}]}, "onChange": {"type": "JSFunction", "value": "function(){this.onTableChange.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "selection": {"type": "JSExpression", "value": "this.state.tableSelection"}, "_unsafe_MixedSetter____condition____select": "VariableSetter", "cell-default-10": "", "onSwitchChange": {"type": "JSFunction", "value": "function(){this.changeWorkStatus.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "doclldr7n01", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.state.isFuture", "mock": true}, "conditionGroup": ""}, {"componentName": "VpdmProTable", "id": "node_oclluk2fn52", "props": {"listParams": {"listType": "listProps", "listPropsType": "props", "listProps": {"request": {"method": "post", "url": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/queryUser`", "mock": "https://yapi.sankuai.com/mock/35239/people/label"}, "responseMap": {"dataSource": "data.records", "total": "data.total"}, "params": {"type": "JSExpression", "value": "this.state.searchFilter"}, "requestMap": {"currentPage": "page"}}, "autoSearch": false, "searchWatchProp": {"type": "JSExpression", "value": "this.state.controlQuery"}, "afterReload": {"type": "JSFunction", "value": "function(){ return this.afterReload.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "requestFailed": {"type": "JSFunction", "value": "function(){ return this.requestFailed.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}}, "columnArray": [{"label": "姓名", "prop": "nameMis", "content": [{"contextProp": "userName", "type": "text", "suffix": "/", "tipEnabled": false, "contentClass": "horizontalCenter"}, {"contextProp": "userMis", "type": "text"}], "width": "120", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}, {"label": "身份", "width": "150", "overflowType": "不设置", "content": [{"type": "text", "contextProp": "roleDesc"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "roleDesc"}, {"label": "分单日期", "prop": "<PERSON><PERSON><PERSON>", "content": [{"contextProp": "<PERSON><PERSON><PERSON>", "type": "text", "formatter": "YYYY-MM-DD", "tipEnabled": false, "tip": ""}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "设置日单量", "sortable": false, "prop": "planCount", "width": "", "content": [{"contextProp": "planCount", "type": "amount"}], "filterable": false, "resizable": false, "slot": false, "header-align": "center", "align": "center"}, {"label": "是否需质检", "content": [{"contextProp": "needInspection", "type": "obj", "formatter": {"true": "是", "false": "否"}, "objClass": {"0": "styleA", "1": "styleB", "2": "styleC", "6": "styleD"}, "tipEnabled": false}], "resizable": false, "slot": false, "width": "", "header-align": "center", "align": "center", "sortable": false, "filterable": false, "prop": "needInspection"}, {"label": "当日分单量", "tip": "", "width": "", "content": [{"contextProp": "assignCount", "type": "amount", "formatter": "YYYY/MM/DD HH:mm:ss"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "assignCount"}, {"label": "当日完成量", "tip": "", "width": "", "content": [{"contextProp": "finishCount", "type": "amount", "formatter": "YYYY/MM/DD HH:mm:ss", "anthorProp": "endTime", "dividerline": "~"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "finishCount"}, {"label": "当日未完成量", "width": "", "content": [{"contextProp": "todoCount", "type": "amount"}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "todoCount"}, {"label": "当日存疑量", "width": "", "content": [{"contextProp": "questionCount", "type": "amount", "url": "https://dev.sankuai.com/code/home", "target": "_blank", "params": ["creator", "endTime"]}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "questionCount"}, {"label": "在岗状态", "prop": "onDuty", "content": [{"contextProp": "onDuty", "type": "obj", "formatter": {"true": "在岗", "false": "休息"}, "tipEnabled": false, "tip": ""}], "width": "100", "header-align": "center", "align": "center", "sortable": false, "filterable": false, "resizable": false, "slot": false}], "pagination": {"enabled": true, "showQuickJumper": true, "showSizeChanger": true, "showTotal": true, "currentPage": 1, "pageSize": 5, "size": "不设置", "pager-count": 7}, "actionColumn": {"enabled": false, "visibleButtonCount": 1, "styleType": "text-primary", "width": "120", "labelClass": "opColHeader", "buttonArray": [{"label": "编辑", "eventType": "func", "url": "https://dev.sankuai.com/code/home", "target": "_blank", "type": "text", "buttonClass": "opBtn", "func": {"type": "JSFunction", "value": "function(){ return this.clickEdit.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "size": "large"}, {"label": "删除", "eventType": "func", "type": "text", "func": {"type": "JSFunction", "value": "function(){ return this.clickDelete.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "size": "large", "buttonClass": "opBtn"}], "_unsafe_MixedSetter_buttonArray_select": "ArraySetter"}, "table": {"size": "不设置", "showHeader": true, "horizontalVirtual": false, "emptyText": "暂无数据", "loadingMessage": "正在加载中", "rowClass": "", "cellClass": "", "headerRowClass": "", "headerCellClass": "", "orderEnabled": false, "checkboxEnabled": false, "expandEnabled": false, "striped": false, "bordered": false, "reserveSelection": true, "showSummary": false, "sumText": "合计"}, "style": {"width": "100%", "marginTop": "10px"}, "ref": "labelPeopleTable", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onSwitchChange", "relatedEventName": "changeWorkStatus"}], "eventList": [{"name": "onSelect", "template": "function(selection,row){}", "disabled": false}, {"name": "onSelectAll", "template": "function(selection){}", "disabled": true}, {"name": "onSwitchChange", "template": "function(val,row){}", "disabled": true}, {"name": "onInputChange", "template": "function(val,row){}", "disabled": false}]}, "onChange": {"type": "JSFunction", "value": "function(){this.onTableChange.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "onSwitchChange": {"type": "JSFunction", "value": "function(){this.changeWorkStatus.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "selection": {"type": "JSExpression", "value": "this.state.tableSelection"}, "_unsafe_MixedSetter____condition____select": "VariableSetter", "cell-default-10": ""}, "docId": "doclldr7n01", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "!this.state.isFuture", "mock": true}, "conditionGroup": ""}]}]}