<template>
  <div id="inspection-people-manage-container" class="inspection-people-manage-container">
    <div class="">
      <div id="ele"></div>
    </div>
  </div>
</template>
<script>
import psb from '@nibfe/platform-sdk'
import { REDIRECT_URL } from '@/lib/constants'
import graphCfg from '@nibfe/ccc-lowcode-render'

export default {
  components: {},
  data() {
    return {
      pageMessage: '',
    }
  },
  created() {
    psb.config('16947796803mlx20', {
      masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
      redirectUrl: process.env.NODE_ENV === 'development' ? null : REDIRECT_URL,
    })
    graphCfg({
      el: '#ele',
      tenantId: 3,
      pageType: 'chart', // 图表类型
      remoteURL: process.env.VUE_APP_API_HOST,
      module: 'inspection-people-manage',
      appEnv: process.env.VUE_APP_ENV,
      templateId: process.env.VUE_APP_ENV === 'production' ? 175 : 2039,
      modelId: process.env.VUE_APP_ENV === 'production' ? 268 : 1410,
      initParams: {
        terminal: 0, //来自于PC，必填
        planSource: 0, //非来自拜访创建的，建议填0
      },
      // mockInfo: {
      //   dslJson: schema,
      //   variable: {},
      // },
    }).then(params => {
      const { instance, eventBus } = params || {}
      this.$bus = eventBus
      eventBus.$on('toOut', res => {
        this.pageMessage = res
      })
    })
  },
  methods: {
    // sendMessage() {
    //   this.$bus.$emit('toInner', '我是来自外部的消息' + Math.random())
    // },
  },
}
</script>

<style lang="scss" scoped>
::v-deep.inspection-people-manage-container {
  min-width: 1100px;
}
</style>
