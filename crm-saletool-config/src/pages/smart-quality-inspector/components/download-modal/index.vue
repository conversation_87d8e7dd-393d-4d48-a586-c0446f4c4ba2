<template>
  <mtd-modal v-model="visible" title="已提交下载任务成功" class="record-review-modal">
    <div class="downloadModal-content-container">
      <div class="downloadModal-content">
        可点击【查看】按钮或从【仪表盘-下载列表页】查看下载进度
      </div>
      <div class="downloadModal-content-button">
        <mtd-button class="cancel-button" @click="visible = false">取消</mtd-button>
        <mtd-button type="primary" class="redirect-button" @click="redirect">查看</mtd-button>
      </div>
    </div>
  </mtd-modal>
</template>

<script>
import { defineComponent } from '@vue/composition-api'

export default defineComponent({
  name: 'DownloadModal',
  data() {
    return {
      visible: false,
    }
  },
  methods: {
    open() {
      this.visible = true
    },
    redirect() {
      window.open(encodeURI('/dataexport/#/downloadList'))
    },
  },
})
</script>

<style lang="scss" scoped>
.downloadModal-content-container {
  .downloadModal-content {
    font-weight: 400;
    font-size: 14px;
    color: #000000;
    margin-bottom: 57px;
  }
  .downloadModal-content-button {
    display: flex;
    justify-content: flex-end;
  }
  .cancel-button {
    margin-top: 10px;
  }
  .redirect-button {
    margin-top: 10px;
    margin-left: 16px;
  }
}
</style>
