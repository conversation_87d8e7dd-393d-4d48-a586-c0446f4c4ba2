<template>
  <div class="comment-picker">
    <!-- 展示 -->
    <div v-if="type === TYPE.VIEW" class="view">
      <div class="view-title">
        <div class="view-title-user">
          <span class="view-title-text">评论</span>
          <img :src="getAvatar(userInfo)" alt="" />
          <mtd-tooltip placement="top" :content="`${userInfo.name}/${userInfo.mis}`">
            <span class="view-title-user-info"> {{ userInfo.name }}/{{ userInfo.mis }} </span>
          </mtd-tooltip>
          <span class="vertical-line"></span>
          <span>{{ comment.commentTime | handleTime }}</span>
        </div>
        <a v-if="canEdit" href="javascript:;" @click="handleEdit">编辑</a>
      </div>
      <div class="view-content">
        <div class="view-content-evaluate">
          <div v-for="link in hitLinks" :key="link.linkId" class="view-content-evaluate-item">
            <mtd-tag :theme="HIT_TAG_STYLE[link.markId]" size="small" type="ghost">
              {{ evaluatEnum.find(item => item.id === link.markId).desc }}
            </mtd-tag>
            <span>{{ link.linkName }}</span>
          </div>
        </div>
        <div class="view-content-comment">
          <span class="view-content-comment-label">评论：</span>
          <span>{{ comment.comment }}</span>
        </div>
      </div>
    </div>
    <!-- 编辑 -->
    <div v-show="type === TYPE.EDIT" class="edit">
      <div class="edit-title">请评价该会话</div>

      <FormProvider :form="form">
        <mtd-form :label-width="0">
          <Field
            name="sceneLinks"
            title="命中子环节:"
            required
            :component="[
              Cascader,
              {
                placeholder: '请选择',
                size: 'small',
                clearable: true,
                multiple: true,
                maxCount: 1,
                checkedStrategy: 'children',
                popperClass: 'comment-cascader',
              },
            ]"
            :decorator="[FormItem, { labelWidth: 80 }]"
          />

          <VoidField
            name="visibleSubLink"
            :component="['div']"
            :decorator="['div', { class: 'sub-link' }]"
          >
            <ArrayField name="subLink" :decorator="[FormItem]">
              <template #default="{ field }">
                <div v-for="(item, index) in field.value" :key="item.linkId" class="sub-link-item">
                  <mtd-tooltip placement="top" :content="item.linkName">
                    <span class="sub-link-item-name">{{ item.linkName }}: </span>
                  </mtd-tooltip>
                  <Field
                    :name="`${index}.markId`"
                    required
                    :data-source="evaluatEnum"
                    :component="[ScoreTag]"
                    :decorator="[FormItem]"
                  />
                  <span class="sub-link-item-close" @click="closeLink(item)">删除</span>
                </div>
              </template>
            </ArrayField>
          </VoidField>

          <Field
            name="comment"
            required
            :component="[
              Textarea,
              { placeholder: '请输入评论', autosize: { minRows: 2, maxRows: 4 }, maxLength: 500 },
            ]"
            :decorator="[FormItem]"
          />

          <div class="actions">
            <mtd-button size="small" @click="handleClose">取消</mtd-button>
            <mtd-button size="small" type="primary" :loading="loading" @click="submit">
              确认
            </mtd-button>
          </div>
        </mtd-form>
      </FormProvider>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { defineComponent } from '@vue/composition-api'
import { FormItem, Cascader, Textarea } from '@nibfe/dz-form-mtd-vue2'
import { onFieldInitAndValueChange } from '@/lib/form-hooks'
import { createForm } from '@nibfe/dz-form-core'
import {
  FormProvider,
  Field,
  ArrayField,
  VoidField,
  connect,
  mapProps,
} from '@nibfe/dz-form-render'
import avatar from '@/assets/<EMAIL>'
import { cloneDeep, isEqualWith, flatten } from 'lodash'
import { errorReportOnly } from '@/lib/utils'

import API from '../../api/index'
import { ScoreTag } from '../index'
import { QUALITY_ENUMS, HIT_TAG_STYLE } from '../../lib/constant'

const TYPE = {
  VIEW: 1,
  EDIT: 2,
}

export default defineComponent({
  name: 'CommentPicker',
  components: {
    FormProvider,
    ArrayField,
    Field,
    VoidField,
  },
  filters: {
    handleTime(timestamp) {
      return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm') : '-'
    },
  },
  props: {
    hitLinks: {
      type: Array,
      default: () => [],
    },
    comment: {
      type: Object,
      default: null,
    },
    sceneLinks: {
      type: Array,
      default: () => [],
    },
    enums: {
      type: Array,
      default: () => [],
    },
    sequenceId: {
      type: [Number, String],
      default: null,
    },
    lx: {
      type: Object,
      default: null,
    },
    canEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const that = this

    const form = createForm({
      effects() {
        onFieldInitAndValueChange('sceneLinks', ({ value, form }) => {
          try {
            const oldSubLink = form.getFieldState('subLink')?.value || []
            const subLink = cloneDeep(that.handleSubLinkById(value || []))

            form.setFieldState('subLink', {
              value: subLink.map(item => {
                return oldSubLink.find(sub => sub.linkId === item.linkId) || item
              }),
            })
            form.setFieldState('visibleSubLink', state => {
              state.display = subLink.length > 0 ? 'visible' : 'hidden'
            })
          } catch (error) {
            //
          }
        })
        onFieldInitAndValueChange('subLink', ({ form }) => {
          form.setFieldState('subLink.*.markId', state => {
            state.dataSource = that.evaluatEnum
          })
        })
      },
    })

    return {
      Cascader,
      FormItem,
      Textarea,
      ScoreTag: connect(ScoreTag, mapProps({ dataSource: true })),
      TYPE,
      HIT_TAG_STYLE,
      defaultAvatar: avatar,
      type: TYPE.EDIT,
      form,
      loading: false,
    }
  },
  computed: {
    userInfo() {
      return this.comment?.user || {}
    },
    evaluatEnum() {
      return (this.enums || []).find(item => item.id === QUALITY_ENUMS.HIT)?.subResultEnums || []
    },
  },
  watch: {
    sceneLinks: {
      deep: true,
      immediate: true,
      handler(val) {
        this.form.setFieldState('sceneLinks', {
          dataSource: val || [],
        })
      },
    },
    comment: {
      deep: true,
      immediate: true,
      handler(val) {
        this.type = val ? TYPE.VIEW : TYPE.EDIT
      },
    },
  },
  methods: {
    // 获取头像
    getAvatar(data) {
      return data?.avatar || this.defaultAvatar
    },
    // 根据级联选中id 获取对应的环节数据
    handleSubLinkById(multIds = []) {
      const subLinks = []

      const recursion = (arr = [], linkIds = [], level = 0) => {
        arr?.forEach(item => {
          if (linkIds.includes(item.value)) {
            if (item?.children?.length) {
              recursion(item.children, linkIds, level + 1)
            } else {
              subLinks.push({
                ...item,
                hitLink: true,
                linkIds,
                markId: this.hitLinks?.find(it => it.linkId === item.value)?.markId,
              })
            }
          }
        })
      }

      multIds.forEach(item => {
        recursion(this.sceneLinks, item)
      })

      return subLinks
    },
    // 编辑
    handleEdit() {
      this.type = TYPE.EDIT
      this.form.setFieldState('sceneLinks', {
        dataSource: this.sceneLinks || [],
      })
      this.$nextTick(() => {
        const sceneLinks = cloneDeep(
          this.hitLinks.filter(item => item?.linkIds?.length).map(item => item.linkIds) || [],
        )
        this.form.setValues({
          comment: this.comment?.comment,
          sceneLinks,
        })
      })
    },
    // 删除子环节
    closeLink(data) {
      const sceneLinks = this.form.getFieldState('sceneLinks').value
      const index = sceneLinks?.findIndex(it => isEqualWith(it, data.linkIds))
      index > -1 && sceneLinks.splice(index, 1)
      this.form.setFieldState('sceneLinks', { value: sceneLinks })
    },
    // 取消
    handleClose() {
      if (this.comment) {
        this.form.reset()
        this.type = TYPE.VIEW
      } else {
        this.$emit('close')
      }
    },
    // 处理参数
    handleParams(value) {
      const { comment, subLink } = value

      const links = cloneDeep(this.sceneLinks)

      const filterLink = (arr = [], linkIds = []) => {
        return arr
          .filter(item => linkIds.includes(item.value))
          .map(item => {
            if (item?.children?.length) {
              item.subLinks = filterLink(item?.children || [], linkIds)
            } else {
              const findSubLink = subLink.find(sub => sub.linkId === item.value)
              item.mark = {
                markId: findSubLink?.markId,
                linkId: findSubLink?.linkId,
                idPath: [QUALITY_ENUMS.HIT, findSubLink?.markId],
              }
            }
            item.hitType = 2
            item.hitLink = true
            delete item.subLinkInfos
            delete item.children
            delete item.value
            delete item.label
            return item
          })
      }

      return {
        sequenceId: this.sequenceId,
        comment: { comment },
        links: flatten(
          filterLink(links, flatten(subLink.map(it => it.linkIds))).map(it => it?.subLinks || []),
        ),
      }
    },
    // 提交
    async submit() {
      const value = await this.form.submit().catch(() => null)
      if (!value) return

      this.lx?.mc(this.comment ? 'b_gc_m_1kfvxy1b_mc' : 'b_gc_m_0dqwo3tv_mc')
      this.loading = true
      const params = {
        callContents: [this.handleParams(value)],
        dialogId: this.$route.query?.dialogId ? Number(this.$route.query.dialogId) : undefined,
      }

      try {
        await API.saveCallContent(params)
        this.$mtd.message.success('评论成功')
        this.type = TYPE.VIEW
        this.$emit('refresh')
        this.loading = false
      } catch (error) {
        this.loading = false
        errorReportOnly(error)
      }
    },
  },
})
</script>

<style lang="scss">
.comment-cascader {
  .mtd-cascader-menu-item {
    .mtd-cascader-menu-item-checkbox {
      display: inline-block; // 默认显示复选框
    }

    //产品不希望父节点的复选框实现全选功能，因此隐藏掉父节点的复选框。
    &:has(.mtd-cascader-menu-item-expand-icon) .mtd-cascader-menu-item-checkbox {
      display: none;
    }
  }
}
</style>
<style lang="scss" scoped>
.comment-picker {
  width: 300px;
  border-radius: 6px;
  background: #fff;

  .view {
    width: 100%;
    height: 100%;

    &-title {
      width: 100%;
      height: 36px;
      padding: 0 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #f0f0f0;
      font-size: 12px;
      color: #999;

      &-text {
        font-size: 14px;
        color: #222;
        font-weight: 500;
        margin-right: 8px;
      }

      &-user {
        flex: 1;
        display: flex;
        align-items: center;

        img {
          margin-right: 4px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          border: 0.5px solid #e8e8e8;
        }

        &-info {
          max-width: 70px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    &-content {
      padding: 8px 12px 12px;
      font-size: 14px;
      color: #222;

      &-evaluate {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 8px;
        border-radius: 6px;
        background: #f8f8f8;
      }

      &-evaluate-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;
        line-height: normal;
      }

      &-comment {
        margin-top: 8px;
        display: flex;
        align-items: flex-start;

        &-label {
          min-width: 42px;
        }
      }
    }
  }

  .edit {
    width: 100%;
    height: 100%;
    padding: 8px 12px;

    &-title {
      height: 20px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #222;
      font-weight: 500;
      margin-bottom: 12px;
    }

    .sub-link {
      width: 100%;
      padding: 12px;
      margin-bottom: 12px;
      border-radius: 6px;
      background: #f8f8f8;

      ::v-deep .formily-mtd-form-item {
        margin-bottom: 0;

        .mtd-form-item-content {
          min-height: auto;
          line-height: normal;
        }

        .formily-mtd-form-item-control-content {
          display: flex;
          flex-direction: column;
          gap: 8px;
          line-height: normal;
        }
      }

      &-item {
        width: 100%;
        display: flex;
        align-items: flex-start;
        font-size: 12px;
        color: #666;

        &-name {
          margin-right: 8px;
          min-width: 76px;
          max-width: 76px;
          line-height: 24px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: right;
        }

        &-close {
          flex: 1;
          text-align: right;
          line-height: 24px;
          cursor: pointer;
        }
      }
    }

    .actions {
      display: flex;
      justify-content: flex-end;
      gap: 4px;
    }
  }

  ::v-deep .mtd-form {
    .mtd-form-item {
      margin-bottom: 12px;

      .mtd-form-item-label {
        height: 32px;
        line-height: 32px;
        font-size: 12px;
        color: #666;

        &::before {
          content: none;
        }
      }

      .mtd-cascader {
        display: block;
        width: 100%;

        .mtd-multiple-select-choices {
          flex-wrap: nowrap;
          gap: 5px;

          .mtd-select-choice {
            max-width: calc(100% - 20px);
          }
        }
      }

      .mtd-textarea-wrapper {
        width: 100%;

        textarea {
          resize: none;
        }
      }
    }
  }

  .vertical-line {
    margin: 0 4px;
    width: 1px;
    height: 10px;
    background-color: #e5e5e5;
  }
}
</style>
