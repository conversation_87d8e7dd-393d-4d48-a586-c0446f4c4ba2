<template>
  <span class="score-tag">
    <mtd-tag
      v-for="(tag, index) in dataSource"
      :key="index"
      :class="{ 'active-tag': tag.id === value }"
      type="ghost"
      @click="handleTagClick(tag)"
    >
      {{ tag.desc }}
    </mtd-tag>
  </span>
</template>

<script>
export default {
  name: 'ScoreTag',
  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },
    value: {
      type: [Number, String],
      default: '',
    },
  },
  methods: {
    handleTagClick(tag) {
      if (tag.id === this.value) return
      this.$emit('change', tag.id)
    },
  },
}
</script>

<style lang="scss" scoped>
.score-tag {
  display: flex;
  gap: 8px;
}

.mtd-tag {
  color: #222;
  cursor: pointer;
}

.active-tag {
  border-color: var(--color-primary);
  background: #fff9de !important;
}
</style>
