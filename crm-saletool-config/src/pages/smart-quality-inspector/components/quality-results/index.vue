<template>
  <UniversalBox class="quality-inspection-results-container">
    <template #title>
      质检结果
      <div v-if="currentSceneData.updateTime">
        <img :src="getAvatar(currentSceneData.user)" alt="" />
        <span>{{ currentSceneData.user.name }}/{{ currentSceneData.user.mis }}</span>
        <span class="vertical-line"></span>
        <span>{{ currentSceneData.updateTime }}</span>
      </div>
    </template>
    <template #action>
      <div v-if="canEdit">
        <mtd-button
          v-if="isEditing"
          :loading="loading"
          style="background-color: #ffd100"
          @click="submitResult"
        >
          提交
        </mtd-button>
        <mtd-button v-else @click="editResult">编辑</mtd-button>
      </div>
    </template>
    <!-- 整体得分 -->
    <mtd-tabs v-model="activeName" show-line class="quality-inspection-results-tab">
      <mtd-tab-pane
        v-for="(item, index) in scenes"
        :key="index"
        :label="item.sceneName"
        :value="item.sceneId"
        class="quality-inspection-results-pane"
      >
        <div class="quality-inspection-results-scorll">
          <div class="quality-inspection-results-content">
            <div class="quality-title">
              <span class="sort">01</span>
              <span class="title">整体得分</span>
            </div>
            <mtd-table v-if="isEditing" :data="overAllDataSourceMap[item.sceneId]">
              <mtd-table-column label="打分项">
                <template #default>
                  <span>整体</span>
                </template>
              </mtd-table-column>
              <mtd-table-column align="right" label="AI质检分" width="134">
                <template #default="{ row }">
                  <ScoreResult
                    :value.sync="row.aiResult.idPath"
                    :quality-enums="qualityEnums"
                  ></ScoreResult>
                </template>
              </mtd-table-column>
              <mtd-table-column align="right" label="人工质检分" width="134">
                <template #default="{ row }">
                  <ScoreResult
                    :value.sync="row.personResult.idPath"
                    :quality-enums="qualityEnums"
                    :can-edit="true"
                  >
                  </ScoreResult>
                </template>
              </mtd-table-column>
            </mtd-table>
            <div v-else class="overall-results">
              <template v-for="(row, index) in overAllDataSourceMap[item.sceneId]">
                <div
                  :key="'ai-' + index"
                  :style="getAiStyle(item.sceneId)"
                  class="overall-results-ai"
                >
                  {{ getTagLabel(qualityEnums, row.aiResult.idPath, true) || '暂无结果' }}
                  <div class="overall-results-conten">AI质检分</div>
                </div>
                <span :key="'-' + index" class="overall-results-line"></span>
                <div
                  :key="'person-' + index"
                  :style="getPersonStyle(item.sceneId)"
                  class="overall-results-person"
                >
                  {{ getTagLabel(qualityEnums, row.personResult.idPath, true) || '暂无结果' }}
                  <div class="overall-results-conten">人工质检分</div>
                </div>
              </template>
            </div>
          </div>
          <!-- 思路环节 -->
          <div class="quality-title">
            <span class="sort">02</span>
            <span class="title">思路环节</span>
          </div>
          <RecursiveLinkTable
            row-key="aiLink.linkName"
            :data="thinkDataSourceMap[item.sceneId]"
            :editable="isEditing"
            :person-inspect-options="qualityEnums"
          />

          <!-- 违规红线 -->
          <div class="quality-title">
            <span class="sort">03</span>
            <span class="title">违规红线</span>
          </div>
          <mtd-table
            v-if="lodashGet(violationDataSourceMap, `${item.sceneId}[0].subLinks`)"
            :data="violationDataSourceMap[item.sceneId][0].subLinks"
          >
            <mtd-table-column label="打分项">
              <template #default="{ row: subLink }">
                <mtd-tooltip :content="subLink.personLink.desc" trigger="hover" placement="top">
                  <div class="quality-desc-ellipsis">{{ subLink.personLink.desc }}</div>
                </mtd-tooltip>
              </template>
            </mtd-table-column>
            <mtd-table-column align="right" label="AI质检分" width="100">
              <template #default="{ row }">
                <ScoreResult
                  :value.sync="row.aiLink.result.idPath"
                  :quality-enums="qualityEnums"
                ></ScoreResult>
              </template>
            </mtd-table-column>
            <mtd-table-column align="right" label="人工质检分" width="134">
              <template #default="{ row }">
                <ScoreResult
                  :value.sync="row.personLink.result.idPath"
                  :quality-enums="violationEnums"
                  :can-edit="isEditing"
                ></ScoreResult>
              </template>
            </mtd-table-column>
          </mtd-table>
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </UniversalBox>
</template>
<script>
import _ from 'lodash'
import avatar from '@/assets/<EMAIL>'
import API from '../../api'
import { UniversalBox } from '@/components'
import RecursiveLinkTable from '../recursive-link-table/index.vue'
import ScoreResult from './score-result.vue'
import { errorReportOnly } from '@/lib/utils'
import { OVERALL_STYLE, TAG_COLOR } from '../../lib/constant'
import { getTagLabel, getPerosonTag, processLinks, mergePersonLinks } from '../../lib/utils'

export default {
  name: 'QualityResults',
  components: {
    UniversalBox,
    RecursiveLinkTable,
    ScoreResult,
  },
  props: {
    scenes: {
      type: Array,
      default: () => [],
    },
    lx: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      lodashGet: _.get,
      defaultAvatar: avatar,
      dialogId: Number(this.$route.query.dialogId),
      user: {},
      loading: false,
      canEdit: false,
      isEditing: false, //当前编辑状态
      activeName: '',
      OVERALL_STYLE,
      TAG_COLOR,
      resultData: [],
      dataSource: {},
      overAllDataSourceMap: {},
      thinkDataSourceMap: {},
      violationDataSourceMap: {},
      qualityEnums: [],
      violationEnums: [],
    }
  },
  computed: {
    //编辑人信息
    currentSceneData() {
      const sceneData = this.overAllDataSourceMap[this.activeName]
      return sceneData ? sceneData[0] : sceneData || {}
    },
  },
  watch: {
    scenes: {
      handler: 'getDefaultScene',
      immediate: true,
    },
  },
  created() {
    this.getDefaultScene()
    this.getQualityEnumsInfo()
    this.getViolationEnumsInfo()
  },
  methods: {
    // 获取标签文案
    getTagLabel,
    getPerosonTag,
    //质检场景
    getDefaultScene() {
      if (this.scenes && this.scenes.length > 0) {
        this.activeName = this.scenes[0].sceneId
      }
      this.getData()
    },
    //质检枚举
    getQualityEnumsInfo() {
      API.getCallQualityEnums()
        .then(res => {
          const data = res.data.results || {}
          this.qualityEnums = data
        })
        .catch(errorReportOnly)
    },
    //质检枚举-违规红线
    getViolationEnumsInfo() {
      API.getCallQualityViolationEnums()
        .then(res => {
          const data = res.data.results || {}
          this.violationEnums = data
        })
        .catch(errorReportOnly)
    },
    //质检结果数据
    getData() {
      if (!this.scenes || this.scenes.length === 0) return
      this.thinkDataSourceMap = {}
      this.violationDataSourceMap = {}
      return API.queryQualityResult({
        dialogId: this.dialogId,
        sceneIds: this.scenes.map(item => item.sceneId),
      })
        .then(res => {
          const data = res.data || null
          if (!data) return
          if (!data.results || data.results.length === 0) return
          this.canEdit = data.canEdit
          this.isEditing = false
          this.resultData = data.results

          this.resultData.forEach(sceneResult => {
            const sceneId = sceneResult.scene.sceneId
            const initializedResult = {
              ...sceneResult,
              aiResult: {
                ...sceneResult.aiResult,
                idPath: sceneResult.aiResult?.idPath || [],
              },
              personResult: {
                ...sceneResult.personResult,
                idPath: sceneResult.personResult?.idPath || [],
              },
            }
            if (!this.overAllDataSourceMap[sceneId]) {
              this.$set(this.overAllDataSourceMap, sceneId, [initializedResult])
            } else {
              this.$set(this.overAllDataSourceMap[sceneId], 0, initializedResult)
            }

            this.dataSource[sceneId] = processLinks([sceneResult])

            function addDefaultValue(source) {
              const target = { ...source }
              if (!source?.personLink?.result?.idPath) {
                _.set(target, 'personLink.result.idPath', [])
              }
              if (!source?.aiLink?.result) {
                _.set(target, 'aiLink.result', {})
              }
              return target
            }
            this.dataSource[sceneId].forEach(item => {
              let modifiedItem = addDefaultValue(item)
              modifiedItem.subLinks = modifiedItem.subLinks.map(addDefaultValue)

              if (!item.aiLink.violationLink) {
                if (!this.thinkDataSourceMap[sceneId]) {
                  this.$set(this.thinkDataSourceMap, sceneId, [])
                }
                this.thinkDataSourceMap[sceneId].push(modifiedItem)
              } else {
                if (!this.violationDataSourceMap[sceneId]) {
                  this.$set(this.violationDataSourceMap, sceneId, [])
                }
                this.violationDataSourceMap[sceneId].push(modifiedItem)
              }
            })
          })
        })
        .catch(errorReportOnly)
    },

    getAvatar(data) {
      return data?.avatar || this.defaultAvatar
    },
    getAiStyle(sceneId) {
      const idPath = this.overAllDataSourceMap[sceneId][0]?.aiResult?.idPath || []
      const markId = idPath.length > 0 ? idPath[idPath.length - 1] : ''
      return this.OVERALL_STYLE[markId]
    },
    getPersonStyle(sceneId) {
      const idPath = this.overAllDataSourceMap[sceneId][0]?.personResult?.idPath || []
      const markId = idPath.length > 0 ? idPath[idPath.length - 1] : ''
      return this.OVERALL_STYLE[markId]
    },

    // 编辑质检信息
    editResult() {
      this.isEditing = true
      this.lx.mc('b_gc_m_ks0v09oo_mc')
    },
    handleParams() {
      const { overAllDataSourceMap, thinkDataSourceMap, violationDataSourceMap, dialogId } = this
      return {
        dialogId: dialogId,
        scene: { sceneId: this.activeName },
        personResult: overAllDataSourceMap[this.activeName][0].personResult,
        personLinks: mergePersonLinks(thinkDataSourceMap, violationDataSourceMap),
      }
    },
    submitResult() {
      const params = this.handleParams()

      if (!params) return
      this.lx.mc('b_gc_m_drq2blqh_mc')
      this.loading = true
      API.submitInspectionResult(params)
        .then(() => {
          this.$mtd.message.success('提交成功')
          this.isEditing = false
          this.getData()
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.quality-inspection-results-container {
  width: 40%;

  .quality-inspection-results-scorll {
    max-height: calc(100vh - 350px);
    overflow: auto;
  }

  ::v-deep {
    .universal-content {
      flex: none;
    }
    .mtd-tabs-bar {
      background-color: #ffd100;
    }

    .mtd-tabs-nocard .mtd-tabs-item.mtd-tab-active,
    .mtd-tabs-nocard .mtd-tabs-item.mtd-tab-active .mtd-picker-selected .mtd-picker-icon,
    .mtd-tabs-nocard .mtd-tabs-item.mtd-tab-active .mtd-tab-icon .mtd-picker-icon {
      font-size: 14px;
      font-weight: 500;
      color: black;
    }

    .mtd-input-wrapper {
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
      border-top: none;
      border-left: none;
      border-right: none;
      border-radius: 0px;
    }

    .universal-header {
      margin-top: -4px;

      .universal-title {
        display: flex;
        align-items: center;
      }

      span {
        font-size: 12px;
        color: #999;
      }

      .vertical-line {
        margin: 0 4px;
        width: 1px;
        height: 12px;
        background-color: #e5e5e5;
      }

      img {
        margin: 0 4px 0 8px;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        vertical-align: middle;
      }
    }

    .quality-inspection-results-tab {
      .mtd-tabs-nav {
        margin-bottom: 5px;
      }
    }

    .mtd-table {
      .mtd-table-header {
        border-radius: 6px;
        overflow: hidden;

        th {
          border-bottom: 0;
          background-color: #f8f8f8;
        }
      }

      .mtd-table-body {
        tr.hover > td,
        tr:hover > td {
          background-color: rgba($color: #f8f8f8, $alpha: 0.4);
        }
      }

      .deviation {
        margin-left: -25px;
      }

      .mtd-table-expand-icon {
        font-size: 20px;
      }
    }
  }

  .quality-title {
    margin: 18px 0 16px;
    font-size: 16px;

    &:first-of-type {
      margin-top: 0;
    }

    .sort {
      margin-right: 2px;
      font-family: Meituan Type, serif;
      font-size: 16px;
      font-weight: bold;
      color: var(--quality-sort-title-color);
    }

    .title {
      font-weight: 500;
    }
  }

  .overall-results {
    height: 77px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    font-weight: 500;
    border-radius: 10px;
    background: #f8f8f8;
    .overall-results-ai,
    .overall-results-person {
      flex: 1;
      text-align: center;
      position: relative;
    }

    .overall-results-line {
      margin: 0 4px;
      width: 1px;
      height: 40px;
      background-color: #e5e5e5;
    }

    .overall-results-conten {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }
  }

  .quality-desc-ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
  }

  .subrule-box {
    width: 100%;
    display: flex;
    flex-direction: column;

    .subrule-item {
      padding: 12px 0 12px 32px;
      flex: 1;
      display: flex;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: rgba($color: #f8f8f8, $alpha: 0.4);
      }

      .arrow {
        height: 22px;
        font-size: 20px;
        transition: transform 0.2s ease-in-out;
        color: rgba(0, 0, 0, 0.5);

        &.arrow-rotate {
          transform: rotate(90deg);
        }
      }

      .subrule-content {
        flex: 1;

        .subrule-title {
          font-size: 14px;
          display: flex;
        }
      }
    }
  }
}
</style>
