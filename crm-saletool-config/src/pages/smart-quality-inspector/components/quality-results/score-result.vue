<template>
  <mtd-cascader
    v-if="canEdit"
    v-model="localValues"
    :data="qualityEnums"
    genre="line"
    :field-names="{
      label: 'desc',
      value: 'id',
      children: 'subResultEnums',
    }"
  />
  <mtd-tag
    v-else-if="localValues.length > 0"
    type="ghost"
    :theme="getPerosonTag(localValues)"
    size="small"
  >
    {{ getTagLabel(qualityEnums, localValues) }}
  </mtd-tag>
  <span v-else>-</span>
</template>

<script>
import { getTagLabel, getPerosonTag } from '../../lib/utils'

export default {
  name: 'ScoreResult',
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    qualityEnums: {
      type: Array,
      default: () => [],
    },
    canEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      localValues: this.value,
    }
  },
  watch: {
    value: {
      handler(val) {
        this.localValues = val
      },
      deep: true,
    },
    localValues: {
      handler(val) {
        this.$emit('update:value', val)
      },
      deep: true,
    },
  },
  methods: {
    getTagLabel,
    getPerosonTag,
  },
}
</script>
