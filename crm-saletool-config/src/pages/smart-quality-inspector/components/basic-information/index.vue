<template>
  <!-- 基础信息 -->
  <UniversalBox :title="'基础信息'">
    <!-- 下载通话质检明细 -->
    <template #action>
      <mtd-button type="text" ghost class="downloadButton" @click="downloadDetailed">
        <mtd-icon name="mtdicon-download-o" />
        下载通话质检明细
      </mtd-button>
    </template>

    <!-- 基础信息内容 -->
    <div class="basic-information-content">
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">门店名称：</label>
        <div class="basic-information-content-value">{{ basicInformation.shopName }}</div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">门店ID：</label>
        <div class="basic-information-content-value">
          <span>
            {{ basicInformation.shopIdLong }}
            <ContentCopy :content="basicInformation.shopIdLong" />
          </span>
        </div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-call-label">商户姓名电话：</label>
        <div class="basic-information-content-call-value">
          <div
            v-for="(item, index) in filteredItemList"
            :key="index"
            class="basic-information-content-box"
          >
            <template>
              <span class="basic-information-content-call-name">{{ item.value[0] }} </span>
              <span>
                <PhoneNum :phone="item.value[1]" :plain-text="decryptHandler" />
              </span>
            </template>
          </div>
        </div>
      </div>
      <div v-if="basicInformation.sale" class="basic-information-content-box">
        <label class="basic-information-content-label">销售：</label>
        <div class="basic-information-content-value">
          {{ basicInformation.sale.employeeName }}/{{ basicInformation.sale.ad }}
        </div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">呼出时间：</label>
        <div class="basic-information-content-value">{{ basicInformation.callStartTime }}</div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">持续时间：</label>
        <div class="basic-information-content-value">{{ basicInformation.billingTime }}</div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">通话ID：</label>
        <div class="basic-information-content-value">
          <span>
            {{ basicInformation.accCallId }}
            <ContentCopy :content="basicInformation.accCallId" />
          </span>
        </div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">通话场景：</label>
        <div class="basic-information-content-value">{{ sceneNames }}</div>
      </div>
    </div>

    <DownloadModal ref="DownloadModalRef" />
  </UniversalBox>
</template>

<script>
import API from '../../api'
import { DownloadModal } from '../index'
import { ContentCopy, PhoneNum, UniversalBox } from '@/components'
import { errorReportOnly } from '@/lib/utils'

export default {
  name: 'BasicInformation',
  components: {
    UniversalBox,
    ContentCopy,
    PhoneNum,
    DownloadModal,
  },
  props: {
    basicInformation: {
      type: Object,
      default: () => ({}),
    },
    scenes: {
      type: Array,
      default: () => [],
    },
    lx: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      decryptTel: null,
    }
  },
  computed: {
    sceneNames() {
      if (!this.scenes || this.scenes.length === 0) {
        return '-'
      }
      return this.scenes.map(scene => scene.sceneName).join('、')
    },
    filteredItemList() {
      return this.basicInformation.itemList?.filter(item => item.type === 2) || []
    },
  },
  methods: {
    // 下载通话质检明细
    downloadDetailed() {
      const params = {
        dialogId: this.$route.query?.dialogId ? Number(this.$route.query.dialogId) : undefined,
      }
      this.lx.mc('b_gc_m_1b5feb80_mc')
      API.downloadDetailed(params)
        .then(res => {
          this.$refs.DownloadModalRef.open()
        })
        .catch(errorReportOnly)
    },
    // 显示号码
    async decryptHandler() {
      if (this.decryptTel) return this.decryptTel
      try {
        const response = await API.queryPhoneDecrypt({
          dialogId: this.$route.query.dialogId,
        })
        if (response && response.data) {
          this.decryptTel = response.data
          return this.decryptTel
        } else {
          return
        }
      } catch (error) {}
    },
  },
}
</script>

<style lang="scss" scoped>
.basic-information-content {
  padding: 20px 0 25px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  border-bottom: 1px solid rgba($color: #111925, $alpha: 0.05);

  .basic-information-content-box {
    width: calc(25% - 12px);
    display: flex;

    .basic-information-content-label {
      text-align: right;
      font-size: 14px;
      color: #666;
      width: 98px;
    }

    .basic-information-content-call-label {
      text-align: left;
      font-size: 14px;
      color: #666;
      width: 98px;
    }

    .basic-information-content-call-value {
      align-self: flex-start;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      font-size: 14px;
      flex-direction: row;
      .basic-information-content-call-name {
        white-space: nowrap;
      }
    }
    .basic-information-content-value {
      flex: 1;
      align-self: flex-start;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      font-size: 14px;
      word-break: break-all;
    }
  }
}

.downloadButton {
  color: #166ff7;
}
.vertical-line {
  margin: 0 4px;
  width: 1px;
  height: 12px;
  background-color: #e5e5e5;
}

.width-70 {
  width: 70px;
}

.width-98 {
  width: 98px;
}
.connect-status-none {
  display: none;
}

.connect-status-1 {
  color: #18b342;
}

.connect-status-2 {
  color: #fe8c00;
}
</style>
