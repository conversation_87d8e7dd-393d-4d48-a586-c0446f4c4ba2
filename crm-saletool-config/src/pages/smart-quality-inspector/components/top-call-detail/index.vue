<template>
  <!-- 通话记录 -->
  <div class="top-container">
    <div class="top-left">
      <div class="page-title">通话记录详情</div>
    </div>
    <div v-if="!isVisitSale" class="top-right">
      <mtd-button v-if="basicInformation.visitId" @click="jumpToVisit('view')">查看拜访</mtd-button>
      <mtd-button
        v-else-if="
          !userInfo.role &&
          basicInformation.teleRecordType == TELE_RECORD_TYPE.VISIT &&
          basicInformation.channel == CHANNEL.CALLOUT &&
          basicInformation.isConnect != CONNECT_CODE.ABANDONED &&
          basicInformation.canFeedback
        "
        @click="jumpToVisit"
        >添加拜访</mtd-button
      >
      <mtd-button
        v-if="
          basicInformation.tenantType == 1 &&
          !userInfo.role &&
          (basicInformation.visitPlan || basicInformation.canAddVisitPlan) &&
          basicInformation.isConnect != CONNECT_CODE.ABANDONED
        "
        style="background: #ffd100; margin-left: 13px"
        @click="jumpToVisitPlan"
        >{{ basicInformation.visitPlan ? '查看计划' : '添加计划' }}</mtd-button
      >
    </div>
    <div v-else class="top-right">
      <mtd-button v-if="basicInformation.canFeedback" @click="fillRecord">填写话务记录</mtd-button>
    </div>
  </div>
</template>
<script>
import API from '../../api'

import { errorReportOnly } from '@/lib/utils'
import { getUrlParams, isDirectProcess, encodeSpecialParam, joinParamString } from '../../lib/utils'
import {
  BIZ_TYPE,
  TELE_RECORD_TYPE,
  CHANNEL,
  CONNECT_CODE,
  VISIT_BASE_URL,
  FROM_TYPE,
} from '../../lib/constant'

export default {
  name: 'TopCallDetail',
  props: {
    basicInformation: {
      type: Object,
      default: () => ({}),
    },
    lx: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      businessTypes: {},
      isCCCGray: false,
      userInfo: '',
      dialogId: Number(this.$route.query.dialogId),
      TELE_RECORD_TYPE,
      CHANNEL,
      CONNECT_CODE,
    }
  },
  computed: {
    isVisitSale() {
      return this.businessTypes.tenantType == BIZ_TYPE.VISIT_SALE
    },
  },
  async created() {
    try {
      await this.queryUserInfo()
    } catch (error) {}
    this.queryCCCGray()
    this.queryTenantBusinessTypes()
  },
  methods: {
    queryUserInfo() {
      const userInfo = API.getUserDetailInfo()
      this.userInfo = userInfo.data || {}
    },
    // 获取销售类型
    queryTenantBusinessTypes() {
      API.queryTenantBusinessTypes({
        dialogId: this.dialogId,
      })
        .then(res => {
          const tenantBusinessType = res.data
          this.businessTypes = tenantBusinessType
        })
        .catch(errorReportOnly)
    },
    //拼接参数
    joinParamString(params, excepts = []) {
      let paramString = ''
      for (let i of Object.keys(params)) {
        if (excepts.indexOf(i) === -1 && params[i] != null) paramString += `${i}=${params[i]}&`
      }
      if (paramString.length) paramString = paramString.substring(0, paramString.length - 1)
      return paramString
    },
    jumpToVisitPlan() {
      this.lx?.mc('b_gc_m_ve2b5h79_mc', { call_out: this.basicInformation.channel == 1 ? 1 : 0 })
      const {
        shopId,
        dialogId,
        visitPlan,
        contactId,
        accCallId,
        callStartTime,
        shopContactMobile,
      } = this.basicInformation || {}
      const jumpVisitPlanUrl = '/crm-pc-plan/edit.html'
      const params = {
        tenantId: 3,
      }
      if (visitPlan) {
        Object.assign(params, {
          type: 'view',
          planId: visitPlan,
        })
      } else {
        Object.assign(params, {
          shopId,
          dialogId,
          contactId,
          accCallId,
          callStartTime,
          shopContactMobile,
        })
      }
      const reqParams = joinParamString(params)
      window.open(encodeURI(`${jumpVisitPlanUrl}?${reqParams}`))
    },
    // 跳转拜访
    async jumpToVisit(type) {
      const { shopId, dialogId, contactId, visitId } = this.basicInformation
      this.lx?.mc('b_gc_m_ve2b5h79_mc', { call_out: this.basicInformation.channel == 1 ? 1 : 0 })
      try {
        if (type == 'view') {
          const res = await API.queryCCCGray({
            tenantId: 3,
            migrationBizCode: 'dz',
            moduleCode: 'config_visit',
            activityType: 4, //4为拜访 1为计划
            activityId: visitId.toString(),
          })
          const params = this.joinParamString({
            visitId,
            type,
          })
          if (res && res.code == 200) {
            if (res.data && res.data.newTemplate && res.data.grayUser) {
              window.open(encodeURI(`/crm-pc-visit/index.html?tenantId=3&${params}`))
            } else {
              window.open(encodeURI(`${VISIT_BASE_URL}?${params}`))
            }
          } else {
            throw new Error()
          }
        } else {
          const params = this.joinParamString({
            shopId: shopId == 0 ? null : shopId,
            dialogId,
            contactId: contactId == 0 ? null : contactId,
          })
          if (this.isCCCGray) {
            window.open(encodeURI(`/crm-pc-visit/index.html?tenantId=3&${params}`))
          } else {
            window.open(encodeURI(`${VISIT_BASE_URL}?${params}`))
          }
        }
      } catch (e) {
        this.$mtd.message({
          message: '获取灰度模版信息出错',
          type: 'error',
        })
      }
    },
    queryCCCGray() {
      API.queryCCCGray({
        tenantId: 3,
        migrationBizCode: 'dz',
        moduleCode: 'config_visit',
      }).then(res => {
        if (res && res.code == 200) {
          this.isCCCGray = res.data && res.data.grayUser
        }
      })
    },
    fillRecord() {
      try {
        const { otherParams } = getUrlParams()
        const record = JSON.parse(otherParams || {}) || {}
        const {
          shopId,
          shopName,
          dialogId,
          shopContactName,
          isKP,
          shopContactTitle,
          shopContactMobile,
          conversationId,
          teleRecordType,
          dialSource,
          channel,
        } = record
        if (TELE_RECORD_TYPE.TELE == teleRecordType) {
          // 电销
          let preUrl =
            process.env.NODE_ENV === 'development'
              ? '/fillincallrecord.html'
              : '/telesales/fillincallrecord.html'
          //基本参数
          let params = {
            tele: shopContactMobile,
            conversationId,
            dialogId,
            dialSource,
            channel,
            fromType: FROM_TYPE.RECORD,
          }
          //如果是普通呼出需要增加参数
          if (channel == CHANNEL.CALLOUT && !isDirectProcess(dialSource)) {
            params = {
              ...params,
              shopId,
              shopName: encodeSpecialParam(shopName),
              owner: encodeSpecialParam(shopContactName),
              isKP,
              identity: shopContactTitle,
            }
          }
          let reqParams = joinParamString(params)
          window.open(encodeURI(`${preUrl}?${reqParams}`))
        }
      } catch (err) {
        window.Owl && window.Owl.addError(err)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.top-container {
  display: flex;
  justify-content: space-between;
  padding: 16px 16px 0 16px;

  .top-left {
    display: flex;
  }

  .mtd-btn-before {
    color: #000;
    margin-right: 4px;
  }

  .page-title {
    padding-top: 4px;
    font-size: 18px;
    line-height: 24px;
    font-weight: 500;
  }

  .top-right {
    margin-top: 4px;
  }
  .mtd-btn {
    border-radius: 4px;
    background: #ffd100;
  }

  .mtd-btn:hover {
    color: #000;
    border-color: rgba(0, 0, 0, 0.12);
    background-color: rgba(0, 0, 0, 0.05);
  }

  .mtd-btn-primary {
    color: #000;
    background: #ffd100;
    border: none;
  }

  .mtd-btn-primary.hover,
  .mtd-btn-primary:focus,
  .mtd-btn-primary:hover {
    color: #000;
    background: #f2c700;
    border-color: #f2c700;
  }
}
</style>
