<template>
  <UniversalBox title="通话记录" class="call-record-container">
    <template #action>
      <div class="action">
        <span>展开全部质检命中</span>
        <mtd-switch v-model="expandAllHit" size="small" @change="handleChangeHit"></mtd-switch>
        <span>展开全部评论</span>
        <mtd-switch
          v-model="expandAllComments"
          size="small"
          @change="handleChangeComments"
        ></mtd-switch>
      </div>
    </template>

    <div class="call-record-content">
      <HitLinkSwitching :hit-links="aiHitLinks" @switchHitLink="handleScrollIntoView" />
      <DialogScrollContainer
        ref="DialogScrollContainerRef"
        :data-source="dataSource"
        :left-aligned-role="ROLE.MERCHANT"
        :audio-address="audioAddress"
      >
        <template #sentence="{ item: data }">
          <!-- 对话信息 -->
          <div
            :class="['chat-content', data.role === ROLE.MERCHANT ? 'left-align' : 'right-align']"
          >
            <!-- 头像 -->
            <div class="avatar">
              <div v-if="data.role === ROLE.MERCHANT" class="mer-avatar">商户</div>
              <img v-else :src="getAvatar(data.user)" alt="" />
            </div>

            <!-- 对话内容 -->
            <div class="content">
              {{ data.content }}

              <div
                v-if="data.role !== ROLE.MERCHANT"
                :style="{
                  [data.role === ROLE.MERCHANT ? 'right' : 'left']: 0,
                  transform: `translateX(calc(${
                    data.role === ROLE.MERCHANT ? '100% + 6px' : '-100% - 6px'
                  }))`,
                }"
                class="exhibition"
              >
                <!-- 命中/评论ICON -->
                <div v-if="data.aiHitLinks.length || data.comment" class="hit operation">
                  <!-- 命中 -->
                  <span v-if="data.aiHitLinks.length" @click="handleExpandHit(data)">
                    <img class="hit-icon" :src="hitIcon" alt="" />
                    <span class="quantity">{{ data.aiHitLinks.length }}</span>
                    <i
                      class="theme-icon"
                      :class="data.expandHit ? 'mtdicon-up-thick' : 'mtdicon-down-thick'"
                    ></i>
                  </span>
                  <span
                    v-if="data.aiHitLinks.length && data.comment"
                    class="vertical-line line-back"
                  ></span>
                  <!-- 评论 -->
                  <span v-if="data.comment" @click="handleExpandComment(data, 'edit')">
                    <i class="mtdicon-comment-fill theme-icon"></i>
                    <i
                      class="theme-icon"
                      :class="data.expandComment ? 'mtdicon-up-thick' : 'mtdicon-down-thick'"
                    ></i>
                  </span>
                </div>

                <!-- 添加评论 -->
                <span
                  v-if="!data.comment && canEdit"
                  class="add-comment operation"
                  @click="handleExpandComment(data, 'add')"
                >
                  <mtd-tooltip content="评论" size="small" placement="top">
                    <i class="mtdicon-add-message"></i>
                  </mtd-tooltip>
                </span>
              </div>
            </div>
          </div>
          <div class="detailed">
            <HitLink v-if="data.expandHit" :links="data.aiHitLinks" />
            <CommentPicker
              v-if="data.expandComment"
              :sequence-id="data.sequenceId"
              :hit-links="data.humanHitLinks"
              :comment="data.comment"
              :scene-links="sceneLinks"
              :enums="enums"
              :can-edit="canEdit"
              @close="data.expandComment = !data.expandComment"
              @refresh="refreshWithExpandComment"
            />
          </div>
        </template>
      </DialogScrollContainer>
    </div>
  </UniversalBox>
</template>

<script>
import { defineComponent } from '@vue/composition-api'
import { DialogScrollContainer, UniversalBox } from '@/components'
import avatar from '@/assets/<EMAIL>'
import hitIcon from '@/assets/hit-icon.png'
import { errorReportOnly } from '@/lib/utils'
import { flatten } from 'lodash'

import { HitLink, CommentPicker, HitLinkSwitching } from '../index'
import { HIT_TYPE, ROLE } from '../../lib/constant'
import API from '../../api'

// 处理环节数据
const handleSceneLinks = (sceneLinks = []) => {
  const recursion = (arr = [], scene = {}, level = 0) => {
    if (level === 0) {
      arr.forEach(item => {
        item.value = item.sceneId
        item.label = item.sceneName
        recursion(
          item?.linkInfos || [],
          { sceneId: item.sceneId, sceneName: item.sceneName },
          level + 1,
        )
        item.children = item?.linkInfos || []
      })
    } else {
      arr.forEach(item => {
        item.scene = scene
        item.value = item.linkId
        item.label = item.linkName
        recursion(item?.subLinkInfos || [], scene, level + 1)
        item.children = item?.subLinkInfos || []
      })
    }
  }

  recursion(sceneLinks)
}

// 获取命中环节
const handleHitLink = (links = [], sequenceId) => {
  const hitLink = []

  const recursion = (arr = [], title = '', linkIds = []) => {
    if (arr.length) {
      const hit = arr.filter(it => it?.hitLink)
      hit.forEach(item => {
        const curtitle = title ? `${title}-${item.linkName}` : item.linkName
        !linkIds.length && linkIds.push(item.scene.sceneId)
        const curIds = [...linkIds, item.linkId]
        if (item?.subLinks?.length) {
          recursion(item.subLinks, curtitle, curIds)
        } else {
          hitLink.push({
            ...item,
            sequenceId,
            markId: item?.mark?.markId,
            linkIds: curIds, // 级联回显数据
            title: curtitle, // 展示命中环节title
          })
        }
      })
    }
  }

  recursion(links)
  return {
    aiHitLinks: hitLink.filter(item => item?.hitType === HIT_TYPE.AI_HIT), // AI命中环节
    humanHitLinks: hitLink.filter(item => item?.hitType === HIT_TYPE.HUMAN_HIT), // 人工命中环节
  }
}

const callRecord2DialogItem = data => ({
  ...data,
  expandComment: false, // 展开评论
  expandHit: false, // 展开命中环节
  links: data?.links || [],
  role: data?.user?.role,
  ...handleHitLink(data?.links || [], data.sequenceId), // 命中环节
})

export default defineComponent({
  name: 'CallRecord',
  components: {
    UniversalBox,
    DialogScrollContainer,
    HitLink,
    CommentPicker,
    HitLinkSwitching,
  },
  props: {
    basicInformation: {
      type: Object,
      default: null,
    },
    lx: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      ROLE,
      hitIcon,
      defaultAvatar: avatar, // 默认头像
      missionId: this.$route.query.missionId,
      dialogId: Number(this.$route.query.dialogId),
      audioAddress: '',
      dataSource: [], // 通话记录
      scenes: [], // 命中场景
      sceneLinks: [], // 所有场景环节信息
      enums: [], // 质检结果枚举
      expandAllComments: false, // 展开全部评论
      expandAllHit: false, // 展开全部质检
      canEdit: false, // 是否可以评论
    }
  },
  computed: {
    aiHitLinks() {
      return flatten(this.dataSource.map(item => item.aiHitLinks))
    },
  },
  watch: {
    basicInformation: {
      handler(val) {
        if (val?.callId) {
          this.getVoiceurl()
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.getCallContent()
    this.getCallQualityScenes()
    this.getCallQualityEnums()
  },
  methods: {
    // 滚动到指定元素
    handleScrollIntoView(data) {
      const { sequenceId, linkId } = data
      const findData = this.dataSource.find(item => item.sequenceId === sequenceId)
      if (!findData) return
      const time = findData?.startTime || 0
      !findData?.expandHit && this.handleExpandHit(findData)
      this.$refs.DialogScrollContainerRef.scrollIntoView(Math.ceil(time / 1000))
    },
    // 获取头像
    getAvatar(data) {
      return data?.avatar || this.defaultAvatar
    },
    // 获取语音链接
    getVoiceurl() {
      API.getVoiceurl({
        callId: this.basicInformation.accCallId,
      })
        .then(res => {
          this.audioAddress = res.data
        })
        .catch(errorReportOnly)
    },
    // 获取通话内容
    getCallContent() {
      API.getCallContent({ dialogId: this.dialogId })
        .then(res => {
          this.dataSource = (res?.data?.contents || []).map(callRecord2DialogItem)
          this.canEdit = res?.data?.canEdit || false
        })
        .catch(errorReportOnly)
    },
    // 刷新列表数据，并保留指定的展开评论状态
    refreshWithExpandComment() {
      const expandedSequenceIdCommentsList = this.dataSource
        .filter(item => item.expandComment)
        .map(item => item.sequenceId)
      const expandedSequenceIdHitList = this.dataSource
        .filter(item => item.expandHit)
        .map(item => item.sequenceId)

      const expandedSequenceIdCommentsSet = new Set(expandedSequenceIdCommentsList)
      const expandedSequenceIdHitSet = new Set(expandedSequenceIdHitList)

      API.getCallContent({ dialogId: this.dialogId })
        .then(res => {
          this.dataSource =
            res.data?.contents?.map(callRecord2DialogItem).map(item => ({
              ...item,
              expandComment: expandedSequenceIdCommentsSet.has(item.sequenceId),
              expandHit: expandedSequenceIdHitSet.has(item.sequenceId),
            })) || []
          this.canEdit = res?.data?.canEdit || false
        })
        .catch(errorReportOnly)
    },
    // 根据输入的场景查询场景下的所有环节
    getSceneLinks() {
      const sceneIds = this.scenes?.map(it => it.sceneId) || []
      if (!sceneIds.length) return
      API.getSceneLinks({ sceneIds })
        .then(res => {
          this.sceneLinks = res.data?.links || []
          handleSceneLinks(this.sceneLinks)
        })
        .catch(errorReportOnly)
    },
    // 通话质量场景
    getCallQualityScenes() {
      API.queryQualityScene({ dialogId: this.dialogId })
        .then(res => {
          this.scenes = res?.data?.scenes || []
          this.getSceneLinks()
        })
        .catch(errorReportOnly)
    },
    // 质检结果枚举
    getCallQualityEnums() {
      API.getCallQualityEnums()
        .then(res => {
          this.enums = res?.data?.results || []
        })
        .catch(errorReportOnly)
    },
    // 展开全部质检命中 change
    handleChangeHit(val) {
      this.expandAllHit = val
      this.dataSource.forEach(item => {
        item.aiHitLinks.length && (item.expandHit = val)
      })
    },
    // 展开/关闭全部质检命中
    handleExpandAllHit() {
      this.expandAllHit = this.dataSource.every(item => {
        return !item.aiHitLinks?.length || (item.aiHitLinks?.length && item.expandHit)
      })
    },
    // 点击展开质检命中
    handleExpandHit(data) {
      !data.expandHit && this.lx?.mc('b_gc_m_2b1f7741_mc')
      data.expandHit = !data.expandHit
      this.handleExpandAllHit()
    },
    // 展开全部评论 change
    handleChangeComments(val) {
      this.expandAllComments = val
      this.dataSource.forEach(item => {
        item.comment && (item.expandComment = val)
      })
    },
    // 展开/关闭全部评论
    handleExpandAllComments() {
      this.expandAllComments = this.dataSource.every(item => {
        return !item.comment || (item.comment && item.expandComment)
      })
    },
    // 点击展开评论
    handleExpandComment(data, type) {
      !data.expandComment && this.lx?.mc('b_gc_m_pnl5b5u6_mc')
      data.expandComment = !data.expandComment
      type !== 'add' && this.handleExpandAllComments(data)
    },
  },
})
</script>

<style lang="scss" scoped>
.call-record-container {
  width: 60%;
  color: #222;
  border-right: 1px solid rgba($color: #111925, $alpha: 0.05);

  .action {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .call-record-content {
    position: relative;
    padding-top: 24px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .audio-player {
      border-top: 0;
      border-color: #e5e5e5;
    }
  }

  .dialogue-item {
    .chat-content {
      .avatar {
        .mer-avatar,
        img {
          width: 36px;
          height: 36px;
          vertical-align: middle;
          border-radius: 50%;
        }

        .mer-avatar {
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--chat-content-color);
          font-size: 12px;
          font-weight: 500;
          border: 0.5px solid #e8e8e8;
          background-color: var(--color-primary);
        }
      }

      .content {
        position: relative;
        padding: 8px 12px;
        border-radius: 4px;
        background-color: #fff;
        border: 1px solid transparent;
      }

      .exhibition {
        position: absolute;
        bottom: 0;
        display: flex;
        gap: 4px;
        flex-direction: row-reverse;

        .operation {
          height: 20px;
          padding: 3px 4px;
          display: flex;
          align-items: center;
          flex-direction: row-reverse;
          cursor: pointer;
          font-size: 12px;
          border-radius: 4px;
        }

        .add-comment {
          display: none;
          background: #fff;
        }

        .hit {
          background-color: var(--chat-operation-background);

          > span {
            display: flex;
            align-items: center;
            gap: 1px;
          }

          .hit-icon {
            height: 12px;
            width: 12px;
          }

          .line-back {
            background-color: rgba(22, 111, 247, 0.08);
          }

          .quantity {
            height: 12px;
            margin-left: 2px;
            line-height: 12px;
            font-size: 12px;
            color: #7a4900;
          }
        }
      }
    }

    .detailed {
      margin: 0 0 0 44px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    &.reverse {
      .chat-content {
        flex-direction: row-reverse;

        .content {
          background-color: rgba(255, 209, 0, 0.4);
        }
      }

      .detailed {
        margin: 0 44px 0 0;
        align-items: flex-end;
      }

      .exhibition {
        flex-direction: row;

        .operation {
          flex-direction: row;
        }
      }
    }

    &:hover {
      .exhibition .add-comment {
        display: flex;
      }
    }
  }
}

.vertical-line {
  margin: 0 4px;
  width: 1px;
  height: 12px;
  background-color: rgba(122, 73, 0, 0.08);
}

.theme-icon {
  color: var(--chat-icon-color);
}
</style>
