<template>
  <div class="hit-description">
    <div class="header">
      <img class="hit-icon" :src="hitBlack" alt="" /> 命中子环节 ({{ links.length }})
    </div>
    <div v-for="link in links" :key="link.linkId" class="content">
      <div class="sub-link-title">{{ link.title }}</div>
      <div>核心语义：{{ getSemantics(link) }}</div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from '@vue/composition-api'
import hitBlack from '@/assets/hit-black.png'

export default defineComponent({
  name: 'HitLink',
  props: {
    links: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      hitBlack,
    }
  },
  methods: {
    getSemantics(link) {
      return link?.semantics?.[0].semanticContent || '-'
    },
  },
})
</script>

<style lang="scss" scoped>
.hit-description {
  width: 300px;
  border-radius: 6px;
  padding-bottom: 8px;
  background: #f0f0f0;

  .header {
    padding-left: 12px;
    height: 36px;
    display: flex;
    gap: 2px;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 1px solid rgba($color: #ccc, $alpha: 0.6);

    .hit-icon {
      width: 13px;
      height: 13px;
    }
  }

  .content {
    padding: 8px 12px 0;

    .sub-link-title {
      color: #166ff7;
      font-weight: 500;
    }
  }
}
</style>
