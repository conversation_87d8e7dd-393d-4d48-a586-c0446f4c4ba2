<template>
  <mtd-table
    :data="data"
    v-bind="$attrs"
    :row-class="tableRowClassName"
    :default-expand-all="defaultExpandAll"
    :expandable="defaultExpandable"
  >
    <mtd-table-column type="expand" width="30" :class-name="`expand-level-${level}`">
      <template #default="{ row }">
        <RecursiveLinkTable
          v-if="row.subLinks && row.subLinks.length"
          :show-header="false"
          :data="row.subLinks"
          :editable="editable"
          :person-inspect-options="personInspectOptions"
          :level="level + 1"
          v-bind="$attrs"
          :default-expand-all="false"
          :expandable="childExpandable"
        />
        <div v-else class="semantic-wrapper" :style="getPaddingStyle(level)">
          <div
            v-for="semantic in getKeySemantics(row.aiLink.semantics)"
            :key="semantic.semanticId"
            class="semantic-box"
          >
            <div class="semantic-item">
              <div>
                <span class="semantic-name">核心语义：</span>
                <span class="semantic-value"> {{ semantic.semanticContent }} </span>
              </div>
              <div>
                <span class="semantic-name">参考话术：</span>
                <span class="semantic-value"> {{ semantic.standardSpeech }} </span>
              </div>
              <div>
                <span class="semantic-name">关键词：</span>
                <span class="semantic-value">
                  {{ (semantic.keywords || []).join('，') }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </mtd-table-column>

    <mtd-table-column prop="aiLink.linkName" label-class="deviation" label="打分项">
      <template #default="{ row }">
        {{ row.aiLink.linkName }}
        <div v-if="row.aiLink.hitCount > 0" class="hit operation" @click="jumpLinkId(row)">
          <img class="hit-icon" src="@/assets/hit-icon.png" alt="" />
          <span class="quantity">{{ row.aiLink.hitCount }}次</span>
          <i class="theme-icon mtdicon-right-thick"></i>
        </div>
      </template>
    </mtd-table-column>

    <mtd-table-column align="right" label="AI质检分" width="100">
      <template #default="{ row }">
        <ScoreResult
          :value.sync="row.aiLink.result.idPath"
          :quality-enums="personInspectOptions"
        ></ScoreResult>
      </template>
    </mtd-table-column>

    <mtd-table-column align="right" label="人工质检分" width="134">
      <template #default="{ row }">
        <ScoreResult
          :value.sync="row.personLink.result.idPath"
          :quality-enums="personInspectOptions"
          :can-edit="editable"
        >
        </ScoreResult>
      </template>
    </mtd-table-column>
  </mtd-table>
</template>
<script>
import { TAG_COLOR } from '../../lib/constant'
import EventBus from '../../lib/eventbus'
import { getTagLabel, getPerosonTag } from '../../lib/utils'
import ScoreResult from '../quality-results/score-result.vue'

export default {
  name: 'RecursiveLinkTable',
  components: {
    ScoreResult,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    level: {
      type: Number,
      default: 0,
    },
    editable: {
      type: Boolean,
      default: false,
    },
    personInspectOptions: {
      type: Array,
      default: () => [],
    },
    defaultExpandAll: {
      type: Boolean,
      default: true,
    },
    expandable: {
      type: Function,
      default: null,
    },
  },
  data() {
    return {
      TAG_COLOR,
      clickedRowId: null,
      isFading: false,
    }
  },
  methods: {
    getTagLabel,
    getPerosonTag,
    getKeySemantics(semantics) {
      return (semantics || []).filter(item => item.isKey)
    },
    getPaddingStyle(level) {
      return {
        marginLeft: `${(level + 1) * 25}px`,
      }
    },
    jumpLinkId(row) {
      this.clickedRowId = row.aiLink.linkId
      this.isFading = false
      EventBus.$emit('openHitLinkSwitching', row.aiLink.linkId)
      setTimeout(() => {
        this.isFading = true
      }, 2600)
      setTimeout(() => {
        this.clickedRowId = null
      }, 3000)
    },
    tableRowClassName({ row }) {
      if (row.aiLink.linkId === this.clickedRowId) {
        return this.isFading ? 'warning-row fading' : 'warning-row'
      }
      return ''
    },
    parentExpandable(row) {
      return row.aiLink && row.aiLink.subLinks.length > 0
    },
    childExpandable(row) {
      if (!row.aiLink || row.aiLink.semantics.length === 0) {
        return false
      }
      return row.aiLink.semantics.some(semantic => semantic.isKey === true)
    },
    defaultExpandable(row) {
      if (this.level === 0) {
        return this.parentExpandable(row)
      } else {
        return this.childExpandable(row)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
// 定义最大级别
$max-level: 3;
::v-deep {
  // 使用 @for 循环来生成不同级别的样式
  @for $i from 1 through $max-level {
    .expand-level-#{$i} {
      .mtd-table-cell {
        padding-left: 28px * $i;
        overflow: visible;
      }

      // 如果需要对紧跟的兄弟元素进行样式定义
      + td .mtd-table-cell {
        padding-left: 20px * $i;
      }
    }
  }

  .warning-row td {
    background-color: rgba(255, 209, 0, 0.2) !important;
    transition: background-color 0.4s ease;
  }

  .warning-row.fading td {
    background-color: rgba(255, 209, 0, 0) !important;
  }
}

.semantic-wrapper {
  flex: 1;
}

.hit {
  background-color: var(--quality-hit-background);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 5px;
  cursor: pointer;

  .hit-icon {
    height: 12px;
    width: 12px;
  }

  .line-back {
    background-color: rgba(22, 111, 247, 0.08);
  }

  .quantity {
    height: 12px;
    margin-left: 2px;
    line-height: 12px;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #7a4900;
  }
}
.operation {
  height: 20px;
  padding: 3px 4px;
  margin-left: 3px;
  align-items: center;
  cursor: pointer;
  font-size: 12px;
  border-radius: 4px;
}
.semantic-box {
  margin: 12px 0;
  padding: 8px;
  width: 100%;
  border-radius: 6px;
  background: #f8f8f8;

  .semantic-item {
    font-size: 12px;

    .semantic-name {
      font-weight: 500;
    }
  }
}
</style>
