<template>
  <div v-show="visible" class="hit-link-switching">
    <div class="header">
      <span>查看命中子环节会话</span>
      <i class="mtdicon mtdicon-close" @click="visible = false"></i>
    </div>

    <div class="content">
      <mtd-select
        v-model="activeData"
        size="small"
        value-key="value"
        :formatter="formatter"
        @change="handleChange"
      >
        <mtd-option v-for="(item, index) in dataSource" :key="index" :value="item">
          {{ formatter({ value: item }) }}
        </mtd-option>
      </mtd-select>
      <span class="vertical-line"></span>
      <div class="btn mr" :class="{ desabled: activeIndex <= 0 }" @click="upper">
        <i class="mtdicon mtdicon-up"></i>
      </div>
      <div class="btn" :class="{ desabled: activeIndex >= maxIndex }" @click="next">
        <i class="mtdicon mtdicon-down"></i>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from '@vue/composition-api'
import EventBus from '../../lib/eventbus'

export default defineComponent({
  name: 'HitLinkSwitching',
  props: {
    hitLinks: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      dataSource: [],
      activeIndex: 0,
      maxIndex: 0,
      activeData: null,
    }
  },
  watch: {
    hitLinks: {
      immediate: true,
      deep: true,
      handler(val) {
        const dataSource = []
        val?.forEach(item => {
          const index = dataSource.findIndex(it => it.value === item.linkId)

          if (index !== -1) {
            dataSource[index].repeatSequenceId.push(item.sequenceId)
          } else {
            dataSource.push({
              label: item.linkName,
              value: item.linkId,
              linkIds: item?.linkIds || [],
              repeatSequenceId: [item.sequenceId],
            })
          }
        })
        this.dataSource = dataSource
      },
    },
  },
  created() {
    EventBus.$on('openHitLinkSwitching', this.open)
  },
  methods: {
    formatter({ value }) {
      return `${value.label}（${value.repeatSequenceId.length}）`
    },
    // 打开弹窗
    open(linkId) {
      this.visible = true
      if (this.dataSource.length) {
        if (linkId) {
          this.activeData = this.dataSource.find(item => item.linkIds.includes(linkId))
          this.handleChange(this.activeData)
        } else {
          this.activeData = this.dataSource[0]
          this.handleChange(this.activeData)
        }
      }
    },
    // 切换选中的环节
    handleSwitchLinks() {
      this.$emit('switchHitLink', {
        linkId: this.activeData.value,
        sequenceId: this.activeData.repeatSequenceId[[this.activeIndex]],
      })
    },
    // 切换环节
    handleChange(val) {
      this.activeIndex = 0
      this.maxIndex = val.repeatSequenceId.length - 1
      this.handleSwitchLinks()
    },
    // 上一个命中环节
    upper() {
      if (this.activeIndex <= 0) return
      this.activeIndex = this.activeIndex - 1
      this.handleSwitchLinks()
    },
    // 下一个命中环节
    next() {
      if (this.activeIndex >= this.maxIndex) return
      this.activeIndex = this.activeIndex + 1
      this.handleSwitchLinks()
    },
  },
})
</script>

<style lang="scss" scoped>
.hit-link-switching {
  position: absolute;
  top: 25px;
  left: 1px;
  z-index: 99;
  width: 264px;
  height: 74px;
  padding: 12px;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);

  .header {
    height: 14px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .mtdicon-close {
      cursor: pointer;
    }
  }

  .content {
    display: flex;
    align-items: center;

    .btn {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      cursor: pointer;
      color: #666;

      &.desabled {
        cursor: not-allowed;
        color: #ccc;
      }

      &.mr {
        margin-right: 8px;
      }
    }
  }

  .vertical-line {
    margin: 0 12px;
    width: 1px;
    height: 12px;
    background-color: #e5e5e5;
  }
}
</style>
