<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>
<script>
import { reportError } from '@/lib/utils'
import { getEnv } from '@/lib/utils'

export default {
  beforeCreate() {
    // 设置主题色
    if (getEnv().isApolloPlatform) {
      // 黄色主题
      import('@ss/mtd-vue2/lib/theme-yellow/index.css').catch(reportError)
      import('./styles/theme-yellow.css')
    } else {
      // 蓝色主题
      import('@ss/mtd-vue2/lib/theme-chalk/index.css').catch(reportError)
      import('./styles/theme-chalk.css')
    }
  },
}
</script>

<style lang="scss">
html,
body,
#app {
  height: 100%;
  color: #222;
}
</style>
