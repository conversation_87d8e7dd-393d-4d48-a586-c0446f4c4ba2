<template>
  <div class="smart-quality-inspection-container">
    <TopCallDetail :basic-information="basicInformation" :lx="lx" />
    <!-- 基础信息 -->
    <BasicInformation :basic-information="basicInformation" :lx="lx" :scenes="scenes" />
    <div class="bottom-content">
      <CallRecord :lx="lx" :basic-information="basicInformation" />
      <QualityResults :lx="lx" :scenes="scenes" />
    </div>
  </div>
</template>

<script>
import { LXUtils } from '@/lib/lx'
import API from '../api'
import { BasicInformation, CallRecord, QualityResults, TopCallDetail } from '../components'
import { valLabGeneratorMap } from '../lib/lx'
import { errorReportOnly } from '@/lib/utils'

export default {
  name: 'Ivr',
  components: {
    BasicInformation,
    CallRecord,
    QualityResults,
    TopCallDetail,
  },
  data() {
    return {
      basicInformation: {},
      scenes: [],
      lx: null,
      dialogId: Number(this.$route.query.dialogId),
    }
  },
  created() {
    this.lx = new LXUtils('gc_m', {
      cid: 'c_gc_m_ybsld2mi',
      appnm: 'dp_apollo_pc',
      valLabGenerator: valLabGeneratorMap.misIdAndTenantId(),
    })
    this.getData()
    this.getScenes()
  },
  methods: {
    getData() {
      API.queryBasicInfo({ dialogId: this.dialogId })
        .then(res => {
          const dataList = res.data || {}
          this.basicInformation = dataList
        })
        .catch(errorReportOnly)
    },
    getScenes() {
      API.queryQualityScene({ dialogId: this.dialogId })
        .then(res => {
          this.scenes = res?.data?.scenes || []
        })
        .catch(errorReportOnly)
    },
  },
}
</script>
<style lang="scss" scoped>
.smart-quality-inspection-container {
  height: 100%;
  padding-bottom: 16px;
  display: flex;
  flex-direction: column;
  .bottom-content {
    flex: 1;
    overflow: hidden;
    display: flex;
  }
}
</style>
