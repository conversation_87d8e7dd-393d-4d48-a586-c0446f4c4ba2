import qs from 'qs'
import { DIRECT_SOURCE, TAG_COLOR } from './constant'

// decode特殊字符参数
export function decodeSpecialParam(param) {
  return param ? decodeURIComponent(param) : param
}
/* 获取url参数 */
export function getPathParams() {
  var url = decodeURI(location.href)
  if (url.split('?').length == 1) return {}
  url = url.split('?')[1]
  var para = url.split('&')
  var len = para.length
  var res = {}
  var arr = []
  const needDecodeParams = ['shopName', 'owner', 'wechatNickName', 'remark']
  for (var i = 0; i < len; i++) {
    arr = para[i].split('=')
    if (needDecodeParams.includes(arr[0])) {
      arr[1] = decodeSpecialParam(arr[1])
    }
    res[arr[0]] = arr[1]
  }
  return res
}

// qs获取URL参数
export function getUrlParams() {
  const search = location.search.replace(/\?/, '')
  return qs.parse(search)
}

/* 判断是否走直呼流程 */
export function isDirectProcess(source) {
  if (!source) return false
  return DIRECT_SOURCE.indexOf(parseInt(source, 10)) >= 0
}
// encode特殊字符参数
export function encodeSpecialParam(param) {
  return param ? encodeURIComponent(param.replace(/%/g, '%25')) : param
}
// 拼接参数
export function joinParamString(params, excepts = []) {
  let paramString = ''
  for (let i of Object.keys(params)) {
    if (excepts.indexOf(i) === -1 && params[i] != null) paramString += `${i}=${params[i]}&`
  }
  if (paramString.length) paramString = paramString.substring(0, paramString.length - 1)
  return paramString
}
// 获取标签文案
const findDesc = (enums, path, overallStyle = false) => {
  const item = enums.find(e => e.id === path[0])
  if (!item) return ''
  if (path.length === 1) {
    return item.desc
  }
  if (item.subResultEnums) {
    return overallStyle
      ? findDesc(item.subResultEnums, path.slice(1), overallStyle)
      : `${item.desc}-${findDesc(item.subResultEnums, path.slice(1))}`
  }
  return item.desc
}
export const getTagLabel = (tree, idPath, overallStyle = false) => {
  if (!idPath || idPath.length === 0) {
    return ''
  }
  return findDesc(tree, idPath, overallStyle)
}
export const getPerosonTag = idPath => {
  const lastId = idPath[idPath.length - 1]
  return TAG_COLOR[lastId] || ''
}

// 质检结果处理数据
export function processLinks(arr) {
  return arr
    .map(item => {
      if (!item.personLinks || item.personLinks.length === 0) {
        return item.aiLinks.map(aiLink => ({ aiLink, personLink: null, subLinks: [] }))
      }
      if (!item.aiLinks || item.aiLinks.length === 0) {
        return item.personLinks.map(personLink => ({ personLink, aiLink: null, subLinks: [] }))
      }
      return item.personLinks.map(personLink => {
        const aiLink = item.aiLinks.find(aiLink => aiLink.linkId === personLink.linkId)
        const subLinks = personLink.subLinks.map(personSubLink => {
          const aiSubLink = aiLink
            ? aiLink.subLinks.find(aiSubLink => aiSubLink.linkId === personSubLink.linkId)
            : null
          return {
            personLink: personSubLink,
            aiLink: aiSubLink,
            subLinks: [],
          }
        })
        return {
          personLink,
          aiLink,
          subLinks,
        }
      })
    })
    .flat()
}
//质检结果提交时处理数据
export function mergePersonLinks(thinkDataSourceMap, violationDataSourceMap) {
  const mergedPersonLinks = []
  Object.values(thinkDataSourceMap).forEach(items => {
    items.forEach(item => {
      if (
        item &&
        item.personLink &&
        !mergedPersonLinks.some(link => link.linkId === item.personLink.linkId)
      ) {
        mergedPersonLinks.push(item.personLink)
      }
    })
  })
  Object.values(violationDataSourceMap).forEach(items => {
    items.forEach(item => {
      if (
        item &&
        item.personLink &&
        !mergedPersonLinks.some(link => link.linkId === item.personLink.linkId)
      ) {
        mergedPersonLinks.push(item.personLink)
      }
    })
  })

  return mergedPersonLinks
}
