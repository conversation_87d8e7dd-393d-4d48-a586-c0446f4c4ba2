import axios from 'axios'
import { Message } from '@ss/mtd-vue2'

const config = {
  baseURL: process.env.VUE_APP_API_HOST || '',
  timeout: Number(process.env.VUE_APP_HTTP_TIMEOUT),
  withCredentials: true,
}

const instance = axios.create(config)

function isObject(value) {
  return value !== null && typeof value === 'object'
}

instance.interceptors.response.use(
  response => {
    const { data = {} } = response || {}
    // 兼容老接口
    if (!data?.hasOwnProperty('code')) {
      data.message = data?.message || (isObject(data?.error) ? data.error?.msg : data.error)
      if (data?.data !== undefined && data?.data !== null && !data.message) {
        data.code = 200
      }
    }
    if (data?.code !== 200) {
      Message({
        type: 'error',
        message: data?.message || '系统异常，请稍后重试',
      })
      return Promise.reject(data)
    }
    return data
  },
  error => {
    Message({
      type: 'error',
      message: error.message || '系统异常，请稍后重试',
    })
    return Promise.reject(error)
  },
)

export default instance
