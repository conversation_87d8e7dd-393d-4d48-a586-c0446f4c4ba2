import request from '../lib/request'

export default {
  // 获取登陆用户信息
  getUserDetailInfo() {
    return request('/telesales/user/getUserDetailInfo', {
      method: 'get',
    })
  },

  // 获取通话语音url
  getVoiceurl(data) {
    return request('/telesales/voiceurl', {
      method: 'get',
      params: data,
    })
  },

  // 查询所有质检场景
  getCallQualityScenes() {
    return request('/gateway/telesales/call/quality/scenes', {
      method: 'get',
    })
  },

  // 根据输入的场景查询场景下的所有环节（场景参数来源于上面的接口）
  getSceneLinks(data) {
    return request('/gateway/telesales/call/scene/links', {
      method: 'post',
      data: {
        req: data,
      },
    })
  },

  // 获取通话内容
  getCallContent(data) {
    return request('/gateway/telesales/call/content', {
      method: 'post',
      data: {
        req: data,
      },
    })
  },

  // 获取质检枚举
  getCallQualityEnums() {
    return request('/gateway/telesales/call/quality/enums', {
      method: 'get',
    })
  },
  //质检结果枚举-违规红线
  getCallQualityViolationEnums() {
    return request('/gateway/telesales/call/quality/violation/enums', {
      method: 'get',
    })
  },

  // 通话文本编辑保存
  saveCallContent(data) {
    return request('/gateway/telesales/call/content/quality/save', {
      method: 'post',
      data: {
        req: data,
      },
    })
  },

  /* 获取用户功能操作类型 */
  queryTenantBusinessTypes() {
    return request('/telesales/tenant/business/types', {
      method: 'get',
    })
  },
  queryCCCGray(params) {
    return request('/gateway/crm/grayTemplateServiceApi/queryUserGrayAndTemplateInfo', {
      method: 'post',
      data: { request: params },
    })
  },

  // 获取通话基础信息
  queryBasicInfo(query) {
    return request('/telesales/conversation/dialog/detail', {
      method: 'get',
      params: query,
    })
  },
  //下载质检明细
  downloadDetailed(data) {
    return request('/gateway/telesales/call/quality/result/download', {
      method: 'post',
      data: {
        req: data,
      },
    })
  },
  // 手机号解迷
  queryPhoneDecrypt(params) {
    return request('/telesales/phone/decrypt', {
      method: 'get',
      params,
    })
  },

  // 质检结果内容查询
  queryQualityResult(data) {
    return request('/gateway/telesales/call/quality/result', {
      method: 'post',
      data: {
        req: data,
      },
    })
  },
  //根据dialogId查询通话提交的质检场景接口
  queryQualityScene(data) {
    return request('/gateway/telesales/call/scenes/dialog', {
      method: 'post',
      data: {
        req: data,
      },
    })
  },

  // 提交质检结果
  submitInspectionResult(data) {
    return request('/gateway/telesales/call/quality/result/save', {
      method: 'post',
      data: {
        req: data,
      },
    })
  },
}
