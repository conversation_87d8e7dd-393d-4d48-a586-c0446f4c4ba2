<template>
  <div class="log-list-container">
    <div id="ele"></div>
  </div>
</template>
<script>
import psb from '@nibfe/platform-sdk'
import { REDIRECT_URL } from '@/lib/constants'
import graphCfg from '@nibfe/ccc-lowcode-render'
import { reportError, getUrlSearch } from '@/lib/utils'

export default {
  components: {},
  data() {
    return {
      pageMessage: '',
    }
  },
  created() {
    psb.config('1701767036mfpmsc', {
      masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
      redirectUrl: process.env.NODE_ENV === 'development' ? null : REDIRECT_URL,
    })
    graphCfg({
      el: '#ele',
      tenantId: 3,
      pageType: 'chart', // 图表类型
      remoteURL: process.env.VUE_APP_API_HOST,
      module: 'operate-log',
      appEnv: process.env.VUE_APP_ENV,
      templateId: process.env.VUE_APP_ENV === 'production' ? 239 : 2280,
      // 上线前注释，使用灰度能力
      // 本地开发可开启在模版未下发的情况下拉取交互
      // modelId: process.env.VUE_APP_ENV === 'production' ? 343 : 1656,
      initParams: {
        terminal: 0, //来自于PC，必填
        planSource: 0, //非来自拜访创建的，建议填0
      },
      lib: {
        reportError,
        getUrlSearch,
      },
    }).then(params => {
      const { instance, eventBus } = params || {}
      this.$bus = eventBus
      eventBus.$on('toOut', res => {
        this.pageMessage = res
      })
    })
  },
  methods: {
    // sendMessage() {
    //   this.$bus.$emit('toInner', '我是来自外部的消息' + Math.random())
    // },
  },
}
</script>

<style lang="scss" scoped>
.log-list-container {
  min-width: 1000px;
}
</style>
