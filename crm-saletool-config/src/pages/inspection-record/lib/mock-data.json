{"componentName": "Page", "id": "node_dockcviv8fo1", "props": {}, "docId": "doclluni9wa", "fileName": "/", "dataSource": {"list": []}, "state": {"HOST": {"type": "JSExpression", "value": "''"}, "INSPECTIONWORKBENCHPATH": {"type": "JSExpression", "value": "''"}, "envParams": {"type": "JSExpression", "value": "[{\n  \"name\": \"HOST\",\n  \"value\": {\n    \"development\": \"//apollo.nibcrm.test.sankuai.com\",\n    \"test\": \"//apollo.nibcrm.test.sankuai.com\",\n    \"production\": \"//apollo.meituan.com\",\n    \"mock\": \"\"\n  },\n  \"description\": \"域名\"\n}, {\n  \"name\": \"INSPECTIONWORKBENCHPATH\",\n  \"value\": {\n    \"development\": \"/inspection-workbench/index.html#/\",\n    \"test\": \"/crm-saletool-config/inspection-workbench/index.html#/\",\n    \"production\": \"/crm-saletool-config/inspection-workbench/index.html#/\",\n    \"mock\": \"\"\n  }\n}]"}, "searchFilter": {"type": "JSExpression", "value": "{\n  \"bizLine\": this.utils.getPageConstant('BIZLINE').general\n}"}, "tableModel": {"type": "JSExpression", "value": "{}"}, "controlQuery": {"type": "JSExpression", "value": "true"}}, "css": ".opColHeader {\n  /* display: flex; */\n  justify-content: center;\n}\n\n.opBtn {\n  width: 30px;\n  margin-right: 0;\n}\n\n.mtd-btn.mtd-btn-text {\n  color: #166ff7\n}\n\n/* 单元格垂直居中 */\n.mtd-table td {\n  vertical-align: middle !important;\n}\n\n/* 操作列的水平居中 */\n.mtd-table-cell {\n  justify-content: center;\n}\n\n/* 操作按钮右边距去除 */\n.pro-table-container .action-column .action-button {\n  margin-right: 0 !important;\n}\n\n/* 修复多行文本水平居中问题 */\n.pro-table-container .cell .content {\n  justify-content: center;\n}\n\n\n/* 修复多选列表头偏移的问题 */\n.mtd-table-column-selection .mtd-table-cell {\n  padding-right: 0;\n}", "lifeCycles": {"created": {"type": "JSFunction", "value": "function created() {\n  // this.constants.env是页面容器在运行时默认会注入的变量。\n  this.initEnv(this.constants.appEnv);\n}"}, "mounted": {"type": "JSFunction", "value": "function mounted() {\n  console.log('mounted'); // 选中url里带来的日期参数，若无则默认选择当天\n\n  const urlPayload = this.utils.getQueryParams() || {};\n  console.log('urlPayload:', urlPayload);\n  const today = new Date().toLocaleDateString('sv');\n  const selectedDate = urlPayload.date ? urlPayload.date : today;\n  this.searchFilterForm().setValuesIn('dateKey', selectedDate);\n  this.setState({\n    searchFilter: Object.assign({}, this.state.searchFilter, {\n      dateKey: this.transformDateStr2Int(selectedDate)\n    })\n  });\n  console.log('searchFilter:', this.state.searchFilter);\n  window.Vue.nextTick(() => {\n    this.setState({\n      controlQuery: !this.state.controlQuery\n    });\n  });\n}"}}, "methods": {"searchFilterForm": {"type": "JSFunction", "value": "function searchFilterForm() {\n  return this.$('searchFilterForm').form;\n}"}, "initEnv": {"type": "JSFunction", "value": "function initEnv(appEnv) {\n  console.log('appEnv', appEnv);\n  const {\n    envParams\n  } = this.state || {};\n  envParams.forEach(item => {\n    if (!!item.name) {\n      this.setState({\n        [item.name]: item.value[appEnv || \"test\"]\n      });\n    }\n  });\n}"}, "fixRequestParams": {"type": "JSFunction", "value": "function fixRequestParams(formValues) {\n  formValues.dateKey = this.transformDateStr2Int(formValues.dateKey);\n  return formValues;\n}"}, "clickFormSearchBtn": {"type": "JSFunction", "value": "function clickFormSearchBtn() {\n  const form = this.searchFilterForm();\n  let formValues = JSON.parse(JSON.stringify(form.getValuesIn('*')));\n  formValues = this.fixRequestParams(formValues); // 上方筛选项\n\n  console.log('formValues:', formValues);\n  this.setState({\n    searchFilter: Object.assign({}, this.state.searchFilter, formValues)\n  });\n  console.log('search filters:', this.state.searchFilter);\n  window.Vue.nextTick(() => {\n    this.setState({\n      controlQuery: !this.state.controlQuery\n    });\n  });\n}"}, "disabledDate": {"type": "JSFunction", "value": "function disabledDate(date) {\n  // 当天往前推最多选到90天\n  const today = new Date();\n  const minDateTime = new Date(today - 1000 * 3600 * 24 * 90);\n  return date < minDateTime || date > today;\n}"}, "afterReload": {"type": "JSFunction", "value": "function afterReload(list, response) {\n  console.log('afterReload', list, response);\n\n  if (response.code !== 200) {\n    let errorMsg = '请求失败';\n\n    if (response && (response.msg || response.message || response.errorMsg)) {\n      errorMsg = response.msg || response.message || response.errorMsg;\n    }\n\n    this.$mtd.message.error(errorMsg);\n  }\n\n  this.setState({\n    tableModel: list\n  });\n  window.Vue.nextTick(() => {\n    this.formatTableData();\n  });\n  console.log('tableModel', this.state.tableModel);\n}"}, "formatTableData": {"type": "JSFunction", "value": "function formatTableData() {\n  this.state.tableModel.dataSource.map(row => {\n    row.dateKey = this.transformInt2DateStr(row.dateKey);\n\n    if (row.hourPercent != null) {\n      if (row.hourPercent.startHour != null && row.hourPercent.endHour != null) {\n        row.timeRange = this.transformTimeRange2Str(row.hourPercent.startHour, row.hourPercent.endHour);\n      } else {\n        row.timeRange = '/';\n      }\n\n      row.percent = row.hourPercent.percent || 0;\n    }\n  });\n  console.log('dataSource after wash:', this.state.tableModel.dataSource);\n}"}, "clickDetail": {"type": "JSFunction", "value": "function clickDetail(ev) {\n  // console.log('click detail', ev.$index, ev.row)\n  // 点击某个质检任务的质检详情页，进入质检工作台列表，且需带有过滤条件：下发质检日期：选中当前任务日期；\n  window.location.href = `${window.location.origin}${this.state.INSPECTIONWORKBENCHPATH}?date=${ev.row.dateKey}`;\n}"}, "transformDateStr2Int": {"type": "JSFunction", "value": "function transformDateStr2Int(dateStr) {\n  if (!dateStr) return 0; // yyyy-MM-dd字符串转成yyyyMMdd数字\n\n  return parseInt(dateStr.replaceAll('-', '')) || 0;\n}"}, "transformInt2DateStr": {"type": "JSFunction", "value": "function transformInt2DateStr(dateInt) {\n  const dateStr = dateInt.toString();\n  return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`;\n}"}, "transformTimeRange2Str": {"type": "JSFunction", "value": "function transformTimeRange2Str(startHour, endHour) {\n  if (startHour == null || endHour == null || startHour < 0 || endHour > 24 || startHour > endHour) return '/';\n  const prefix1 = startHour < 10 ? '0' : '';\n  const prefix2 = endHour < 10 ? '0' : '';\n  return `${prefix1}${startHour}:00-${prefix2}${endHour}:00`;\n}"}, "toastMsg": {"type": "JSFunction", "value": "function toastMsg(content, type) {\n  this.$mtd.message({\n    message: content,\n    type: type\n  });\n}"}, "fetch": {"type": "JSFunction", "value": "function fetch(apiName, data, entire = false, needThrowError = true) {\n  return this.dataSourceMap[apiName].load(data).then(res => {\n    // 返回请求的全部内容\n    if (entire) {\n      return res;\n    } // 没有特殊需求直接返回数据部分\n\n\n    if (res && res.code === 200) {\n      return res.data;\n    } else {\n      let errorMsg = '请求失败';\n\n      if (res && (res.msg || res.message || res.errorMsg)) {\n        errorMsg = res.msg || res.message || res.errorMsg;\n      }\n\n      this.$mtd.message.error(errorMsg);\n\n      if (needThrowError) {\n        throw errorMsg;\n      }\n    }\n  }).catch(e => {\n    console.log(e);\n    throw e;\n  });\n}"}, "requestFailed": {"type": "JSFunction", "value": "function requestFailed(response) {\n  console.log('request failed', response);\n  this.$mtd.message.error('请求失败');\n}"}}, "originCode": "class LowcodeComponent extends Component {\n  state = {\n    \"HOST\": '',\n    \"INSPECTIONWORKBENCHPATH\": '',\n    envParams: [\n      {\n        \"name\": \"HOST\",\n        \"value\": {\n          \"development\": \"//apollo.nibcrm.test.sankuai.com\",\n          \"test\": \"//apollo.nibcrm.test.sankuai.com\",\n          \"production\": \"//apollo.meituan.com\",\n          \"mock\": \"\"\n        },\n        \"description\": \"域名\"\n      },\n      {\n        \"name\": \"INSPECTIONWORKBENCHPATH\",\n        \"value\": {\n          \"development\": \"/inspection-workbench/index.html#/\",\n          \"test\": \"/crm-saletool-config/inspection-workbench/index.html#/\",\n          \"production\": \"/crm-saletool-config/inspection-workbench/index.html#/\",\n          \"mock\": \"\"\n        }\n      }\n    ],\n    // 顶部筛选条件\n    \"searchFilter\": {\n      \"bizLine\": this.utils.getPageConstant('BIZLINE').general\n    },\n    // 质检记录列表表格数据模型\n    \"tableModel\": {},\n    // 控制质检记录列表表格刷新\n    \"controlQuery\": true,\n  }\n\n  get searchFilterForm() {\n    return this.$('searchFilterForm').form\n  }\n\n  created() {\n    // this.constants.env是页面容器在运行时默认会注入的变量。\n    this.initEnv(this.constants.appEnv)\n  }\n\n  initEnv(appEnv) {\n    console.log('appEnv', appEnv)\n    const { envParams } = this.state || {}\n    envParams.forEach(item => {\n      if (!!item.name) {\n        this.setState({ [item.name]: item.value[appEnv || \"test\"] })\n      }\n    })\n  }\n\n  mounted() {\n    console.log('mounted');\n    // 选中url里带来的日期参数，若无则默认选择当天\n    const urlPayload = this.utils.getQueryParams() || {}\n    console.log('urlPayload:', urlPayload)\n    const today = new Date().toLocaleDateString('sv')\n    const selectedDate = urlPayload.date ? urlPayload.date : today\n    this.searchFilterForm().setValuesIn('dateKey', selectedDate)\n    this.setState({\n      searchFilter: Object.assign({}, this.state.searchFilter, { dateKey: this.transformDateStr2Int(selectedDate) })\n    })\n    console.log('searchFilter:', this.state.searchFilter)\n    window.Vue.nextTick(() => {\n      this.setState({\n        controlQuery: !this.state.controlQuery\n      })\n    })\n  }\n\n  fixRequestParams(formValues) {\n    formValues.dateKey = this.transformDateStr2Int(formValues.dateKey)\n    return formValues\n  }\n\n  // 筛选与搜索\n  clickFormSearchBtn() {\n    const form = this.searchFilterForm()\n    let formValues = JSON.parse(JSON.stringify(form.getValuesIn('*')))\n    formValues = this.fixRequestParams(formValues)\n    // 上方筛选项\n    console.log('formValues:', formValues)\n    this.setState({\n      searchFilter: Object.assign({}, this.state.searchFilter, formValues)\n    })\n    console.log('search filters:', this.state.searchFilter)\n    window.Vue.nextTick(() => {\n      this.setState({\n        controlQuery: !this.state.controlQuery\n      })\n    })\n  }\n\n  disabledDate(date) {\n    // 当天往前推最多选到90天\n    const today = new Date()\n    const minDateTime = new Date(today - 1000 * 3600 * 24 * 90)\n    return date < minDateTime || date > today\n  }\n\n  afterReload(list, response) {\n    console.log('afterReload', list, response)\n    if (response.code !== 200) {\n      let errorMsg = '请求失败'\n      if (response && (response.msg || response.message || response.errorMsg)) {\n        errorMsg = response.msg || response.message || response.errorMsg\n      }\n      this.$mtd.message.error(errorMsg)\n    }\n\n    this.setState({\n      tableModel: list\n    })\n    window.Vue.nextTick(() => {\n      this.formatTableData()\n    })\n    console.log('tableModel', this.state.tableModel)\n  }\n\n  // 洗数据\n  formatTableData() {\n    this.state.tableModel.dataSource.map(row => {\n      row.dateKey = this.transformInt2DateStr(row.dateKey)\n      if (row.hourPercent != null) {\n        if (row.hourPercent.startHour != null && row.hourPercent.endHour != null){\n          row.timeRange = this.transformTimeRange2Str(row.hourPercent.startHour, row.hourPercent.endHour)\n        } else {\n          row.timeRange = '/'\n        }\n        row.percent = row.hourPercent.percent || 0\n      }  \n    })\n    console.log('dataSource after wash:', this.state.tableModel.dataSource)\n  }\n\n  clickDetail(ev) {\n    // console.log('click detail', ev.$index, ev.row)\n    // 点击某个质检任务的质检详情页，进入质检工作台列表，且需带有过滤条件：下发质检日期：选中当前任务日期；\n    window.location.href = `${window.location.origin}${this.state.INSPECTIONWORKBENCHPATH}?date=${ev.row.dateKey}`\n  }\n\n  // 工具函数\n  transformDateStr2Int(dateStr) {\n    if (!dateStr) return 0\n    // yyyy-MM-dd字符串转成yyyyMMdd数字\n    return parseInt(dateStr.replaceAll('-', '')) || 0\n  }\n\n  transformInt2DateStr(dateInt) {\n    const dateStr = dateInt.toString()\n    return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`\n  }\n\n  transformTimeRange2Str(startHour, endHour) {\n    if (startHour == null || endHour == null || \n      startHour < 0 || endHour > 24 || startHour > endHour)\n      return '/'\n    const prefix1 = startHour < 10 ? '0' : ''\n    const prefix2 = endHour < 10 ? '0' : ''\n    return `${prefix1}${startHour}:00-${prefix2}${endHour}:00`\n  }\n\n  toastMsg(content, type) {\n    this.$mtd.message({\n      message: content,\n      type: type\n    })\n  }\n\n  fetch(apiName, data, entire = false, needThrowError = true) {\n    return this.dataSourceMap[apiName].load(data)\n      .then(res => {\n        // 返回请求的全部内容\n        if (entire) {\n          return res\n        }\n        // 没有特殊需求直接返回数据部分\n        if (res && res.code === 200) {\n          return res.data\n        } else {\n          let errorMsg = '请求失败'\n          if (res && (res.msg || res.message || res.errorMsg)) {\n            errorMsg = res.msg || res.message || res.errorMsg\n          }\n          this.$mtd.message.error(errorMsg)\n          if (needThrowError) {\n            throw errorMsg\n          }\n        }\n      })\n      .catch(e => {\n        console.log(e)\n        throw e\n      })\n  }\n\n  requestFailed(response) {\n    console.log('request failed', response)\n    this.$mtd.message.error('请求失败')\n  }\n}", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "pageDataSource": {"list": [{"id": "HOST", "desp": "接口域名，可配置test环境和production环境，配置示例：https://***.sankuai.com", "type": "constant", "sort": 0, "fixed": "top", "isMultiple": true, "fixedValues": ["test", "production"]}, {"id": "HEADERS", "desp": "接口请求头，配置后会自动带入到接口配置中，支持自行修改，access-token默认从this.utils.getSSOToken()获取。", "type": "constant", "sort": 1, "fixed": "top", "isMultiple": true, "fixedValues": ["swimlane"], "valueObject": {"swimlane": "", "access-token": {"type": "JSExpression", "value": "this.utils.getSSOToken()"}, "Content-Type": "application/json"}}, {"type": "constant", "isMultiple": false, "id": "BIZLINE", "desp": "业务线，1006-餐；1007-综", "valueSingle": {"type": "JSFunction", "value": "{\n  \"food\": 1006,\n  \"general\": 1007\n}"}}]}, "constants": {"HOST": "", "HEADERS": {"swimlane": "", "access-token": {"type": "JSExpression", "value": "this.utils.getSSOToken()"}, "Content-Type": "application/json"}, "BIZLINE": {"food": 1006, "general": 1007}}, "utils": {}, "children": [{"componentName": "DmvFormilyForm", "id": "node_ocllqccdf4bf", "props": {"showColon": false, "labelPosition": "right", "appContext": {"type": "JSExpression", "value": "this"}, "rules": {"type": "JSExpression", "value": "this.state.rules"}, "labelWidth": 100, "style": {"marginLeft": "20px", "marginRight": "20px", "backgroundColor": "rgba(155,155,155,0.27)", "display": "flex", "paddingTop": "10px", "marginTop": "20px", "marginBottom": "20px", "alignItems": "center"}, "ref": "searchFilterForm"}, "docId": "docllqccdf4", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilySpace", "id": "node_ocllqccdf4bg", "props": {"schemaProps": {"title": "", "x-component-props": {"isHidenWhenCollapsed": false, "wrap": false}, "description": "", "required": false, "x-decorator-props": {"labelWidthAuto": true, "labelSizeAuto": true}}, "ref": "searchFilterForm", "style": {"display": "flex", "alignItems": "center"}}, "docId": "docllqccdf4", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyDatePicker", "id": "node_ocllp3j16faj", "props": {"schemaProps": {"title": "质检日期", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"size": "不设置", "isHidenWhenCollapsed": false, "type": "date", "clearable": true, "placement": "bottom-start", "showBtnNow": true, "splitPanels": true, "showWeekNumbers": false, "appendToContainer": true, "weekStart": 1, "multiple": false, "disabledDate": {"type": "JSFunction", "value": "function(){ return this.disabledDate.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "valueFormat": {"type": "JSExpression", "value": "'yyyy-MM-dd'", "mock": ""}}, "name": "<PERSON><PERSON><PERSON>"}}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvButton", "id": "node_ocllp3j16fan", "props": {"type": "primary", "children": "主按钮", "text": "搜索", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "clickFormSearchBtn"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.clickFormSearchBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}, {"componentName": "DmvContainer", "id": "node_oclluuutaa1", "props": {"direction": "horizontal", "style": {"paddingLeft": "20px", "paddingRight": "20px"}}, "docId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "VpdmProTable", "id": "node_oclluuutcu1", "props": {"listParams": {"listType": "listProps", "listPropsType": "props", "listProps": {"request": {"method": "post", "url": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/InspectionTService/queryInspectionStatistics`", "mock": "https://yapi.sankuai.com/mock/35239/check/record"}, "responseMap": {"dataSource": "data.records", "total": "data.total"}, "params": {"type": "JSExpression", "value": "this.state.searchFilter"}, "requestMap": {"currentPage": "page"}}, "autoSearch": false, "searchWatchProp": {"type": "JSExpression", "value": "this.state.controlQuery"}, "afterReload": {"type": "JSFunction", "value": "function(){ return this.afterReload.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "requestFailed": {"type": "JSFunction", "value": "function(){ return this.requestFailed.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}}, "columnArray": [{"label": "抽查日期", "prop": "<PERSON><PERSON><PERSON>", "content": [{"contextProp": "<PERSON><PERSON><PERSON>", "type": "text"}], "width": "150", "header-align": "center", "align": "center", "sortable": false, "filterable": false, "resizable": false, "slot": false}, {"label": "抽查时段", "prop": "timeRange", "content": [{"contextProp": "timeRange", "type": "text"}], "width": "150", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}, {"label": "抽查比例", "prop": "percent", "content": [{"contextProp": "percent", "type": "text", "suffix": "%", "tipEnabled": false}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "质检抽单量", "prop": "assignCount", "content": [{"contextProp": "assignCount", "type": "amount"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "完成质检量", "prop": "finishCount", "content": [{"contextProp": "finishCount", "type": "amount"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "未完成质检量", "prop": "todoCount", "content": [{"contextProp": "todoCount", "type": "amount"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "质检通过量", "prop": "passCount", "content": [{"contextProp": "passCount", "type": "amount"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "质检不通过量", "prop": "rejectCount", "content": [{"contextProp": "rejectCount", "type": "amount"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}], "pagination": {"enabled": true, "showQuickJumper": true, "showSizeChanger": true, "showTotal": true, "currentPage": 1, "pageSize": 5, "size": "不设置", "pager-count": 7}, "actionColumn": {"enabled": true, "visibleButtonCount": 1, "styleType": "text-primary", "width": "100", "buttonArray": [{"label": "查看详情", "eventType": "func", "url": "https://dev.sankuai.com/code/home", "target": "_blank", "type": "text", "buttonClass": "buttonClass", "link": {"target": "_blank"}, "func": {"type": "JSFunction", "value": "function(){ return this.clickDetail.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}], "labelClass": "opColHeader"}, "table": {"size": "不设置", "showHeader": true, "horizontalVirtual": false, "emptyText": "暂无数据", "loadingMessage": "正在加载中", "rowClass": "", "cellClass": "", "headerRowClass": "", "headerCellClass": ""}, "style": {"width": "100%"}, "cell-default-0": "", "cell-default-1": "", "cell-default-2": "", "cell-default-3": "", "cell-default-4": "", "cell-default-5": "", "cell-default-6": "", "cell-default-7": ""}, "docId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}