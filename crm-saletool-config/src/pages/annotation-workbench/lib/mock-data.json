{"componentName": "Page", "id": "node_dockcviv8fo1", "props": {}, "docId": "docllx3lomg", "fileName": "/", "dataSource": {"list": [{"options": {"headers": {"swimlane": "", "Content-Type": "application/json"}, "params": {"bizLine": 1007}, "method": "POST", "isCors": true, "timeout": 5000, "uriPath": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/CommonTService/queryAddress`"}, "uri": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/CommonTService/queryAddress`"}}, "type": "fetch", "source": "NORMAL_API", "isInit": false, "isAutoHost": false, "id": "query<PERSON>ddress", "desp": "查询地址列表", "dataHandler": {"type": "JSFunction", "value": "function(res) { return res.data }"}}, {"options": {"headers": {"swimlane": "", "Content-Type": "application/json"}, "params": {"groupKey": 1, "bizLine": 1007}, "method": "POST", "isCors": true, "timeout": 5000, "uriPath": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/queryUserRole`"}, "uri": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/UserTService/queryUserRole`"}}, "type": "fetch", "source": "NORMAL_API", "isInit": false, "isAutoHost": false, "id": "queryUserRole", "desp": "用户角色查询（是否外包，是否组长）"}, {"options": {"headers": {"swimlane": "", "Content-Type": "application/json"}, "params": {"bizLine": 1007}, "method": "POST", "isCors": true, "timeout": 5000, "uriPath": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/ManualAnnotationTService/batchReSign`"}, "uri": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/ManualAnnotationTService/batchReSign`"}}, "type": "fetch", "source": "NORMAL_API", "isInit": false, "isAutoHost": false, "id": "batchReSign", "desp": "批量改签"}]}, "state": {"HOST": {"type": "JSExpression", "value": "''"}, "SHOPURLPREFIX": {"type": "JSExpression", "value": "'https://apollo.meituan.com/shop/view'"}, "JUMPURLPATH": {"type": "JSExpression", "value": "''"}, "envParams": {"type": "JSExpression", "value": "[{\n  \"name\": \"HOST\",\n  \"value\": {\n    \"development\": \"//apollo.nibcrm.test.sankuai.com\",\n    \"test\": \"//apollo.nibcrm.test.sankuai.com\",\n    \"production\": \"//apollo.meituan.com\",\n    \"mock\": \"\"\n  },\n  \"description\": \"域名\"\n}, // {\n//   \"name\": \"HOST\",\n//   \"value\": {\n//     \"development\": \"//yapi.sankuai.com/thrift/mock/project/45358\",\n//     \"test\": \"//yapi.sankuai.com/thrift/mock/project/45358\",\n//     \"production\": \"\",\n//     \"mock\": \"\"\n//   },\n//   \"description\": \"域名\"\n// },\n{\n  \"name\": \"JUMPURLPATH\",\n  \"value\": {\n    \"development\": \"/mark-detail/index.html#/mark-operation\",\n    \"test\": \"/crm-saletool-config/mark-detail/index.html#/mark-operation\",\n    \"production\": \"/crm-saletool-config/mark-detail/index.html#/mark-operation\",\n    \"mock\": \"\"\n  }\n}]"}, "tableModel": {"type": "JSExpression", "value": "{}"}, "controlQuery": {"type": "JSExpression", "value": "true"}, "tableSelection": {"type": "JSExpression", "value": "[]"}, "tmpAnnotationRecord": {"type": "JSExpression", "value": "[]"}, "searchFilter": {"type": "JSExpression", "value": "{\n  \"bizLine\": this.utils.getPageConstant('BIZLINE').general\n}"}, "changeModal": {"type": "JSExpression", "value": "{\n  \"isShow\": false\n}"}, "userInfo": {"type": "JSExpression", "value": "null"}}, "css": ".opColHeader {\n  /* display: flex; */\n  justify-content: center;\n}\n\n.opBtn {\n  width: 30px;\n  margin-right: 0;\n}\n\n.mtd-btn.mtd-btn-text {\n  color: #166ff7\n}\n\n/* 单元格垂直居中 */\n.mtd-table td {\n  vertical-align: middle !important;\n}\n\n/* 操作列的水平居中 */\n.mtd-table-cell {\n  justify-content: center;\n}\n\n/* 操作按钮右边距去除 */\n.pro-table-container .action-column .action-button {\n  margin-right: 0 !important;\n}\n\n/* 修复多行文本水平居中问题 */\n.pro-table-container .cell .content {\n  justify-content: center;\n}\n\n\n/* 修复多选列表头偏移的问题 */\n.mtd-table-column-selection .mtd-table-cell {\n  padding-right: 12px;\n}", "lifeCycles": {"created": {"type": "JSFunction", "value": "function created() {\n  // this.constants.env是页面容器在运行时默认会注入的变量。\n  this.initEnv(this.constants.appEnv);\n}"}, "mounted": {"type": "JSFunction", "value": "async function mounted() {\n  // console.log(this.state)\n  console.log('mounted');\n  const userInfo = await this.getUserInfo();\n  this.setState({\n    userInfo\n  });\n  window.Vue.nextTick(() => {\n    this.initSearchFilter();\n  });\n}"}}, "methods": {"searchFilterForm": {"type": "JSFunction", "value": "function searchFilterForm() {\n  return this.$('searchFilterForm').form;\n}"}, "changeForm": {"type": "JSFunction", "value": "function changeForm() {\n  return this.$('changeForm').form;\n}"}, "initEnv": {"type": "JSFunction", "value": "function initEnv(appEnv) {\n  console.log('appEnv', appEnv);\n  const {\n    envParams\n  } = this.state || {};\n  envParams.forEach(item => {\n    if (!!item.name) {\n      this.setState({\n        [item.name]: item.value[appEnv || \"test\"]\n      });\n    }\n  });\n}"}, "getUserInfo": {"type": "JSFunction", "value": "function getUserInfo() {\n  return new Promise((resolve, reject) => {\n    this.fetch('queryUserRole').then(data => {\n      console.log('userInfo:', data);\n      resolve(data);\n    }).catch(err => {\n      console.log(err);\n      resolve({});\n    });\n  });\n}"}, "initSearchFilter": {"type": "JSFunction", "value": "function initSearchFilter() {\n  // 默认下发日期选择当天，完成日期不筛选\n  const today = new Date().toLocaleDateString('sv');\n  const initialValues = {\n    dateKey: today,\n    annotationStatus: this.getAnnotationStatus('todo')\n  };\n  this.loadProvince();\n  this.searchFilterForm().setValues(initialValues);\n  this.setState({\n    searchFilter: Object.assign({}, this.state.searchFilter, {\n      dateKey: this.transformDateStr2Int(today),\n      annotationStatus: initialValues.annotationStatus\n    }),\n    controlQuery: !this.state.controlQuery\n  });\n}"}, "loadProvince": {"type": "JSFunction", "value": "function loadProvince() {\n  this.fetch('queryAddress', {\n    level: 0\n  }).then(data => {\n    const provinceList = data.map(item => {\n      return {\n        value: item.id || 0,\n        label: item.name || '',\n        isLeaf: false\n      };\n    });\n    this.searchFilterForm().setFieldState('dpCityId', {\n      dataSource: provinceList\n    });\n  }).catch(err => {\n    console.log(err);\n  });\n}"}, "loadCity": {"type": "JSFunction", "value": "function loadCity(item, callback) {\n  const url = `${this.state.HOST}/gateway/bml/guiguzi/annotation/CommonTService/queryAddress`;\n  const params = {\n    level: 1,\n    id: item.value || 0,\n    bizLine: this.utils.getPageConstant('BIZLINE').general\n  };\n  fetch(url, {\n    method: 'POST',\n    body: JSON.stringify(params)\n  }).then(response => response.json()).then(({\n    data\n  }) => {\n    // console.log('cityList:', data)\n    const cityList = data.map(item => {\n      return {\n        value: item.id || 0,\n        label: item.name || '',\n        isLeaf: true\n      };\n    });\n    callback(cityList);\n  }, err => {\n    console.log(err);\n    callback([]);\n  });\n}"}, "fixRequestParams": {"type": "JSFunction", "value": "function fixRequestParams(formValues) {\n  formValues.dateKey = this.transformDateStr2Int(formValues.dateKey);\n\n  if (formValues.hasOwnProperty('finishDate')) {\n    if (formValues.finishDate == \"\") {\n      formValues.finishDate = null;\n    } else {\n      formValues.finishDate = this.transformDateStr2Int(formValues.finishDate);\n    }\n  }\n\n  if (formValues.hasOwnProperty('annotationStatus') && formValues.annotationStatus == \"\") formValues.annotationStatus = null;\n  if (formValues.hasOwnProperty('inspectionStatus') && formValues.inspectionStatus == \"\") formValues.inspectionStatus = null;\n\n  if (formValues.hasOwnProperty('dpCityId')) {\n    if (Array.isArray(formValues.dpCityId) && formValues.dpCityId.length > 0) {\n      formValues.dpCityId = formValues.dpCityId[formValues.dpCityId.length - 1];\n    } else {\n      formValues.dpCityId = null;\n    }\n  }\n\n  if (formValues.hasOwnProperty('misOrName') && formValues.misOrName) formValues.misOrName = formValues.misOrName.trim();\n  if (formValues.hasOwnProperty('searchKeyword') && formValues.searchKeyword) formValues.searchKeyword = formValues.searchKeyword.trim();\n  return formValues;\n}"}, "clickFormSearchBtn": {"type": "JSFunction", "value": "function clickFormSearchBtn() {\n  const form = this.searchFilterForm();\n  let formValues = JSON.parse(JSON.stringify(form.getValuesIn('*')));\n  formValues = this.fixRequestParams(formValues); // 上方筛选项\n\n  console.log('formValues:', formValues);\n  this.setState({\n    searchFilter: Object.assign({}, this.state.searchFilter, formValues)\n  });\n  console.log('search filters:', this.state.searchFilter);\n  window.Vue.nextTick(() => {\n    // 防止表格组件未渲染，因此将触发请求操作放在vue.nextTick\n    this.setState({\n      controlQuery: !this.state.controlQuery\n    });\n  });\n}"}, "clickBatchChangeBtn": {"type": "JSFunction", "value": "function clickBatchChangeBtn() {\n  this.setState({\n    changeModal: {\n      isShow: true\n    },\n    tmpAnnotationRecord: this.state.tableSelection\n  });\n}"}, "clickChangeBtn": {"type": "JSFunction", "value": "function clickChangeBtn(ev) {\n  // console.log('click change', ev.$index)\n  // console.log('row data', ev.row)\n  this.setState({\n    changeModal: {\n      isShow: true\n    },\n    tmpAnnotationRecord: [ev.row]\n  });\n}"}, "clickChangeModalOk": {"type": "JSFunction", "value": "function clickChangeModalOk(ev) {\n  const form = this.changeForm();\n  form.validate().then(async () => {\n    const mis = form.getValuesIn('mis').trim();\n    const ids = this.state.tmpAnnotationRecord.map(item => item.id) || []; // console.log('target mis:', mis, ids)\n\n    await this.fetch('batchReSign', {\n      ids,\n      mis\n    }).then(data => {\n      this.$mtd.confirm({\n        title: `改签成功`,\n        message: `共${this.state.tmpAnnotationRecord.length}条标注单已改签至${mis}`,\n        width: '430px',\n        showCancelButton: false\n      }).catch(() => {});\n    }).catch(err => {\n      console.log(err);\n    }); // 改签操作后清除已选中项，重新请求表格接口\n\n    this.setState({\n      changeModal: {\n        isShow: false\n      },\n      tmpAnnotationRecord: [],\n      tableSelection: []\n    });\n    setTimeout(() => {\n      this.setState({\n        controlQuery: !this.state.controlQuery\n      });\n    }, 1000);\n  }).catch(err => {\n    console.log('validate error:', err);\n  });\n}"}, "clickChangeModalClose": {"type": "JSFunction", "value": "function clickChangeModalClose(ev) {\n  this.setState({\n    changeModal: {\n      isShow: false\n    }\n  });\n}"}, "clickDetailBtn": {"type": "JSFunction", "value": "function clickDetailBtn(ev) {\n  // console.log('click detail', ev.$index)\n  // console.log('row data', ev.row)\n  const accessControl = this.checkAnnotationFinishExpire(ev.row.annotationStatus) ? 'readOnly' : 'editable';\n  const params = {\n    markId: ev.row.id,\n    type: 'mark',\n    accessControl\n  };\n  const jumpUrl = `${window.location.origin}${this.state.JUMPURLPATH}?${this.generateUrlQuery(params)}`; // console.log('detail jumpUrl:', jumpUrl)\n\n  window.open(jumpUrl, target = '_blank');\n}"}, "hideChangeBtn": {"type": "JSFunction", "value": "function hideChangeBtn(ev) {\n  if (!ev) return false;\n  const annotationStatus = ev.row.annotationStatus;\n  const hideChange = !this.checkIsLeader() || this.checkAnnotationFinishExpire(annotationStatus);\n  return hideChange;\n}"}, "checkboxable": {"type": "JSFunction", "value": "function checkboxable(row) {\n  return !this.checkAnnotationFinishExpire(row.annotationStatus);\n}"}, "indexOfSelection": {"type": "JSFunction", "value": "function indexOfSelection(row, selection) {\n  return selection.map(s => s.id).indexOf(row.id);\n}"}, "afterReload": {"type": "JSFunction", "value": "function afterReload(list, response) {\n  console.log('afterReload', list, response);\n\n  if (response.code !== 200) {\n    let errorMsg = '请求失败';\n\n    if (response && (response.msg || response.message || response.errorMsg)) {\n      errorMsg = response.msg || response.message || response.errorMsg;\n    }\n\n    this.$mtd.message.error(errorMsg);\n  }\n\n  this.setState({\n    tableModel: list\n  });\n  window.Vue.nextTick(() => {\n    this.formatTableData();\n  });\n  console.log('tableModel', this.state.tableModel);\n}"}, "formatTableData": {"type": "JSFunction", "value": "function formatTableData() {\n  this.state.tableModel.dataSource.map(row => {\n    if (row.operatorOutsourcing) {\n      row.operatorName = `【外】${row.operatorName}`;\n    }\n\n    if (!row.finishTime) row.finishTime = '/';\n  });\n  console.log('dataSource after wash:', this.state.tableModel.dataSource);\n}"}, "transformDateStr2Int": {"type": "JSFunction", "value": "function transformDateStr2Int(dateStr) {\n  if (!dateStr) return 0; // yyyy-MM-dd字符串转成yyyyMMdd数字\n\n  return parseInt(dateStr.replaceAll('-', '')) || 0;\n}"}, "transformInt2DateStr": {"type": "JSFunction", "value": "function transformInt2DateStr(dateInt) {\n  const dateStr = dateInt.toString();\n  return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`;\n}"}, "checkIsLeader": {"type": "JSFunction", "value": "function checkIsLeader() {\n  return this.state.userInfo && this.state.userInfo.role === this.utils.getPageConstant('ROLEENUM').leader;\n}"}, "checkIsWb": {"type": "JSFunction", "value": "function checkIsWb() {\n  return this.state.userInfo && this.state.userInfo.outsourcing;\n}"}, "checkAnnotationFinishExpire": {"type": "JSFunction", "value": "function checkAnnotationFinishExpire(status) {\n  return status != this.getAnnotationStatus('todo');\n}"}, "getAnnotationStatus": {"type": "JSFunction", "value": "function getAnnotationStatus(type) {\n  return this.utils.getPageConstant('ANNOTATIONSTATUSENUM')[type];\n}"}, "getInspectionStatus": {"type": "JSFunction", "value": "function getInspectionStatus(type) {\n  return this.utils.getPageConstant('INSPECTIONSTATUSENUM')[type];\n}"}, "disabledDate": {"type": "JSFunction", "value": "function disabledDate(date) {\n  // 本次下发日期和完成日期是当天往前推最多选到90天\n  const today = new Date();\n  const minDateTime = new Date(today - 1000 * 3600 * 24 * 90);\n  return date < minDateTime || date > today;\n}"}, "fetch": {"type": "JSFunction", "value": "function fetch(apiName, data, entire = false, needThrowError = true) {\n  return this.dataSourceMap[apiName].load(data).then(res => {\n    // 返回请求的全部内容\n    if (entire) {\n      return res;\n    } // 没有特殊需求直接返回数据部分\n\n\n    if (res && res.code === 200) {\n      return res.data;\n    } else {\n      let errorMsg = '请求失败';\n\n      if (res && (res.msg || res.message || res.errorMsg)) {\n        errorMsg = res.msg || res.message || res.errorMsg;\n      }\n\n      this.$mtd.message.error(errorMsg);\n\n      if (needThrowError) {\n        throw errorMsg;\n      }\n    }\n  }).catch(e => {\n    console.log(e);\n    throw e;\n  });\n}"}, "generateUrlQuery": {"type": "JSFunction", "value": "function generateUrlQuery(params) {\n  let queryArr = [];\n\n  for (const key in params) {\n    if (params.hasOwnProperty(key)) {\n      queryArr.push(`${key}=${params[key]}`);\n    }\n  }\n\n  return queryArr.join('&');\n}"}, "requestFailed": {"type": "JSFunction", "value": "function requestFailed(response) {\n  console.log('request failed', response);\n  this.$mtd.message.error('请求失败');\n}"}}, "originCode": "class LowcodeComponent extends Component {\n  state = {\n    \"HOST\": '',\n    \"SHOPURLPREFIX\": 'https://apollo.meituan.com/shop/view',\n    \"JUMPURLPATH\": '',\n    envParams: [\n      {\n        \"name\": \"HOST\",\n        \"value\": {\n          \"development\": \"//apollo.nibcrm.test.sankuai.com\",\n          \"test\": \"//apollo.nibcrm.test.sankuai.com\",\n          \"production\": \"//apollo.meituan.com\",\n          \"mock\": \"\"\n        },\n        \"description\": \"域名\"\n      },\n      // {\n      //   \"name\": \"HOST\",\n      //   \"value\": {\n      //     \"development\": \"//yapi.sankuai.com/thrift/mock/project/45358\",\n      //     \"test\": \"//yapi.sankuai.com/thrift/mock/project/45358\",\n      //     \"production\": \"\",\n      //     \"mock\": \"\"\n      //   },\n      //   \"description\": \"域名\"\n      // },\n      {\n        \"name\": \"JUMPURLPATH\",\n        \"value\": {\n          \"development\": \"/mark-detail/index.html#/mark-operation\",\n          \"test\": \"/crm-saletool-config/mark-detail/index.html#/mark-operation\",\n          \"production\": \"/crm-saletool-config/mark-detail/index.html#/mark-operation\",\n          \"mock\": \"\"\n        }\n      }\n    ],\n    // 标注单列表表格数据模型\n    \"tableModel\": {},\n    // 控制标注单列表表格刷新\n    \"controlQuery\": true,\n    // 标注单列表表格选中项\n    \"tableSelection\": [],\n    // 打开改签弹窗时，暂存需要改签的标注单\n    \"tmpAnnotationRecord\": [],\n    // 顶部筛选条件\n    \"searchFilter\": {\n      \"bizLine\": this.utils.getPageConstant('BIZLINE').general\n    },\n    // 改签弹窗\n    \"changeModal\": {\n      \"isShow\": false,\n    },\n    \"userInfo\": null,\n  }\n\n  get searchFilterForm() {\n    return this.$('searchFilterForm').form\n  }\n\n  get changeForm() {\n    return this.$('changeForm').form\n  }\n\n  created() {\n    // this.constants.env是页面容器在运行时默认会注入的变量。\n    this.initEnv(this.constants.appEnv)\n  }\n\n  initEnv(appEnv) {\n    console.log('appEnv', appEnv)\n    const { envParams } = this.state || {}\n    envParams.forEach(item => {\n      if (!!item.name) {\n        this.setState({ [item.name]: item.value[appEnv || \"test\"] })\n      }\n    })\n  }\n  \n  async mounted() {\n    // console.log(this.state)\n    console.log('mounted');\n    const userInfo = await this.getUserInfo()\n    this.setState({ userInfo })\n    window.Vue.nextTick(() => {\n      this.initSearchFilter()\n    })\n  }\n\n  getUserInfo() {\n    return new Promise((resolve, reject) => {\n      this.fetch('queryUserRole').then(data => {\n        console.log('userInfo:', data)\n        resolve(data)\n      }).catch(err => {\n        console.log(err)\n        resolve({})\n      })\n    })\n  }\n\n  // 筛选与搜索\n  initSearchFilter() {\n    // 默认下发日期选择当天，完成日期不筛选\n    const today = new Date().toLocaleDateString('sv')\n    const initialValues = {\n      dateKey: today,\n      annotationStatus: this.getAnnotationStatus('todo')\n    }\n    this.loadProvince()\n    this.searchFilterForm().setValues(initialValues)\n    this.setState({\n      searchFilter: Object.assign({}, this.state.searchFilter, {\n        dateKey: this.transformDateStr2Int(today),\n        annotationStatus: initialValues.annotationStatus\n      }),\n      controlQuery: !this.state.controlQuery\n    })\n  }\n\n  loadProvince() {\n    this.fetch('queryAddress', {level: 0}).then(data => {\n      const provinceList = data.map(item => {\n        return {\n          value: item.id || 0,\n          label: item.name || '',\n          isLeaf: false\n        }\n      })\n      this.searchFilterForm().setFieldState('dpCityId', {\n        dataSource: provinceList\n      })\n    }).catch(err => {\n      console.log(err)\n    })\n  }\n\n  loadCity(item, callback) {\n    const url = `${this.state.HOST}/gateway/bml/guiguzi/annotation/CommonTService/queryAddress`\n    const params = { level: 1, id: item.value || 0, bizLine: this.utils.getPageConstant('BIZLINE').general}\n    fetch(url, {\n      method: 'POST',\n      body: JSON.stringify(params)\n    })\n      .then(response => response.json())\n      .then(({ data }) => {\n        // console.log('cityList:', data)\n        const cityList = data.map(item => {\n          return {\n            value: item.id || 0,\n            label: item.name || '',\n            isLeaf: true\n          }\n        })\n        callback(cityList)\n      }, (err) => {\n        console.log(err)\n        callback([])\n      })\n  }\n\n  fixRequestParams(formValues) {\n    formValues.dateKey = this.transformDateStr2Int(formValues.dateKey)\n    if (formValues.hasOwnProperty('finishDate')) {\n      if (formValues.finishDate == \"\") {\n        formValues.finishDate = null\n      }\n      else {\n        formValues.finishDate = this.transformDateStr2Int(formValues.finishDate)\n      }\n    }\n    if (formValues.hasOwnProperty('annotationStatus') && formValues.annotationStatus == \"\")\n      formValues.annotationStatus = null\n    if (formValues.hasOwnProperty('inspectionStatus') && formValues.inspectionStatus == \"\")\n      formValues.inspectionStatus = null\n    if (formValues.hasOwnProperty('dpCityId')) {\n      if (Array.isArray(formValues.dpCityId) && formValues.dpCityId.length > 0) {\n        formValues.dpCityId = formValues.dpCityId[formValues.dpCityId.length - 1]\n      } else {\n        formValues.dpCityId = null\n      }\n    }\n    if (formValues.hasOwnProperty('misOrName') && formValues.misOrName)\n      formValues.misOrName = formValues.misOrName.trim()\n    if (formValues.hasOwnProperty('searchKeyword') && formValues.searchKeyword)\n      formValues.searchKeyword = formValues.searchKeyword.trim()\n    return formValues\n  }\n\n  clickFormSearchBtn() {\n    const form = this.searchFilterForm()\n    let formValues = JSON.parse(JSON.stringify(form.getValuesIn('*')))\n    formValues = this.fixRequestParams(formValues)\n    // 上方筛选项\n    console.log('formValues:', formValues)\n    this.setState({\n      searchFilter: Object.assign({}, this.state.searchFilter, formValues)\n    })\n    console.log('search filters:', this.state.searchFilter)\n    window.Vue.nextTick(() => {\n      // 防止表格组件未渲染，因此将触发请求操作放在vue.nextTick\n      this.setState({\n        controlQuery: !this.state.controlQuery\n      })\n    })\n  }\n  \n  clickBatchChangeBtn() {\n    this.setState({\n      changeModal: {\n        isShow: true\n      },\n      tmpAnnotationRecord: this.state.tableSelection\n    })\n  }\n\n  clickChangeBtn(ev) {\n    // console.log('click change', ev.$index)\n    // console.log('row data', ev.row)\n    this.setState({\n      changeModal: {\n        isShow: true\n      },\n      tmpAnnotationRecord: [ev.row]\n    })\n    \n  }\n\n  // 改签弹窗\n  clickChangeModalOk(ev) {\n    const form = this.changeForm()\n    form.validate().then(async () => {\n      const mis = form.getValuesIn('mis').trim()\n      const ids = this.state.tmpAnnotationRecord.map(item => item.id) || []\n      // console.log('target mis:', mis, ids)\n      await this.fetch('batchReSign', {ids, mis}).then(data => {\n        this.$mtd.confirm({\n          title: `改签成功`,\n          message: `共${this.state.tmpAnnotationRecord.length}条标注单已改签至${mis}`,\n          width: '430px',\n          showCancelButton: false\n        }).catch(() => { });\n      \n      }).catch(err => {\n        console.log(err)\n      })\n\n      // 改签操作后清除已选中项，重新请求表格接口\n      this.setState({\n        changeModal: {\n          isShow: false\n        },\n        tmpAnnotationRecord: [],\n        tableSelection: []\n      })\n      setTimeout(() => {\n        this.setState({\n          controlQuery: !this.state.controlQuery\n        })\n      }, 1000)\n    }).catch(err => {\n      console.log('validate error:', err)\n    })\n  }\n\n  clickChangeModalClose(ev) {\n    this.setState({\n      changeModal: {\n        isShow: false\n      }\n    })\n  }\n\n  // 点击跳转到标注单详情（区分是否为查询版）\n  clickDetailBtn(ev) {\n    // console.log('click detail', ev.$index)\n    // console.log('row data', ev.row)\n    const accessControl = this.checkAnnotationFinishExpire(ev.row.annotationStatus) ? 'readOnly' : 'editable'\n    const params = {markId: ev.row.id, type: 'mark', accessControl}\n    const jumpUrl = `${window.location.origin}${this.state.JUMPURLPATH}?${this.generateUrlQuery(params)}`\n    // console.log('detail jumpUrl:', jumpUrl)\n    window.open(jumpUrl, target='_blank')\n  }\n\n  // 各种按钮权限控制\n  hideChangeBtn(ev) {\n    if (!ev) return false\n    const annotationStatus = ev.row.annotationStatus\n    const hideChange = !this.checkIsLeader() || this.checkAnnotationFinishExpire(annotationStatus)\n    return hideChange\n  }\n\n  checkboxable(row) {\n    return !this.checkAnnotationFinishExpire(row.annotationStatus)\n  }\n\n  indexOfSelection(row, selection) {\n    return selection.map(s => s.id).indexOf(row.id)\n  }\n\n  afterReload(list, response) {\n    console.log('afterReload', list, response)\n    if (response.code !== 200) {\n      let errorMsg = '请求失败'\n      if (response && (response.msg || response.message || response.errorMsg)) {\n        errorMsg = response.msg || response.message || response.errorMsg\n      }\n      this.$mtd.message.error(errorMsg)\n    }\n\n    this.setState({\n      tableModel: list\n    })\n    window.Vue.nextTick(() => {\n      this.formatTableData()\n    })\n    console.log('tableModel', this.state.tableModel)\n  }\n\n  // 洗数据\n  formatTableData() {\n    this.state.tableModel.dataSource.map(row => {\n      if (row.operatorOutsourcing) {\n        row.operatorName = `【外】${row.operatorName}`\n      }\n      if (!row.finishTime) row.finishTime = '/'\n    })\n    console.log('dataSource after wash:', this.state.tableModel.dataSource)\n  }\n\n\n  // 工具函数\n  transformDateStr2Int(dateStr) {\n    if (!dateStr) return 0\n    // yyyy-MM-dd字符串转成yyyyMMdd数字\n    return parseInt(dateStr.replaceAll('-', '')) || 0\n  }\n\n  transformInt2DateStr(dateInt) {\n    const dateStr = dateInt.toString()\n    return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`\n  }\n  // 组长或外包组长\n  checkIsLeader() {\n    return this.state.userInfo && \n      this.state.userInfo.role === this.utils.getPageConstant('ROLEENUM').leader\n  }\n\n  // 外包组长或外包组员\n  checkIsWb() {\n    return this.state.userInfo && this.state.userInfo.outsourcing\n  }\n\n  // 标注单已标注或已过期（无法判断应该也视为已标注）\n  checkAnnotationFinishExpire(status) {\n    return status != this.getAnnotationStatus('todo')\n  }\n  \n  getAnnotationStatus(type) {\n    return this.utils.getPageConstant('ANNOTATIONSTATUSENUM')[type]\n  }\n\n  getInspectionStatus(type) {\n    return this.utils.getPageConstant('INSPECTIONSTATUSENUM')[type]\n  }\n\n  disabledDate(date) {\n    // 本次下发日期和完成日期是当天往前推最多选到90天\n    const today = new Date()\n    const minDateTime = new Date(today - 1000 * 3600 * 24 * 90)\n    return date < minDateTime || date > today\n  }\n\n  fetch(apiName, data, entire = false, needThrowError = true) {\n    return this.dataSourceMap[apiName].load(data)\n      .then(res => {\n        // 返回请求的全部内容\n        if (entire) {\n          return res\n        }\n        // 没有特殊需求直接返回数据部分\n        if (res && res.code === 200) {\n          return res.data\n        } else {\n          let errorMsg = '请求失败'\n          if (res && (res.msg || res.message || res.errorMsg)) {\n            errorMsg = res.msg || res.message || res.errorMsg\n          }\n          this.$mtd.message.error(errorMsg)\n          if (needThrowError) {\n            throw errorMsg\n          }\n        }\n      })\n      .catch(e => {\n        console.log(e)\n        throw e\n      })\n  }\n\n  generateUrlQuery(params) {\n    let queryArr = []\n    for (const key in params) {\n      if (params.hasOwnProperty(key)) {\n        queryArr.push(`${key}=${params[key]}`)\n      }\n    }\n    return queryArr.join('&')\n  }\n\n  requestFailed(response) {\n    console.log('request failed', response)\n    this.$mtd.message.error('请求失败')\n  }\n}", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "pageDataSource": {"list": [{"id": "HOST", "desp": "接口域名，可配置test环境和production环境，配置示例：https://***.sankuai.com", "type": "constant", "sort": 0, "fixed": "top", "isMultiple": true, "fixedValues": ["test", "production"]}, {"id": "HEADERS", "desp": "接口请求头，配置后会自动带入到接口配置中，支持自行修改，access-token默认从this.utils.getSSOToken()获取。", "type": "constant", "sort": 1, "fixed": "top", "isMultiple": true, "fixedValues": ["swimlane"], "valueObject": {"swimlane": "", "access-token": {"type": "JSExpression", "value": "this.utils.getSSOToken()"}, "Content-Type": "application/json"}}, {"type": "constant", "isMultiple": false, "id": "ANNOTATIONSTATUSOPTION", "valueSingle": {"type": "JSFunction", "value": "[\n  {\n    \"label\": \"待标注\",\n    \"value\": 1\n  },\n  {\n    \"label\": \"已标注\",\n    \"value\": 2\n  },\n  {\n    \"label\": \"无法判断\",\n    \"value\": 4\n  },\n  {\n    \"label\": \"标注过期\",\n    \"value\": 8\n  }\n]"}}, {"type": "constant", "isMultiple": false, "id": "INSPECTIONSTATUSOPTION", "valueSingle": {"type": "JSFunction", "value": "[\n  {\n    \"label\": \"无需质检\",\n    \"value\": 2\n  },\n  {\n    \"label\": \"未抽到\",\n    \"value\": 4\n  },\n  {\n    \"label\": \"待质检\",\n    \"value\": 8\n  },\n  {\n    \"label\": \"质检通过\",\n    \"value\": 16\n  },\n  {\n    \"label\": \"质检不通过\",\n    \"value\": 32\n  },\n  {\n    \"label\": \"质检超时\",\n    \"value\": 64\n  }\n]"}}, {"type": "constant", "isMultiple": false, "id": "ROLEENUM", "valueSingle": {"type": "JSFunction", "value": "{\n  \"leader\": 1,\n  \"member\": 2\n}"}}, {"type": "constant", "isMultiple": false, "id": "ANNOTATIONSTATUSENUM", "valueSingle": {"type": "JSFunction", "value": "{\n  \"todo\": 1,\n  \"finish\": 2,\n  \"question\": 4,\n  \"expire\": 8\n}"}}, {"type": "constant", "isMultiple": false, "id": "INSPECTIONSTATUSENUM", "valueSingle": {"type": "JSFunction", "value": "{\n  \"noneed\": 2,\n  \"nopick\": 4,\n  \"todo\": 8,\n  \"pass\": 16,\n  \"reject\": 32,\n  \"expire\": 64\n}"}}, {"type": "constant", "isMultiple": false, "id": "ROLEOPTION", "valueSingle": {"type": "JSFunction", "value": "[\n  {\n    \"label\": \"组长\",\n    \"value\": 0\n  },\n  {\n    \"label\": \"组员\",\n    \"value\": 1\n  },\n  {\n    \"label\": \"组长（外包）\",\n    \"value\": 2\n  },\n  {\n    \"label\": \"组员（外包）\",\n    \"value\": 3\n  }\n]"}}, {"type": "constant", "isMultiple": false, "id": "BIZLINE", "valueSingle": {"type": "JSFunction", "value": "{\n  \"food\": 1006,\n  \"general\": 1007\n}"}, "desp": "业务线，1006-餐；1007-综"}]}, "constants": {"HOST": "", "HEADERS": {"swimlane": "", "access-token": {"type": "JSExpression", "value": "this.utils.getSSOToken()"}, "Content-Type": "application/json"}, "ANNOTATIONSTATUSOPTION": [{"label": "待标注", "value": 1}, {"label": "已标注", "value": 2}, {"label": "无法判断", "value": 4}, {"label": "标注过期", "value": 8}], "INSPECTIONSTATUSOPTION": [{"label": "无需质检", "value": 2}, {"label": "未抽到", "value": 4}, {"label": "待质检", "value": 8}, {"label": "质检通过", "value": 16}, {"label": "质检不通过", "value": 32}, {"label": "质检超时", "value": 64}], "ROLEENUM": {"leader": 1, "member": 2}, "ANNOTATIONSTATUSENUM": {"todo": 1, "finish": 2, "question": 4, "expire": 8}, "INSPECTIONSTATUSENUM": {"noneed": 2, "nopick": 4, "todo": 8, "pass": 16, "reject": 32, "expire": 64}, "ROLEOPTION": [{"label": "组长", "value": 0}, {"label": "组员", "value": 1}, {"label": "组长（外包）", "value": 2}, {"label": "组员（外包）", "value": 3}], "BIZLINE": {"food": 1006, "general": 1007}}, "utils": {}, "children": [{"componentName": "DmvFormilyForm", "id": "node_ocllqccdf4bf", "props": {"showColon": false, "labelPosition": "right", "appContext": {"type": "JSExpression", "value": "this"}, "rules": {"type": "JSExpression", "value": "this.state.rules"}, "labelWidth": 100, "style": {"marginLeft": "20px", "marginRight": "20px", "backgroundColor": "rgba(155,155,155,0.27)", "paddingTop": "10px", "paddingBottom": "10px", "marginTop": "20px", "paddingLeft": "10px", "marginBottom": "20px"}, "ref": "searchFilterForm"}, "docId": "docllqccdf4", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilySpace", "id": "node_ocllqccdf4bg", "props": {"schemaProps": {"title": "", "x-component-props": {"isHidenWhenCollapsed": false, "wrap": false}, "description": "", "required": false, "x-decorator-props": {"labelWidthAuto": true}, "name": "space1"}, "ref": "dmvformilyspace-abc7097d"}, "docId": "docllqccdf4", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyDatePicker", "id": "node_ocllp3j16faj", "props": {"schemaProps": {"title": "本次下发日期", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"size": "不设置", "isHidenWhenCollapsed": false, "type": "date", "clearable": false, "placement": "bottom-start", "showBtnNow": true, "splitPanels": true, "showWeekNumbers": false, "appendToContainer": true, "weekStart": 1, "multiple": false, "disabledDate": {"type": "JSFunction", "value": "function(){ return this.disabledDate.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "valueFormat": {"type": "JSExpression", "value": "'yyyy-MM-dd'", "mock": ""}}, "name": "<PERSON><PERSON><PERSON>"}, "ref": "dmvformilydatepicker-4a0bcc16"}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilyDatePicker", "id": "node_ocllx6e7q21", "props": {"schemaProps": {"title": "本次完成日期", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"size": "不设置", "isHidenWhenCollapsed": false, "type": "date", "clearable": true, "placement": "bottom-start", "showBtnNow": true, "splitPanels": true, "showWeekNumbers": false, "appendToContainer": true, "weekStart": 1, "multiple": false, "disabledDate": {"type": "JSFunction", "value": "function(){ return this.disabledDate.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "valueFormat": {"type": "JSExpression", "value": "'yyyy-MM-dd'", "mock": ""}}, "name": "finishDate"}, "ref": "dmvformilydatepicker-4a0bcc16"}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilySelect", "id": "node_ocllp3j16fak", "props": {"schemaProps": {"title": "标注状态", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"isHidenWhenCollapsed": false, "size": "不设置", "icon": "down", "clearable": true, "closable": true, "filterable": false, "autoClearQuery": false, "debounce": 0, "multiple": false, "appendToContainer": false, "placeholder": "请选择", "loadingText": "搜索中", "showCheckbox": false, "reserveKeyword": false, "allowCreate": false, "show-select-all": false}, "enum": {"type": "JSExpression", "value": "this.utils.getPageConstant('ANNOTATIONSTATUSOPTION')"}, "name": "annotationStatus", "_unsafe_MixedSetter_enum_select": "ExpressionSetter"}, "style": {"width": "200px"}}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilySelect", "id": "node_ocllp3j16fal", "props": {"schemaProps": {"title": "质检状态", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "class": "", "labelStyle": {"type": "JSExpression", "value": "{width:'100px'}"}, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"isHidenWhenCollapsed": false, "size": "不设置", "icon": "down", "clearable": true, "closable": true, "filterable": false, "autoClearQuery": false, "debounce": 0, "multiple": false, "appendToContainer": false, "placeholder": "请选择", "loadingText": "搜索中", "showCheckbox": false, "reserveKeyword": false, "allowCreate": false, "show-select-all": false}, "enum": {"type": "JSExpression", "value": "this.utils.getPageConstant('INSPECTIONSTATUSOPTION')"}, "name": "inspectionStatus", "_unsafe_MixedSetter_enum_select": "ExpressionSetter"}, "style": {"width": "200px"}, "ref": "dmvformilyselect-c519ed33"}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}, {"componentName": "DmvFormilySpace", "id": "node_ocllx6e7q22", "props": {"schemaProps": {"title": "", "x-component-props": {"isHidenWhenCollapsed": false, "wrap": false}, "description": "", "required": false, "x-decorator-props": {"labelWidthAuto": true}, "name": "space2"}}, "docId": "docllx6e7q2", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyCascader", "id": "node_ocllx6e7q24", "props": {"schemaProps": {"title": "门店归属城市", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"size": "不设置", "isHidenWhenCollapsed": false, "noDataText": "暂无数据", "fieldNames": {"label": "label", "value": "value", "children": "children", "loading": "loading", "isLeaf": "<PERSON><PERSON><PERSON><PERSON>", "disabled": "disabled"}, "changeOnSelect": false, "expandTrigger": "click", "separator": "/", "filterable": false, "debounce": 300, "remote": false, "noMatchText": "暂无搜索结果", "loading": false, "loadingText": "正在加载中", "clearable": true, "placeholder": "请选择", "placement": "bottom-start", "appendToContainer": true, "reserveKeyword": true, "multiple": false, "checkStrictly": false, "checkedStrategy": "all", "loadData": {"type": "JSExpression", "value": "function() { return this.loadCity.apply(this, Array.prototype.slice.call(arguments).concat([])) }"}}, "default": [], "enum": [{"value": "<PERSON><PERSON>an", "label": "指南", "children": [{"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "设计原则", "disabled": true, "children": [{"value": "yizhi", "label": "一致"}, {"value": "fankui", "label": "反馈"}, {"value": "xiaolv", "label": "效率"}, {"value": "kekong", "label": "可控"}]}, {"value": "da<PERSON><PERSON>", "label": "导航", "children": [{"value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "侧向导航"}, {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "顶部导航"}]}]}, {"value": "<PERSON><PERSON><PERSON>", "label": "组件", "children": [{"value": "basic", "label": "Basic", "disabled": true, "children": [{"value": "layout", "label": "Layout 布局"}, {"value": "color", "label": "Color 色彩"}, {"value": "typography", "label": "Typography 字体"}, {"value": "icon", "label": "Icon 图标"}, {"value": "button", "label": "Button 按钮"}]}, {"value": "form", "label": "Form", "isLeaf": true}, {"value": "data", "label": "Data", "children": [{"value": "table", "label": "Table 表格"}, {"value": "tag", "label": "Tag 标签"}, {"value": "progress", "label": "Progress 进度条"}, {"value": "tree", "label": "Tree 树形控件"}, {"value": "pagination", "label": "Pagination 分页"}, {"value": "badge", "label": "Badge 标记"}]}, {"value": "notice", "label": "Notice", "children": [{"value": "alert", "label": "<PERSON><PERSON> 警告"}, {"value": "loading", "label": "Loading 加载"}, {"value": "message", "label": "Message 消息提示"}, {"value": "message-box", "label": "MessageBox 弹框"}, {"value": "notification", "label": "Notification 通知"}]}, {"value": "navigation", "label": "Navigation", "children": [{"value": "menu", "label": "NavMenu 导航菜单"}, {"value": "tabs", "label": "Tabs 标签页"}, {"value": "breadcrumb", "label": "Breadcrumb 面包屑"}, {"value": "dropdown", "label": "Dropdown 下拉菜单"}, {"value": "steps", "label": "Steps 步骤条"}]}, {"value": "others", "label": "Others", "children": [{"value": "dialog", "label": "Dialog 对话框"}, {"value": "tooltip", "label": "Tooltip 文字提示"}, {"value": "popover", "label": "Popover 弹出框"}, {"value": "card", "label": "Card 卡片"}, {"value": "carousel", "label": "Carousel 走马灯"}, {"value": "collapse", "label": "Collapse 折叠面板"}]}]}, {"value": "ziyuan", "label": "资源", "children": [{"value": "axure", "label": "Axure Components"}, {"value": "sketch", "label": "Sketch Templates"}, {"value": "<PERSON><PERSON><PERSON>", "label": "组件交互文档"}]}], "name": "dpCityId"}, "style": {"width": "200px"}}, "docId": "docllx6e7q2", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvFormilyInput", "id": "node_ocllx6oz6d1", "props": {"schemaProps": {"title": "门店", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"genre": "不设置", "size": "不设置", "clearable": false, "readonly": false, "showCount": false, "isHidenWhenCollapsed": false, "type": "text"}, "name": "searchKeyword", "default": ""}, "style": {"width": "200px"}}, "docId": "docllx6p2ud", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}, {"componentName": "DmvFormilySpace", "id": "node_ocllqccdf4bj", "props": {"schemaProps": {"title": "", "x-component-props": {"isHidenWhenCollapsed": false, "wrap": false}, "description": "", "required": false, "x-decorator-props": {"labelWidthAuto": true}, "name": "space3"}}, "docId": "docllqccdf4", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyInput", "id": "node_oclmfzh9k21", "props": {"schemaProps": {"title": "标注员", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": false, "labelSizeAuto": true, "useHtmlMessage": false}, "x-component-props": {"genre": "不设置", "size": "不设置", "clearable": false, "readonly": false, "showCount": false, "isHidenWhenCollapsed": false, "type": "text"}, "name": "misOrName", "default": ""}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "DmvButton", "id": "node_ocllp3j16fan", "props": {"type": "primary", "children": "主按钮", "text": "搜索", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": false, "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "clickFormSearchBtn"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.clickFormSearchBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "docllp3j16f", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}, {"componentName": "DmvContainer", "id": "node_ocllxf24721", "props": {"direction": "horizontal", "ref": "dmvcontainer-6764f78b"}, "docId": "docllxf2cxf", "hidden": false, "title": "", "isLocked": false, "condition": false, "conditionGroup": "", "children": [{"componentName": "DmvRadioButton", "id": "node_ocllxf24722", "props": {"style": {"width": 200}, "options": {"type": "JSExpression", "value": "this.utils.getPageConstant('ROLEOPTION')"}, "v-model:modelValue": {"type": "JSExpression", "value": "this.state.role"}, "checked": false, "size": "", "type": "fill", "_unsafe_MixedSetter_options_select": "ExpressionSetter", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onInput", "relatedEventName": "changeRole"}], "eventList": [{"name": "onInput", "template": "", "disabled": true}]}, "onInput": {"type": "JSFunction", "value": "function(){this.changeRole.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "ref": "dmvradiobutton-02b70d17"}, "docId": "docllxf2cxf", "hidden": false, "title": "", "isLocked": false, "condition": false, "conditionGroup": ""}]}, {"componentName": "DmvContainer", "id": "node_oclldr7c0p6", "props": {"direction": "horizontal", "ref": "dmvcontainer-69232632", "style": {"marginTop": "10px", "marginBottom": "10px", "marginLeft": "8px"}, "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "docId": "doclldr7n01", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvButton", "id": "node_ocllkt70gqdk", "props": {"type": "text", "children": "主按钮", "text": "批量改签", "htmlType": "button", "size": "middle", "ghost": false, "dashed": false, "disabled": {"type": "JSExpression", "value": "this.state.tableSelection.length == 0", "mock": false}, "ref": "dmvbutton-b2c9dcf3", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClick", "relatedEventName": "clickBatchChangeBtn"}], "eventList": [{"name": "onClick", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClick": {"type": "JSFunction", "value": "function(){this.clickBatchChangeBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "docId": "docllkt70gq", "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.checkIsLeader()", "mock": true}, "conditionGroup": ""}]}, {"componentName": "DmvContainer", "id": "node_ocllkt70gq56", "props": {"direction": "horizontal", "ref": "dmvcontainer-621c88fd", "style": {"marginLeft": "20px", "marginRight": "20px"}}, "docId": "docllkt70gq", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "VpdmProTable", "id": "node_oclldr7c0p5", "props": {"listParams": {"listType": "listProps", "listPropsType": "props", "listProps": {"request": {"method": "post", "url": {"type": "JSExpression", "value": "`${this.state.HOST}/gateway/bml/guiguzi/annotation/ManualAnnotationTService/queryList`", "mock": "https://yapi.sankuai.com/mock/35239/label/workbench"}, "responseMap": {"dataSource": "data.records", "total": "data.total"}, "params": {"type": "JSExpression", "value": "this.state.searchFilter"}, "requestMap": {"currentPage": "page"}}, "autoSearch": false, "searchWatchProp": {"type": "JSExpression", "value": "this.state.controlQuery"}, "afterReload": {"type": "JSFunction", "value": "function(){ return this.afterReload.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "requestFailed": {"type": "JSFunction", "value": "function(){ return this.requestFailed.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}}, "columnArray": [{"label": "批次", "prop": "batchNo", "content": [{"contextProp": "batchNo", "type": "text"}], "width": "100", "header-align": "center", "align": "center", "sortable": false, "filterable": false, "resizable": false, "slot": false}, {"label": "门店ID", "prop": "mtPoiId", "content": [{"contextProp": "creator", "type": "label"}], "resizable": false, "slot": true, "header-align": "center", "align": "center", "sortable": false, "filterable": false}, {"label": "门店名称", "prop": "mtPoiName", "content": [{"contextProp": "mtPoiName", "type": "link", "linkParams": {"url": {"type": "JSExpression", "value": "this.state.SHOPURLPREFIX", "mock": ""}, "target": "_blank", "params": ["shopId"]}}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": true, "overflowType": "不设置"}, {"label": "行业门店ID", "prop": "comPoiId", "content": [{"contextProp": "comPoiId", "type": "text"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "行业门店名称", "prop": "comPoiName", "content": [{"contextProp": "comPoiName", "type": "text"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "标注状态", "prop": "annotationStatus", "content": [{"contextProp": "annotationStatus", "type": "obj", "formatter": {"1": "待标注", "2": "已标注", "4": "无法判断", "8": "标注过期"}, "tipEnabled": false}], "width": "100", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}, {"label": "质检状态", "prop": "inspectionStatus", "content": [{"contextProp": "inspectionStatus", "type": "obj", "formatter": {"1": "/", "2": "/", "4": "未抽中", "8": "待质检", "16": "质检通过", "32": "质检不通过", "64": "质检超时"}, "tipEnabled": false}], "width": "100", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "门店归属城市", "prop": "dpCityName", "content": [{"contextProp": "dpCityName", "type": "text"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "本次标注人", "prop": "nameMis", "content": [{"contextProp": "operatorName", "type": "text", "tipEnabled": false, "suffix": "/"}, {"type": "text", "contextProp": "operatorMis"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}, {"label": "本次下发日期", "prop": "addTime", "content": [{"contextProp": "addTime", "type": "text", "tipEnabled": false}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "本次完成日期", "prop": "finishTime", "content": [{"contextProp": "finishTime", "type": "text"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}], "pagination": {"enabled": true, "showQuickJumper": true, "showSizeChanger": true, "showTotal": true, "currentPage": 1, "pageSize": 5, "size": "不设置", "pager-count": 7}, "actionColumn": {"enabled": true, "visibleButtonCount": 1, "styleType": "text-primary", "width": "126", "labelClass": "opColHeader", "buttonArray": [{"label": "详情", "eventType": "func", "url": "https://dev.sankuai.com/code/home", "target": "_blank", "type": "text", "buttonClass": "opBtn", "func": {"type": "JSFunction", "value": "function(){ return this.clickDetailBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "size": "large"}, {"label": "改签", "eventType": "func", "type": "text", "func": {"type": "JSFunction", "value": "function(){ return this.clickChangeBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "size": "large", "buttonClass": "opBtn", "hidden": {"type": "JSFunction", "value": "function(){ return this.hideChangeBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}], "_unsafe_MixedSetter_buttonArray_select": "ArraySetter", "className": ""}, "table": {"size": "不设置", "showHeader": true, "horizontalVirtual": false, "emptyText": "暂无数据", "loadingMessage": "正在加载中", "rowClass": "", "cellClass": "", "headerRowClass": "", "headerCellClass": "", "orderEnabled": false, "checkboxEnabled": {"type": "JSExpression", "value": "this.checkIsLeader()", "mock": true}, "expandEnabled": false, "striped": false, "bordered": false, "reserveSelection": true, "showSummary": false, "sumText": "合计", "checkboxable": {"type": "JSFunction", "value": "function(){ return this.checkboxable.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "indexOfSelection": {"type": "JSFunction", "value": "function(){ return this.indexOfSelection.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "style": {"width": "100%"}, "ref": "labelRecordTable", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onSwitchChange", "relatedEventName": "changeWorkStatus"}], "eventList": [{"name": "onSelect", "template": "function(selection,row){}", "disabled": false}, {"name": "onSelectAll", "template": "function(selection){}", "disabled": true}, {"name": "onSwitchChange", "template": "function(val,row){}", "disabled": true}, {"name": "onInputChange", "template": "function(val,row){}", "disabled": false}]}, "onChange": {"type": "JSFunction", "value": "function(){this.onTableChange.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "onSwitchChange": {"type": "JSFunction", "value": "function(){this.changeWorkStatus.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "selection": {"type": "JSExpression", "value": "this.state.tableSelection"}, "_unsafe_MixedSetter____condition____select": "BoolSetter", "cell-default-1": {"type": "JSSlot", "params": ["scope"], "value": [{"componentName": "DmvLink", "id": "node_oclmg7jj7m3", "props": {"text": {"type": "JSExpression", "value": "this.scope.row.mtPoiId", "mock": "跳转链接"}, "target": "_blank", "href": {"type": "JSExpression", "value": "`${this.state.SHOPURLPREFIX}?shopId=${this.scope.row.dpPoiId}`", "mock": ""}, "ref": "dmvlink-8867fe31", "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "!this.checkIsWb() && (!!this.scope.row.mtPoiId)", "mock": true}, "conditionGroup": ""}, {"componentName": "DmvText", "id": "node_oclmg7jj7m5", "props": {"text": {"type": "JSExpression", "value": "(!this.scope.row.mtPoiId) ? '/' : `${this.scope.row.mtPoiId[0] + '*'.repeat(Math.max(this.scope.row.mtPoiId.length - 2, 0)) + this.scope.row.mtPoiId[this.scope.row.mtPoiId.length-1]}`", "mock": "/"}, "lineThrough": false, "underline": false, "fontWeight": false, "ref": "dmvtext-d4125f99", "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.checkIsWb() || !this.scope.row.mtPoiId", "mock": true}, "conditionGroup": ""}], "title": "插槽容器"}, "cell-default-2": {"type": "JSSlot", "params": ["scope"], "value": [{"componentName": "DmvLink", "id": "node_oclmg7k3hs4", "props": {"text": {"type": "JSExpression", "value": "this.scope.row.mtPoiName", "mock": "跳转链接"}, "target": "_blank", "href": {"type": "JSExpression", "value": "`${this.state.SHOPURLPREFIX}?shopId=${this.scope.row.dpPoiId}`", "mock": ""}, "ref": "dmvlink-67b7b979", "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "!this.checkIsWb() && (!!this.scope.row.mtPoiId)", "mock": true}, "conditionGroup": ""}, {"componentName": "DmvText", "id": "node_oclmg7k3hs5", "props": {"text": {"type": "JSExpression", "value": "(!this.scope.row.mtPoiId || !this.scope.row.mtPoiName) ? '/' : this.scope.row.mtPoiName", "mock": "/"}, "lineThrough": false, "underline": false, "fontWeight": false, "ref": "dmvtext-1a204bb4", "_unsafe_MixedSetter____condition____select": "VariableSetter"}, "hidden": false, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.checkIsWb() || !this.scope.row.mtPoiId", "mock": true}, "conditionGroup": ""}], "title": "插槽容器"}}, "docId": "doclldr7n01", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}, {"componentName": "DmvModal", "id": "node_ocllxn7ht71", "props": {"title": "请输入mis号", "context": {"type": "JSExpression", "value": "this"}, "value": {"type": "JSExpression", "value": "this.state.changeModal.isShow", "mock": true}, "placement": "center", "mask": true, "mask-closable": false, "closable": true, "destroy-on-close": false, "width": "", "append-to-container": true, "footer": {"align": "center", "size": "large"}, "buttons": [{"text": "确定", "type": "primary", "onClick": {"type": "JSFunction", "value": "function(){ return this.clickChangeModalOk.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "disabled": false, "hidden": false}], "ref": "dmvmodal-b5dcf595", "_unsafe_MixedSetter____condition____select": "VariableSetter", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onClose", "relatedEventName": "clickChangeModalClose"}], "eventList": [{"name": "onOk", "template": "function(e){console.log(e)}", "disabled": false}, {"name": "onCancel", "template": "function(e){console.log(e)}", "disabled": false}, {"name": "onClose", "template": "function(e){console.log(e)}", "disabled": true}]}, "onClose": {"type": "JSFunction", "value": "function(){this.clickChangeModalClose.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "docId": "docllxn7ht7", "hidden": true, "title": "", "isLocked": false, "condition": {"type": "JSExpression", "value": "this.state.changeModal.isShow", "mock": true}, "conditionGroup": "", "children": [{"componentName": "DmvFormilyForm", "id": "node_ocllxn7ht73", "props": {"showColon": false, "labelPosition": "right", "appContext": {"type": "JSExpression", "value": "this"}, "rules": {"type": "JSExpression", "value": "this.state.rules"}, "labelWidth": 80, "ref": "changeForm"}, "docId": "docllxn7ht7", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DmvFormilyInput", "id": "node_ocllxn7ht72", "props": {"schemaProps": {"title": "", "required": false, "description": "", "x-decorator-props": {"labelPosition": "", "labelWidthAuto": true}, "x-component-props": {"genre": "不设置", "size": "不设置", "clearable": false, "readonly": false, "showCount": false, "isHidenWhenCollapsed": false, "type": "text"}, "name": "mis", "default": ""}, "style": {"display": "flex", "justifyContent": "center", "alignItems": "center"}, "ref": "dmvformilyinput-a28814fd"}, "docId": "docllxn7ht7", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}, {"componentName": "DmvContainer", "id": "node_ocllxg87k51", "props": {"direction": "horizontal", "ref": "dmvcontainer-621c88fd", "style": {"marginLeft": "20px", "marginRight": "20px"}}, "docId": "docllkt70gq", "hidden": false, "title": "", "isLocked": false, "condition": false, "conditionGroup": "", "children": [{"componentName": "VpdmProTable", "id": "node_ocllxg87k52", "props": {"listParams": {"listType": "listProps", "listPropsType": "props", "listProps": {"request": {"method": "post", "url": "https://yapi.sankuai.com/mock/35239/label/workbench", "responseMap": {"dataSource": "data", "total": "pageResult.total"}, "params": {"type": "JSExpression", "value": "this.state.searchFilter"}}, "autoSearch": false, "searchWatchProp": {"type": "JSExpression", "value": "this.state.controlQuery"}, "afterReload": {"type": "JSFunction", "value": "function(){ return this.afterReload.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}}, "columnArray": [{"label": "批次", "prop": "batchId", "content": [{"contextProp": "batchId", "type": "text"}], "width": "100", "header-align": "center", "align": "center", "sortable": false, "filterable": false, "resizable": false, "slot": false}, {"label": "门店ID", "width": "", "overflowType": "tooltip", "content": [{"type": "text", "contextProp": "shopId", "url": {"type": "JSExpression", "value": "this.state.mtShopUrlPrefix", "mock": ""}, "target": "_blank", "tipEnabled": false, "buttonArray": [], "params": ["shopId"]}], "header-align": "center", "align": "center", "resizable": false, "slot": false, "prop": "shopId", "sortable": false, "filterable": false}, {"label": "门店名称", "prop": "shopName", "content": [{"contextProp": "shopName", "type": "text", "url": {"type": "JSExpression", "value": "this.state.mtShopUrlPrefix"}, "target": "_blank", "tipEnabled": false, "params": ["shopId"]}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false, "overflowType": "不设置"}, {"label": "行业门店ID", "prop": "dyShopId", "content": [{"contextProp": "dyShopId", "type": "text"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}, {"label": "行业门店名称", "prop": "dyShopName", "content": [{"contextProp": "dyShopName", "type": "text"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "标注状态", "prop": "labelStatus", "content": [{"contextProp": "labelStatus", "type": "obj", "formatter": {"0": "全部", "1": "待标注", "2": "已标注", "3": "标注过期", "4": "无法判断"}, "tipEnabled": false}], "width": "100", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "质检状态", "prop": "checkStatus", "content": [{"contextProp": "checkStatus", "type": "obj", "formatter": {"0": "全部", "1": "/", "2": "未抽到", "3": "待质检", "4": "质检通过", "5": "质检不通过", "6": "质检超时"}, "tipEnabled": false}], "width": "100", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "业务", "width": "100", "overflowType": "不设置", "content": [{"type": "obj", "contextProp": "bu", "tipEnabled": false, "formatter": {"0": "全部", "1": "休娱", "2": "综发", "3": "丽人", "4": "LE", "5": "商运", "6": "医疗", "7": "医美"}}], "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "门店归属城市", "prop": "city", "content": [{"contextProp": "city", "type": "text"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "本次标注人", "prop": "name", "content": [{"contextProp": "name", "type": "text"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false, "sortable": false, "filterable": false}, {"label": "本次下发日期", "prop": "deliverDate", "content": [{"contextProp": "deliverDate", "type": "date", "tipEnabled": false}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}, {"label": "本次完成日期", "prop": "finishDate", "content": [{"contextProp": "finishDate", "type": "date"}], "width": "", "header-align": "center", "align": "center", "resizable": false, "slot": false}], "pagination": {"enabled": true, "showQuickJumper": true, "showSizeChanger": true, "showTotal": true, "currentPage": 1, "pageSize": 5, "size": "不设置", "pager-count": 7}, "actionColumn": {"enabled": true, "visibleButtonCount": 1, "styleType": "text-primary", "width": "120", "labelClass": "opColHeader", "buttonArray": [{"label": "详情", "eventType": "func", "url": "https://dev.sankuai.com/code/home", "target": "_blank", "type": "text", "buttonClass": "opBtn", "func": {"type": "JSFunction", "value": "function(){ return this.clickDetailBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "size": "large"}, {"label": "改签", "type": "text", "eventType": "func", "hidden": {"type": "JSFunction", "value": "function(){ return this.hideChangeBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "link": {"target": "_blank"}, "buttonClass": "opBtn", "func": {"type": "JSFunction", "value": "function(){ return this.clickChangeBtn.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}], "_unsafe_MixedSetter_buttonArray_select": "ArraySetter", "className": ""}, "table": {"size": "不设置", "showHeader": true, "horizontalVirtual": false, "emptyText": "暂无数据", "loadingMessage": "正在加载中", "rowClass": "", "cellClass": "", "headerRowClass": "", "headerCellClass": "", "orderEnabled": false, "checkboxEnabled": {"type": "JSExpression", "value": "this.checkIsLeader()", "mock": true}, "expandEnabled": false, "striped": false, "bordered": false, "reserveSelection": true, "showSummary": false, "sumText": "合计", "checkboxable": {"type": "JSFunction", "value": "function(){ return this.checkboxable.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}}, "style": {"width": "100%"}, "ref": "labelRecordTable-Wb", "__events": {"eventDataList": [{"type": "componentEvent", "name": "onSwitchChange", "relatedEventName": "changeWorkStatus"}], "eventList": [{"name": "onSelect", "template": "function(selection,row){}", "disabled": false}, {"name": "onSelectAll", "template": "function(selection){}", "disabled": true}, {"name": "onSwitchChange", "template": "function(val,row){}", "disabled": true}, {"name": "onInputChange", "template": "function(val,row){}", "disabled": false}]}, "onChange": {"type": "JSFunction", "value": "function(){this.onTableChange.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "onSwitchChange": {"type": "JSFunction", "value": "function(){this.changeWorkStatus.apply(this,Array.prototype.slice.call(arguments).concat([])) }"}, "selection": {"type": "JSExpression", "value": "this.state.tableSelection"}, "_unsafe_MixedSetter____condition____select": "BoolSetter"}, "docId": "doclldr7n01", "hidden": false, "title": "", "isLocked": false, "conditionGroup": "", "condition": true}]}]}