/**
 * @typedef {Object} PageInfo
 * @property {string} title - 页面名称(仅用作本地开发表格展示)
 * @property {string} description - 页面备注信息(仅用作本地开发表格展示)
 * @property {string} localLink - 本地调试链接地址(建议配置为相对路径，保证域名/端口变更时也能正确生效)
 * @property {string} [testLink] - 测试环境链接地址(若以 '/' 开头，则视为相对路径，会尝试使用上一级的 testPrefix 进行拼接)
 * @property {string} [prodLink] - 线上环境链接地址(若以 '/' 开头，则视为相对路径，会尝试使用上一级的 prodPrefix 进行拼接)
 */

/**
 * @typedef {Object} BussinessPageGroup
 * @property {string} title - 业务名称(仅用作本地开发表格展示)
 * @property {string} [testPrefix] - 测试环境链接统一前缀
 * @property {string} [prodPrefix] - 线上环境链接统一前缀
 * @property {PageInfo[]} pages - 相关页面信息
 */

const APOLLO_HOST = {
  TEST: 'https://apollo.nibcrm.test.sankuai.com',
  PROD: 'https://apollo.meituan.com',
}

const XIAOZHI_HOST = {
  TEST: 'https://xiaozhi.ai.test.sankuai.com',
  PROD: 'https://xiaozhi.sankuai.com',
}

/**
 * @type {BussinessPageGroup}
 */
const bussinessPageGroup = [
  {
    title: '标注走查工作台',
    testPrefix: APOLLO_HOST.TEST + '/crm-saletool-config',
    prodPrefix: APOLLO_HOST.PROD + '/crm-saletool-config',
    pages: [
      {
        title: '人员管理-标注员配置',
        localLink: '/people-manage/index.html#/annotation',
      },
      {
        title: '人员管理-质检员配置',
        localLink: '/people-manage/index.html#/inspection',
      },
      {
        title: '标注工作台',
        localLink: '/annotation-workbench/index.html#/',
      },
      {
        title: '质检工作台',
        localLink: '/inspection-workbench/index.html#/',
      },
      {
        title: '质检记录页',
        localLink: '/inspection-record/index.html#/',
      },
      {
        title: '标注详情页',
        localLink:
          '/mark-detail/index.html#/mark-operation?markId=000&type=mark&accessControl=editable',
      },
      {
        title: '质检详情页',
        localLink:
          '/mark-detail/index.html#/mark-operation?markId=000&type=inspection&accessControl=editable',
      },
      {
        title: '手动下发记录页',
        localLink: '/manual-upload-poi/index.html#/record-list',
      },
      {
        title: '标注操作日志页',
        localLink: '/operate-log/index.html#/log-list?annInsId=000&type=1',
      },
      {
        title: '质检操作日志页',
        localLink: '/operate-log/index.html#/log-list?annInsId=000&type=2',
      },
    ],
  },
  {
    title: '自动追价',
    testPrefix: APOLLO_HOST.TEST + '/crm-saletool-config',
    prodPrefix: APOLLO_HOST.PROD + '/crm-saletool-config',
    pages: [
      {
        title: '列表页',
        localLink: '/price-match-automation#/list',
      },
      {
        title: '表单创建页',
        localLink: '/price-match-automation#/create',
      },
      {
        title: '表单编辑&查看页',
        localLink: '/price-match-automation#/detail',
      },
    ],
  },
  {
    title: '智能演练配置平台',
    testPrefix: APOLLO_HOST.TEST + '/crm-saletool-config/smart-conversation-config',
    prodPrefix: APOLLO_HOST.PROD + '/crm-saletool-config/smart-conversation-config',
    pages: [
      {
        title: '演练配置列表',
        localLink: '/smart-conversation-config/index.html#/config-list',
        testLink: '/index.html#/config-list',
        prodLink: '/index.html#/config-list',
      },
      {
        title: '演练任务列表',
        localLink: '/smart-conversation-config/index.html#/task-list',
        testLink: '/index.html#/task-list',
        prodLink: '/index.html#/task-list',
      },
    ],
  },
  {
    title: '真人演练配置平台',
    testPrefix: APOLLO_HOST.TEST + '/crm-saletool-config/real-drill-manage',
    prodPrefix: APOLLO_HOST.PROD + '/crm-saletool-config/real-drill-manage',
    pages: [
      {
        title: '演练完成情况列表',
        localLink: '/real-drill-manage/index.html#/completion-list',
        testLink: '/index.html#/completion-list',
        prodLink: '/index.html#/completion-list',
      },
      {
        title: '演练完成情况详情',
        localLink: '/real-drill-manage/index.html#/completion-detail',
        testLink: '/index.html#/completion-detail',
        prodLink: '/index.html#/completion-detail',
      },
    ],
  },
  {
    title: 'BML销售作业路径',
    testPrefix: APOLLO_HOST.TEST + '/crm-saletool-config/bml-sale-pc',
    prodPrefix: APOLLO_HOST.PROD + '/crm-saletool-config/bml-sale-pc',
    pages: [
      {
        title: '报错记录页',
        localLink: '/bml-sale-pc/index.html#/error-record-list',
        testLink: '/index.html#/error-record-list',
        prodLink: '/index.html#/error-record-list',
      },
    ],
  },
  {
    title: 'AIBD 王晓慧',
    testPrefix: XIAOZHI_HOST.TEST + '/crm-saletool-config/aibd',
    prodPrefix: XIAOZHI_HOST.PROD + '/crm-saletool-config/aibd',
    pages: [
      {
        title: '质检详情',
        localLink: '/aibd/index.html#/call-quality-inspection?taskId=727',
        testLink: '/index.html#/call-quality-inspection?taskId=727',
        prodLink: '/index.html#/call-quality-inspection?taskId=512',
      },
    ],
  },
  // 新页面
  {
    title: 'IVR 人工质检',
    testPrefix: XIAOZHI_HOST.TEST + '/crm-saletool-config/smart-quality-inspector',
    prodPrefix: XIAOZHI_HOST.PROD + '/crm-saletool-config/smart-quality-inspector',
    pages: [
      {
        title: '智能销售',
        localLink: '/smart-quality-inspector/index.html#/ivr?dialogId=198513',
        // testLink: '/index.html#/call-quality-inspection?taskId=512',
        // prodLink: '/index.html#/call-quality-inspection?taskId=512',
      },
    ],
  },
]

/**
 *
 * @param {BussinessPageGroup[]} source
 * @returns {BussinessPageGroup[]}
 */
function bussinessPageGroupTransformer(source) {
  return source.map(group => {
    const { testPrefix = '', prodPrefix = '', pages } = group

    return {
      ...group,
      pages: pages.map(page => {
        const { testLink = '', prodLink = '' } = page

        return {
          ...page,
          testLink: testLink.startsWith('/') ? testPrefix + testLink : testLink,
          prodLink: prodLink.startsWith('/') ? prodPrefix + prodLink : prodLink,
        }
      }),
    }
  })
}

export default bussinessPageGroupTransformer(bussinessPageGroup)
