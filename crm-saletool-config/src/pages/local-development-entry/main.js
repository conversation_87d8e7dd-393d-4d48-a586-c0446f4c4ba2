import Vue from 'vue'
import App from './app.vue'
import MTD from '@ss/mtd-vue2'
import VueCompositionAPI from '@vue/composition-api'
import Router from './router/index'
import Store from './store/index'

import '@ss/mtd-vue2/lib/theme-yellow/index.css'
import '@nibfe/apollo-pc-ui-component-mtd2/src/styles/index.css'

Vue.use(VueCompositionAPI)
Vue.use(MTD)

new Vue({
  router: Router,
  store: Store,
  render: h => h(App),
}).$mount('#app')

document.title = '祝你开发愉快！'
