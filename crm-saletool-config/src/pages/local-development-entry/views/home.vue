<template>
  <div class="container">
    <mtd-card title="本地开发指南" class="margin-bottom-wrapper">
      这儿打算直接把 README 渲染出来，还没找到比较合适的方案
    </mtd-card>
    <mtd-card title="页面路由信息汇总" class="info-group">
      <div v-for="(group, index) in pageGroupList" :key="index" class="margin-bottom-wrapper">
        <div class="page-group-title">{{ group.title }}</div>
        <mtd-table :data="group.pages">
          <mtd-table-column prop="title" label="页面名称"></mtd-table-column>
          <mtd-table-column prop="localLink" label="本地调试链接">
            <template #default="{ row }">
              <mtd-button type="text" :href="row.localLink" target="_blank"> 冲啊！！ </mtd-button>
              <mtd-icon-button icon="copy-o" @click="copyLinkHandler(row.localLink)" />
            </template>
          </mtd-table-column>
          <mtd-table-column prop="testLink" label="测试环境链接">
            <template #default="{ row }">
              <mtd-button type="text" :href="row.testLink" target="_blank"> 冲啊！！ </mtd-button>
              <mtd-icon-button icon="copy-o" @click="copyLinkHandler(row.testLink)" />
            </template>
          </mtd-table-column>
          <mtd-table-column prop="prodLink" label="线上环境链接">
            <template #default="{ row }">
              <mtd-button type="text" :href="row.prodLink" target="_blank"> 冲啊！！ </mtd-button>
              <mtd-icon-button icon="copy-o" @click="copyLinkHandler(row.prodLink)" />
            </template>
          </mtd-table-column>
        </mtd-table>
      </div>
    </mtd-card>
  </div>
</template>

<script>
import { Message } from '@ss/mtd-vue2'

import pageGroupList from '../lib/page-group-list'

export default {
  data() {
    return {
      pageGroupList,
    }
  },
  methods: {
    copyLinkHandler(linkText) {
      // TODO DDL 2099-12-31
      // 这里可以补充一个复制逻辑
      Message({
        message: linkText,
        type: 'info',
      })
    },
  },
}
</script>

<style lang="less" scoped>
.container {
  padding: 16px;

  .margin-bottom-wrapper:not(:last-child) {
    margin-bottom: 16px;
  }

  .page-group-title {
    font-weight: 800;
    margin-bottom: 8px;
    font-size: 1.05em;
  }
}
</style>
