import { SOURCE_FROM, OUTPUT_TYPE } from './constants'

function getSourceValue(params) {
  if (params.source === SOURCE_FROM.EXTERNAL_INPUT) {
    return '${' + params.label + '}'
  } else if (params.source === SOURCE_FROM.INTERNAL_INPUT) {
    return params.path
  } else {
    return params.value
  }
}
function queryParams(params) {
  const changParams = {
    label: params.label,
    name: params.name,
    description: params.description,
    source: params.source,
  }
  changParams.sourceValue = getSourceValue(params)
  return changParams
}

export default function changeFormToParams(form, isCheck) {
  const formData = form.getValuesIn()
  let params = {
    name: formData.name,
    description: formData.description,
    tenantId: 3,
    onlineWritten: formData.onlineWritten,
  }
  if (isCheck) {
    params.trailParam = formData.trailParam
  }
  const taskList = formData.tasks?.map(task => {
    let item = {
      alias: task.alias,
      url: task.url,
      timeout: task.timeout,
      taskType: task.taskType,
    }
    if (task['swimlane'] || task['swimlane'] !== '') {
      item['swimlane'] = task['swimlane']
    }
    if (task.taskType === 'PigeonGeneric' || task.taskType === 'ThriftGeneric') {
      item.method = task.methodName
      item.rpcParamConfig = task.rpcParamConfig?.map(params => {
        const changParams = {
          label: params.label,
          name: params.name,
          required: params.required,
          type: params.type,
          description: params.description,
          source: params.source,
        }
        changParams.sourceValue = getSourceValue(params)
        return changParams
      })
    } else {
      item.method = task.methodType
      item.queries = task.queries?.map(query => {
        return queryParams(query, 'query')
      })
      item.paths = task.paths?.map(path => {
        return queryParams(path, 'path')
      })
      item.headers = task.headers?.map(header => {
        return queryParams(header, 'header')
      })
      item.body = task.body?.map(body => {
        return queryParams(body, 'body')
      })
    }
    return item
  })
  params.tasks = taskList
  params.outputConfigType = formData.outputConfigType
  if (formData.outputConfigType === OUTPUT_TYPE.OUTPUT_STRING) {
    params.outputConfig = formData.output_string
  } else {
    const temp = {}
    formData.output_list?.forEach(data => {
      temp[data.output_name] = data.output_path || ''
    })
    params = { ...params, outputConfig: JSON.stringify(temp) }
  }
  return params
}
