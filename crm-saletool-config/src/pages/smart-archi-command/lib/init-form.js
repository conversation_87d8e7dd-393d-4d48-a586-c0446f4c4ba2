import { reportError } from '@/lib/utils'
import { OUTPUT_TYPE } from './constants'
export function initForm(form) {
  const initValue = {
    name: '',
    description: '',
    onlineWritten: false,
    tasks: [
      {
        alias: '',
        taskType: 'PigeonGeneric',
        url: '',
        methodName: '',
        swimlane: '',
        timeout: 1000,
        rpcParamConfig: [
          {
            label: '',
            name: '',
            type: undefined,
            required: false,
            description: '',
            source: 1,
          },
        ],
      },
    ],
    outputConfigType: 2,
    output_string: '',
    output_list: [
      {
        output_name: '',
        output_path: '',
      },
    ],
    trailParam: '',
  }
  form.setInitialValues(initValue)
}

function setOutputData(outputData, type) {
  const data = {}
  if (type == OUTPUT_TYPE.OUTPUT_STRING) {
    data.outputConfigType = 2
    data.output_string = outputData || ''
  } else {
    data.outputConfigType = 1
    try {
      const outputMap = JSON.parse(outputData)
      data.output_list = Object.keys(outputMap).map(key => {
        return {
          output_name: key,
          output_path: outputMap[key],
        }
      })
    } catch (e) {
      reportError({ content: e })
      data.output_list = []
    }
  }
  return data
}

function setQueryData(queryData, _type) {
  return queryData?.map(item => {
    const temp = {
      ...item,
    }
    if (temp.source == 2) {
      temp.path = temp.sourceValue
    }
    if (temp.source == 3) {
      temp.value = temp.sourceValue
    }
    delete temp.sourceValue
    return temp
  })
}

function setTasksData(tasksData) {
  const data = {}
  data.tasks = tasksData.map(item => {
    const task = {
      ...item,
    }
    delete task.method
    if (item.taskType == 'ThriftGeneric' || task.taskType == 'PigeonGeneric') {
      task.methodName = item.method
      task.rpcParamConfig = setQueryData(item.rpcParamConfig, 'rpcParamConfig') || []
    } else {
      task.methodType = item.method
      task.querys = setQueryData(item.querys, 'query') || []
      task.paths = setQueryData(item.paths, 'path') || []
      task.headers = setQueryData(item.headers, 'header') || []
      task.body = setQueryData(item.body, 'body') || []
    }
    return task
  })
  return data
}

export function setResponseData(form, responseData) {
  let data = {
    name: responseData.name,
    description: responseData.description,
    onlineWritten: responseData.onlineWritten,
    trailParam: responseData.trailParam || '',
  }
  if (responseData.outputConfig) {
    data = { ...data, ...setOutputData(responseData.outputConfig, responseData.outputConfigType) }
  } else {
    data.outputConfigType = OUTPUT_TYPE.OUTPUT_STRING
    data.output_string = ''
    data.output_list = [
      {
        output_name: '',
        output_path: '',
      },
    ]
  }
  if (responseData.tasks) {
    data = { ...data, ...setTasksData(responseData.tasks) }
  } else {
    data.tasks = []
  }
  form.setInitialValues(data)
}
