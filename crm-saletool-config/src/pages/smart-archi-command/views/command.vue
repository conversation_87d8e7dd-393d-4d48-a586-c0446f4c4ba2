<template>
  <div id="annotation-workbench" class="annotation-workbench">
    <div id="ele"></div>
  </div>
</template>
<script>
import graphCfg from '@nibfe/ccc-lowcode-render'
import { getUrlSearch, reportError } from '@/lib/utils'

import { getCommandDetail, postCommandCreate, postCommandTrial } from '../api/index'
import changeFormToParams from '../lib/form-to-params'
import { initForm, setResponseData } from '../lib/init-form'

export default {
  components: {},
  data() {
    const params = getUrlSearch()
    return {
      commandId: params.commandId || null,
    }
  },
  created() {
    graphCfg({
      el: '#ele',
      tenantId: 3,
      pageType: 'chart', // 图表类型
      remoteURL: process.env.VUE_APP_API_HOST_AIX,
      module: 'command-management',
      appEnv: process.env.VUE_APP_ENV,
      templateId: process.env.VUE_APP_ENV === 'production' ? 260 : 2410,
      modelId: process.env.VUE_APP_ENV === 'production' ? 378 : 1772,
      initParams: {
        terminal: 0, //来自于PC，必填
        planSource: 0, //非来自拜访创建的，建议填0
      },
      lib: {
        reportError,
        getCommandDetail,
        postCommandCreate,
        postCommandTrial,
        changeFormToParams,
        initForm,
        setResponseData,
      },
      passthrough: {
        COMMAND_ID: this.commandId,
      },
    }).catch(() => {})
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
::v-deep.annotation-workbench {
  min-width: 1100px;
  display: flex;
  flex: 1;
  justify-content: center;
}
</style>
