import axios from 'axios'

const API = axios.create({
  withCredentials: true,
  timeout: 5000,
  baseURL: process.env.VUE_APP_ENV === 'development' ? process.env.VUE_APP_API_HOST : '',
})

export async function getCommandDetail(commandId) {
  const response = await API.get('/api/aix/operation/command/detail', {
    params: {
      commandId: commandId,
    },
  })
  const data = response.data
  if (data.code !== 200) throw new Error(data.msg)
  return data
}
export async function postCommandCreate(body) {
  const response = await API.post('/api/aix/operation/command/create', body)
  const data = response.data
  if (data.code !== 200) throw new Error(data.msg)
  return data
}

export async function postCommandTrial(body) {
  const response = await API.post('/api/aix/operation/command/trial', body)
  const data = response.data
  if (data.code !== 200) throw new Error(data.msg)
  return data
}
