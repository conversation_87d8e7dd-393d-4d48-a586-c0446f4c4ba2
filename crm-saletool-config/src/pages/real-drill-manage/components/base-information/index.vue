<template>
  <div class="base-container">
    <div class="head-content">
      <div class="rectangle"></div>
      <span class="head-title">基础信息</span>
    </div>
    <div class="main-content">
      <div class="part-sell">
        <div class="information-content">
          <div class="information">演练销售:</div>
          <div class="information-detail">
            {{ baseInformation.learner.name }}{{ baseInformation.learner.mis }}
          </div>
          <ContentCopy :content="contentLearner"></ContentCopy>
        </div>
        <div class="information-content">
          <div class="information">所在小组:</div>
          <div class="information-detail">{{ baseInformation.learner.organizationName }}</div>
        </div>
      </div>
      <div class="part-mission">
        <div class="information-content">
          <div class="information">任务名称:</div>
          <mtd-tooltip :content="baseInformation.planName" size="small" placement="top">
            <div class="information-detail task-text">{{ baseInformation.planName }}</div>
          </mtd-tooltip>
          <div class="tag">
            <mtd-tag :theme="missionTheme">{{
              MissionStatus[baseInformation.missionStatus]
            }}</mtd-tag>
          </div>
        </div>
        <div class="information-content">
          <div class="information">陪练师傅:</div>
          <div class="information-detail">
            {{ baseInformation.mentor.name }}{{ baseInformation.mentor.mis }}
          </div>
          <ContentCopy :content="contentMaster"></ContentCopy>
        </div>
      </div>
      <div class="part-master">
        <div class="information-content">
          <div class="information">师傅评分:</div>
          <div v-if="baseInformation.mentorScore !== 0" class="star-title">
            <mtd-rate v-model="baseInformation.mentorScore" disabled class="drill-rate-value">
              <span slot-scope="scope" class="rate-score">{{ scope.value.toFixed(1) }}</span>
            </mtd-rate>
          </div>
          <div v-else class="information-detail">暂无评分</div>
        </div>
        <div class="information-content-drill-mode">
          <div class="information">演练模式:</div>
          <div class="information-detail">{{ baseInformation.topicModeDesc }}</div>
        </div>
      </div>
      <div class="part4">
        <div class="information-content">
          <div class="information">演练时间:</div>
          <div class="information-detail">{{ baseInformation.completeTime }}</div>
        </div>
        <div class="information-content">
          <div class="information">演练时长:</div>
          <div class="information-detail">{{ baseInformation.duration }}</div>
        </div>
      </div>
    </div>
    <hr />
  </div>
</template>
<script>
const MissionStatusTextMap = { 1: '未开始', 2: '进行中', 3: '已完成', 4: '已撤回', 5: '已超时' }
import { ContentCopy } from '@/components'
import API from '@/api/real-drill-manage'
import { errorHandler } from '@/lib/utils'
const themes = {
  3: 'green',
  2: 'blue',
  1: 'gray',
  4: 'red',
  5: 'brown',
}
export default {
  components: { ContentCopy },
  props: ['eventBus'],
  data() {
    return {
      baseInformation: {
        completeTime: '',
        duration: null,
        learner: {
          mis: '',
          name: '',
          organizationName: '',
        },
        mentor: {
          mis: '',
          name: '',
        },
        mentorScore: 0,
        missionStatus: null,
        planName: '',
        topicMode: '',
      },
      missionId: this.$route.query.missionId,
      tenantId: this.$route.query.tenantId,
      MissionStatus: MissionStatusTextMap,
    }
  },
  computed: {
    missionTheme() {
      const status = this.baseInformation.missionStatus
      return themes[status]
    },
  },
  watch: {
    eventBus(val) {
      this.sendPostRequest()
    },
  },
  created() {},
  mounted() {
    this.sendPostRequest()
  },
  methods: {
    async sendPostRequest() {
      try {
        const res = await API.queryBaseInformation({
          missionId: this.missionId,
          tenantId: this.tenantId,
        })
        Object.assign(this.baseInformation, res.mission)
        this.contentLearner = res.mission.learner.name + res.mission.learner.mis
        this.contentMaster = res.mission.mentor.name + res.mission.mentor.mis
        let durationMin = Math.floor(res.mission.duration / 60).toString()
        let durationS = (res.mission.duration % 60).toString()
        this.baseInformation.duration = durationMin + 'min ' + durationS + 's'
      } catch (error) {
        errorHandler(error)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.base-container {
  padding: 6px 6px 0;

  .head-content {
    display: flex;
    margin-top: 10px;
    .rectangle {
      align-self: center;
      background-color: #ffc701;
      height: 16px;
      width: 4px;
      margin-left: -6px;
      border-top-right-radius: 2px; /* 设置右上角圆角 */
      border-bottom-right-radius: 2px; /* 设置右下角圆角 */
    }
    .head-title {
      align-self: center;
      margin-left: 10px;
      font-size: 16px;
      font-weight: 600;
      color: #000;
    }
  }
  .main-content {
    display: flex;
    margin-top: 3px;
    margin-left: 7px;
    margin-right: 7px;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  hr {
    border: none; /* 移除默认边框 */
    border-top: 1px solid #efefef; /* 设置分割线为2px宽的红色实线 */
    margin-left: 6px;
    margin-right: 6px;
    margin-top: 29px;
    margin-bottom: 0;
  }
  .information-content-drill-mode {
    display: flex;
    margin-top: 11.5px;
    .information {
      font-size: 14px;
      font-weight: 400;
      color: #11192599;
    }
    .information-detail {
      font-size: 14px;
      font-weight: 400;
      color: #111925;
      margin-left: 12px;
    }
  }
  .information-content {
    margin-top: 16px;
    display: flex;
    .star-title {
      margin-left: 12px;
      margin-top: -6px;
    }
    .rate-score {
      font-size: 16px;
      color: #ffb400;
      font-weight: 500;
      font-family: sans-serif;
      margin-left: -9px;
    }
    .drill-rate-value ::v-deep .mtd-rate-icon {
      font-size: 16px;
    }
    .drill-rate-value ::v-deep .mtd-rate-item-wrapper {
      width: 16px;
    }
    .drill-rate-value ::v-deep .mtdicon-star-rate::before {
      font-size: 16px;
    }
    .information {
      font-size: 14px;
      font-weight: 400;
      color: #11192599;
    }
    .information-detail {
      font-size: 14px;
      font-weight: 400;
      color: #111925;
      margin-left: 12px;
    }
    .task-text {
      white-space: nowrap;
      overflow: hidden;
      max-width: 140px;
      text-overflow: ellipsis;
    }

    .icon-copy {
      cursor: pointer;
      margin-left: 6.92px;
    }
  }
}
</style>
