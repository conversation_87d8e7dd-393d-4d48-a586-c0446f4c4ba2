<template>
  <div class="play-content">
    <div class="head-content">
      <div class="rectangle"></div>
      <span class="head-title">演练内容</span>
    </div>
    <div class="chats">
      <div ref="scrollContent" class="chats-content">
        <div class="messages-list">
          <div
            v-for="(message, index) in messages"
            :key="index"
            :class="message.role === 1 ? 'messageRight' : 'messageLeft'"
          >
            <img
              :src="getAvatar(message)"
              :alt="message.role"
              :class="message.role === 1 ? 'avatarRight' : 'avatarLeft'"
            />
            <div
              :ref="'dialogContent' + message.contentId"
              :class="message.role === 1 ? 'textRight' : 'textLeft'"
            >
              {{ message.content }}
            </div>
          </div>
        </div>
      </div>
      <AudioPlayer :audio-address="audioAddress" @timeChange="scrollIntoView"></AudioPlayer>
    </div>
  </div>
</template>
<script>
import avatar from '@/assets/<EMAIL>'
import API from '@/api/real-drill-manage'
import { AudioPlayer } from '@/components'
import { errorHandler } from '@/lib/utils'
import { Message } from '@ss/mtd-vue2'
export default {
  components: {
    AudioPlayer,
  },
  data() {
    return {
      missionId: this.$route.query.missionId,
      tenantId: this.$route.query.tenantId,
      userId: this.$route.query.userId,
      messages: [],
      currentTime: 0,
      audioAddress: null,
      page: 1,
      pageSize: 10000,
      defaultAvatar: avatar,
    }
  },
  mounted() {
    this.sendPostRequest()
  },
  created() {},
  methods: {
    async sendPostRequest() {
      const params = {
        missionId: this.missionId,
        tenantId: this.tenantId,
        // userId: this.userId,
        page: this.page,
        pageSize: this.pageSize,
      }
      try {
        const drillInfoResponse = await API.queryDrillInformation({
          request: params,
        })
        this.messages = drillInfoResponse.dialogList.map((item, index) => {
          return { ...item, contentId: index }
        })

        const audioResponse = await API.queryAudio({
          missionId: this.missionId,
          userId: this.userId,
          tenantId: this.tenantId,
        })
        if (audioResponse.code == 200) {
          this.audioAddress = audioResponse.data || null
        } else {
          Message.error(audioResponse.message)
        }
      } catch (error) {
        errorHandler(error)
      }
    },
    getAvatar(message) {
      // 此处可以根据实际情况来设置默认头像或者根据角色来指定头像
      return message.user.avatar || this.defaultAvatar
    },
    scrollIntoView(value) {
      this.currentTime = value
      const messageElement = this.messages.find(
        message =>
          message.startTime / 1000 <= this.currentTime &&
          message.endTime / 1000 >= this.currentTime,
      )
      if (messageElement) {
        // 获取该消息在DOM中的元素
        const element = this.$refs[`dialogContent${messageElement.contentId}`][0]
        if (element) {
          // 滚动到该元素
          let parentNode = this.$refs.scrollContent
          parentNode.scrollTop = element.offsetTop - parentNode.offsetTop
        }
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.textRight {
  font-size: 15px;
  background-color: #f9d247;
  position: relative;
  padding: 8px;
  border-radius: 5px;
  margin: 15px 15px 0 70px;
}
.textRight::before {
  content: '';
  position: absolute;
  top: 14px; /* 根据需要调整三角形的垂直位置 */
  right: -6px; /* 小三角形的宽度加上希望它突出的距离 */
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 6px solid #f9d247; /* 与气泡div的背景颜色相同 */
}
.messageRight {
  display: flex;
  flex-direction: row-reverse; /* 反转子元素的排列顺序 */
}
.avatarRight {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  object-fit: cover;
  margin-top: 15px;
  margin-right: 20px;
}
.textLeft {
  font-size: 15px;
  background-color: #ffffff;
  position: relative;
  padding: 8px;
  border-radius: 5px;
  margin: 15px 70px 0 15px;
}
.textLeft::before {
  content: '';
  position: absolute;
  top: 14px; /* 根据需要调整三角形的垂直位置 */
  left: -6px; /* 小三角形的宽度加上希望它突出的距离 */
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid #ffffff; /* 与气泡div的背景颜色相同 */
}
.messageLeft {
  display: flex;
}
.avatarLeft {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  object-fit: cover;
  margin-top: 15px;
  margin-left: 20px;
}
.play-content {
  padding: 6px;
  display: flex;
  flex-direction: column;
  min-height: 300px;
  .chats {
    flex: 1;
    border-radius: 10px;
    padding: 10px;
    width: 100%;
    min-height: 300px;
  }
  .chats-content {
    border-top-left-radius: 10px; /* 设置左上角的圆角 */
    border-top-right-radius: 10px; /* 设置右上角的圆角 */
    width: 100%;
    height: 90%;
    background-color: #f7f7f7;
    padding: 0;
    overflow: auto;
  }
  .head-content {
    display: flex;
    margin-top: 10px;
    .rectangle {
      align-self: center;
      background-color: #ffc701;
      height: 16px;
      width: 4px;
      margin-left: -6px;
      border-top-right-radius: 2px; /* 设置右上角圆角 */
      border-bottom-right-radius: 2px; /* 设置右下角圆角 */
    }
    .head-title {
      align-self: center;
      margin-left: 10px;
      font-size: 16px;
      font-weight: 600;
      color: #000;
    }
  }
}
</style>
