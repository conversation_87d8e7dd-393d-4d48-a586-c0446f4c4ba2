<template>
  <div class="play-content">
    <div class="head-content">
      <div class="rectangle"></div>
      <span class="head-title">主管评分</span>
      <div class="managerInformation">
        <img :src="getAvatar(managerInformation.manager)" class="titles" />
        <span class="managerName">{{ managerInformation.manager.name }}</span>
      </div>
    </div>
    <div class="managerContent">
      <div v-if="showsEmpty" class="empty">
        <div class="empty-rate">暂无评分</div>
        <div v-if="managerInformation.canEdit" class="edits-button">
          <mtd-button type="text" icon="edit-o" @click="contentChange">编辑</mtd-button>
        </div>
      </div>
      <div v-if="showsContent" class="realContrnt">
        <div v-if="showsManager">
          <div class="star-title">
            <div class="title-total">总体</div>
            <mtd-rate
              v-model="managerInformation.evaluation.totalScore"
              disabled
              class="drill-rate-value"
            >
              <span slot-scope="scope" class="rate-score">{{ scope.value.toFixed(1) }}</span>
            </mtd-rate>
          </div>
          <div class="comments-content">{{ managerInformation.evaluation.comment }}</div>
          <div v-if="managerInformation.canEdit" class="edits-button">
            <mtd-button type="text" icon="edit-o" @click="contentChange">编辑</mtd-button>
          </div>
        </div>
        <div v-if="showsEdit">
          <div class="star-title">
            <div>
              <span class="rate-label">总体：</span>
              <mtd-rate v-model="scoreParam.totalScore" class="drill-rate-value" @change="(value) => {scoreParam.totalScore = value}">
                <span slot-scope="scope" class="rate-score">{{ scope.value.toFixed(1) }}</span>
              </mtd-rate>
            </div>
          </div>
          <div class="comments-edit">
            <mtd-textarea
              class="comment-textarea"
              v-model="value"
              rows="3"
              :max-length="50"
              :show-count="true"
            ></mtd-textarea>
          </div>
          <div v-if="managerInformation.canEdit" class="edits-button">
            <mtd-button type="text" @click="sendmessage">确定</mtd-button>
          </div>
        </div>
      </div>
      <hr class="depart-content" />
    </div>
    <div class="head-content">
      <div class="rectangle"></div>
      <span class="head-title">师傅评分</span>
      <div class="managerInformation">
        <img :src="getAvatar(mentorInformation.mentor)" class="titles" />
        <span class="managerName">{{ mentorInformation.mentor.name }}</span>
      </div>
    </div>
    <div class="managerContent">
      <div v-if="showMentorEmpty" class="empty">
        <div class="empty-rate">暂无评分</div>
        <div v-if="mentorInformation.canEdit" class="edits-button">
          <mtd-button type="text" icon="edit-o" @click="contentMentorChange">编辑</mtd-button>
        </div>
      </div>
      <div v-if="showMentorContent" class="realContrnt">
        <div v-if="showMentor" >
          <div class="star-title">
            <div class="title-total">总体</div>
            <mtd-rate
              v-model="mentorInformation.evaluation.totalScore"
              disabled
              class="drill-rate-value"
            >
              <span slot-scope="scope" class="rate-score">{{ scope.value.toFixed(1) }}</span>
            </mtd-rate>
          </div>
          <div class="rates-detail">
            <div class="professions">
              <div class="details">专业性</div>
              <div class="details-content">{{ mentorInformation.evaluation.profession }}</div>
            </div>
            <div>
              <div class="departs-profession"></div>
            </div>
            <div class="persuasives">
              <div class="details">说服性</div>
              <div class="details-content">{{ mentorInformation.evaluation.persuasive }}</div>
            </div>
            <div>
              <div class="departs-persuasives"></div>
            </div>
            <div class="flexibilitys">
              <div class="details">灵活性</div>
              <div class="details-content">{{ mentorInformation.evaluation.flexibility }}</div>
            </div>
          </div>
          <!-- <div class="voice">语音反馈</div>-->
          <div class="comments-content">{{ mentorInformation.evaluation.comment }}</div>
          <div v-if="mentorInformation.canEdit" class="edits-button">
            <mtd-button type="text" icon="edit-o" @click="contentMentorChange">编辑</mtd-button>
          </div>
        </div>
        <div v-if="showMentorEdit">
          <div  class="star-title1">
            <div>
              <span class="rate-label">总体：</span>
              <mtd-rate v-model="scoreMentorParam.totalScore" class="drill-rate-value" @change="(value) => {scoreMentorParam.totalScore = value}">
                <span slot-scope="scope" class="rate-score">{{ scope.value.toFixed(1) }}</span>
              </mtd-rate>
            </div>
            <div>
              <span class="rate-label">专业性：</span>
              <mtd-rate v-model="scoreMentorParam.profession" class="drill-rate-value" @change="(value) => {scoreMentorParam.profession = value}">
                <span slot-scope="scope" class="rate-score">{{ scope.value.toFixed(1)}}</span>
              </mtd-rate>
            </div>
            <div>
              <span class="rate-label">说服性：</span>
              <mtd-rate v-model="scoreMentorParam.persuasive" class="drill-rate-value" @change="(value) => {scoreMentorParam.persuasive = value}">
                <span slot-scope="scope" class="rate-score">{{ scope.value.toFixed(1) }}</span>
              </mtd-rate>
            </div>
            <div>
              <span class="rate-label">灵活性：</span>
              <mtd-rate v-model="scoreMentorParam.flexibility" class="drill-rate-value" @change="(value) => {scoreMentorParam.flexibility = value}">
                <span slot-scope="scope" class="rate-score">{{ scope.value.toFixed(1) }}</span>
              </mtd-rate>
            </div>
          </div>
          <div class="comments-edit">
            <mtd-textarea
              class="comment-textarea"
              v-model="value"
              rows="3"
              :max-length="50"
              :show-count="true"
            ></mtd-textarea>
          </div>
          <div v-if="mentorInformation.canEdit" class="edits-button">
            <mtd-button type="text" @click="sendMentorMessage">确定</mtd-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import avatar from '@/assets/<EMAIL>'
import API from '@/api/real-drill-manage'
import { errorHandler } from '@/lib/utils'
import { Message } from '@ss/mtd-vue2'
import { LXUtils } from '@/lib/lx'
import { valLabGeneratorMap } from '../../lib/lx'
export default {
  components: {},
  data() {
    return {
      managerInformation: {
        evaluation: {
          comment: '',
          flexibility: 0,
          persuasive: 0,
          profession: 0,
          totalScore: 0,
        },
        manager: {
          name: '',
          avatar: '',
        },
        canEdit: false,
      },
      mentorInformation: {
        evaluation: {
          comment: '',
          flexibility: 0,
          persuasive: 0,
          profession: 0,
          totalScore: 0,
        },
        mentor: {
          avatar: '',
          name: '',
        },
        canEdit: false,
      },
      scoreParam: {
        totalScore: 0,
        profession: 0,
        persuasive: 0,
        flexibility: 0,
      },
      scoreMentorParam: {
        totalScore: 0,
        profession: 0,
        persuasive: 0,
        flexibility: 0,
      },
      missionId: this.$route.query.missionId,
      tenantId: this.$route.query.tenantId,
      userId: this.$route.query.userId,
      showsEmpty: false,
      showsContent: true,
      showsManager: true,
      showsEdit: false,
      showMentorEmpty: false,
      showMentorContent: true,
      showMentor: true,
      showMentorEdit: false,
      value: null,
      defaultAvatar: avatar,
      lx: null
    }
  },
  mounted() {
    this.sendPostRequest()
    this.lx = new LXUtils('gc_m', {
      cid: 'c_gc_m_xx7ezat6',
      appnm: 'dp_apollo_pc',
      valLabGenerator: valLabGeneratorMap.misIdAndTenantId(this.$route.query.tenantId ?? '-1'),
    })
  },
  created() {},
  methods: {
    getAvatar(message) {
      // 此处可以根据实际情况来设置默认头像或者根据角色来指定头像
      return message.avatar || this.defaultAvatar
    },
    contentChange() {
      this.showsEmpty = false
      this.showsContent = true
      this.showsManager = false
      this.showsEdit = true
    },
    contentMentorChange () {
      this.lx.mc("b_gc_m_kg74amm8_mc")
      this.showMentorEmpty = false
      this.showMentorContent = true
      this.showMentor = false
      this.showMentorEdit = true
    },
    async sendmessage() {
      this.lx.mc("b_gc_m_gq0ellal_mc")
      const params = {
        missionId: this.missionId,
        comment: this.value,
        tenantId: this.tenantId,
        ...this.scoreParam
      }
      try {
        const res = await API.editManagerRate({
          request: params
        })
        if (res.code === 200) {
          await this.sendPostRequest() // 确保按顺序执行
        } else {
          Message.error(res.message)
          return
        }
      } catch (error) {
        errorHandler(error)
        return // 发生错误时提前返回，防止执行后续代码
      }
      this.showsEmpty = false
      this.showsContent = true
      this.showsManager = true
      this.showsEdit = false
    },
    async sendMentorMessage() {
      this.lx.mc("b_gc_m_kg74amm8_mc")
      const params = {
        missionId: this.missionId,
        comment: this.value,
        tenantId: this.tenantId,
        ...this.scoreMentorParam
      }
      try {
        const res = await API.editMentorRate({
          request: params
        })
        if (res.code === 200) {
          await this.sendPostRequest() // 确保按顺序执行
        } else {
          Message.error(res.message)
          return
        }
      } catch (error) {
        errorHandler(error)
        return // 发生错误时提前返回，防止执行后续代码
      }
      this.showMentorEmpty = false
      this.showMentorContent = true
      this.showMentor = true
      this.showMentorEdit = false
    },
    async sendPostRequest() {
      try {
        const mentorRateResponse = await API.queryMentorRate({
          missionId: this.missionId,
          tenantId: this.tenantId,
          userId: this.userId
        })
        this.assignWithNullExclusion(this.managerInformation, mentorRateResponse)
        this.managerInformation.evaluation.totalScore = parseInt(this.managerInformation.evaluation.totalScore)

        if (
          this.managerInformation.evaluation.totalScore == 0 &&
          this.managerInformation.evaluation.comment == ''
        ) {
          this.showsEmpty = true
          this.showsContent = false
          this.showsManager = false
        }
        const masterRateResponse = await API.queryMasterRate({
          missionId: this.missionId,
          tenantId: this.tenantId,
          userId: this.userId
        })
        this.assignWithNullExclusion(this.mentorInformation, masterRateResponse)
        this.mentorInformation.evaluation.totalScore = parseInt(this.mentorInformation.evaluation.totalScore)
        this.$emit('basicPost', true)
        if (
          this.mentorInformation.evaluation.totalScore == 0 &&
          this.mentorInformation.evaluation.comment == ''
        ) {
          this.showMentorEmpty = true
          this.showMentorContent = false
          this.showMentor = false
        }
      } catch (error) {
        errorHandler(error)
      }
    },
    assignWithNullExclusion(target, ...sources) {
      sources.forEach(source => {
        Object.keys(source).forEach(key => {
          if (source[key] !== null) {
            target[key] = source[key];
          }
        });
      });
      return target;
    }
  },
}
</script>
<style lang="scss" scoped>
.voice {
  margin-left: 15px;
  margin-top: 16px;
  color: #000;
  font-weight: 600;
}
.details-content {
  font-weight: 600;
  font-family: sans-serif;
  font-size: 15px;
}
.title-total {
  margin-top: 6px;
  margin-right: 2px;
  margin-left: 4px;
  color: #11192599;
}
.details {
  margin-right: 5px;
  color: #11192599;
}
.departs-persuasives {
  width: 1px;
  align-items: end;
  margin-top: 5px;
  height: 12px;
  background-color: #efefef;
}
.departs-profession {
  width: 1px;
  margin-top: 5px;
  height: 12px;
  align-items: end;
  background-color: #efefef;
}
.professions {
  display: flex;
  margin-left: 15px;
  margin-right: 10px;
}
.persuasives {
  display: flex;
  margin-left: 10px;
  margin-right: 10px;
}
.flexibilitys {
  display: flex;
  margin-left: 10px;
}
.rates-detail {
  display: flex;
  margin-top: 5px;
}
.comments-edit {
  margin-left: 15px;
  .comment-textarea {
    width: 100%;
  }
}
.comments-content {
  margin-top: 8px;
  margin-left: 15px;
  color: #000;
}
.rate-score {
  font-size: 16px;
  color: #ffb400;
  font-weight: 500;
  font-family: sans-serif;
  margin-left: -9px;
}
.star-title {
  display: flex;
  margin-left: 12px;
  margin-top: 10px;
}
.star-title1 {
  margin-left: 12px;
  margin-top: 10px;
}
.rate-label {
  line-height: 34px;
}
.drill-rate-value ::v-deep .mtd-rate-icon {
  font-size: 16px;
}
.drill-rate-value ::v-deep .mtd-rate-item-wrapper {
  width: 16px;
}
.drill-rate-value ::v-deep .mtdicon-star-rate::before {
  font-size: 16px;
}
.depart-content {
  border: none; /* 移除默认边框 */
  width: 98%;
  border-top: 1px solid #efefef; /* 设置分割线为2px宽的红色实线 */
  margin-left: 15px;
  margin-right: 15px;
  margin-top: 20px;
}
.empty-rate {
  color: #cccccc;
  margin-left: 15px;
  margin-top: 20px;
}
.managerName {
  margin-top: 16px;
  font-size: 14px;
  color: #999999;
}
.managerInformation {
  margin-left: auto;
  display: flex;
}
.titles {
  width: 24px;
  height: 24px;
  margin-top: 16px;
  margin-right: 4px;
  border-radius: 50%;
  object-fit: cover;
  display: inline-block;
  outline: 1px solid #e8e8e8;
  outline-offset: -0.5px;
}
.play-content {
  padding: 6px 16px 6px 0;
  .head-content {
    display: flex;
    .rectangle {
      margin-top: 16px;
      align-self: center;
      background-color: #ffc701;
      height: 16px;
      width: 4px;
      border-top-right-radius: 2px; /* 设置右上角圆角 */
      border-bottom-right-radius: 2px; /* 设置右下角圆角 */
    }
    .head-title {
      margin-top: 16px;
      align-self: center;
      margin-left: 10px;
      font-size: 16px;
      font-weight: 600;
      color: #000;
    }
  }
}
</style>
