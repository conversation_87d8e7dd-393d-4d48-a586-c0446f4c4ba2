function getBody(xhr) {
  const result = xhr.responseText || xhr.response
  if (!result) {
    return result
  }
  try {
    return JSON.parse(result)
  } catch (e) {
    return result
  }
}

function getError(action, xhr) {
  let msg = `fail to post ${action} ${xhr.status}`
  if (xhr.response) {
    if (xhr.responseType === '' || xhr.responseType === 'text') {
      let response = {}
      try {
        response = JSON.parse(xhr.response)
      } catch (e) {}
      msg = `${response.msg || xhr.response}`
    } else {
      msg = `${xhr.response}`
    }
  } else if (xhr.responseText) {
    msg = `${xhr.responseText}`
  }

  const err = new Error(msg)
  err.status = xhr.status
  err.url = action
  return err
}

function noop(params) {
  // eslint-disable-next-line no-console
  console.log('noop', params)
}

export function createXHRForUpload(options) {
  if (typeof XMLHttpRequest === 'undefined') {
    alert('您使用的浏览器过于老旧，强烈建议您使用最新版的Chrome浏览器。')
    return
  }

  const withCredentials = true
  const xhrOptions = {
    method: options.method || 'post',
    xhrUrl: options.xhrUrl,
    headers: options.headers || {},
    fileXHR: options.fileXHR === undefined ? true : options.fileXHR,
    withCredentials: options.withCredentials || withCredentials,
    data: options.data || {},
    onProgress: options.onProgress || null,
    onSuccess: options.onSuccess || noop,
    onError: options.onError || noop,
    onTimeout: options.onTimeout || noop,
  }
  const data = xhrOptions.data
  const headers = xhrOptions.headers
  let params = JSON.stringify(data)
  const xhr = new XMLHttpRequest()
  //上传进度的监控
  if (xhrOptions.onProgress && xhr.upload) {
    xhr.upload.onprogress = function progress(e) {
      if (e.total > 0) {
        e.percent = (e.loaded / e.total) * 100
      }
      xhrOptions.onProgress(e)
    }
  }
  // 请求成功的回调
  xhr.onload = function onload() {
    if (xhr.status < 200 || xhr.status >= 300) {
      const errorResult = getError(xhrOptions.xhrUrl, xhr)
      return xhrOptions.onError(errorResult)
    }
    xhrOptions.onSuccess(getBody(xhr))
  }
  // 请求失败的回调
  xhr.onerror = function error(e) {
    xhrOptions.onError(e)
  }
  // 请求超时的回调
  xhr.ontimeout = e => {
    xhrOptions.onTimeout(e)
  }
  xhr.open(xhrOptions.method, xhrOptions.xhrUrl, true)

  for (let item in headers) {
    if (headers.hasOwnProperty(item) && headers[item] !== null) {
      xhr.setRequestHeader(item, headers[item])
    }
  }
  xhr.withCredentials = xhrOptions.withCredentials
  if (xhrOptions.fileXHR) {
    const formData = new FormData()
    for (let key in data) {
      if (data.hasOwnProperty(key) && data[key] !== null) {
        formData.append(key, data[key])
      }
    }
    params = formData
  }
  // 发送请求
  xhr.send(params)
  return xhr
}
