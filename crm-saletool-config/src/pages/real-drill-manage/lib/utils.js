import * as XLSX from 'xlsx'
import { reportError } from '@/lib/utils'

/**
 * 从xlsx文件中获取JSON数据
 * @param {File} file - 上传的xlsx文件
 */
export async function getXlsxJsonData(file) {
  try {
    if (!file) return
    const buffer = await file.arrayBuffer()
    const workbook = XLSX.read(buffer)
    const jsonResult = XLSX.utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]])
    return jsonResult
  } catch (error) {
    reportError({ error })
    return []
  }
}
