import { reportError } from '@/lib/utils'
import { queryUserInfoById } from '../api'

/**
 * 基于 JS 特性实现页面维度的共用私有变量 (避免 window 挂载全局变量)
 *
 * 请注意不要在非 LX 埋点场景下使用该变量，避免引起混淆
 */
const globalVariablesCache = {
  misid: null,
}

/**
 * ValLab 包含字段较多，一般使用默认配置即可
 *
 * 如有定制需求请移步官方文档 https://docs.sankuai.com/lx/web/extensions/
 *
 * @typedef ValLab
 */
export const valLabGeneratorMap = {
  misIdAndTenantId(tenant_id) {
    return async () => {
      let misid = globalVariablesCache.misid

      if (!misid) {
        try {
          const userInfo = await queryUserInfoById()
          misid = userInfo.mis
          globalVariablesCache.misid = misid
        } catch (error) {
          reportError(error)
          misid = '-'
        }
      }

      return {
        misid,
        tenant_id,
        custom: {
          misid,
          tenant_id,
        },
      }
    }
  },
}
