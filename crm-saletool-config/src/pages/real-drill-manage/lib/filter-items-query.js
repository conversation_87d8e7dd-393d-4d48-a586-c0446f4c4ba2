import axios from '@/lib/axios'

// 目录 Cascader fieldNames 选项
export const catalogueFieldNames = {
  label: 'label',
  value: 'value',
  children: 'children',
  loading: 'loading',
  isLeaf: 'isLeafNode',
  disabled: 'disabled',
}

// 目录首层级拉取
export const axiosForQueryRootCatalogue = ({
  requestData,
  requestConfig,
  form,
  setFieldPattern,
}) => {
  return axios
    .post(
      '/gateway/sk/drill/topic/queryRootCatalogue',
      {
        drillCatalogueRequest: requestData,
      },
      requestConfig,
    )
    .then(response => {
      const drillCatalogueResponse = response.data
      const dataSource = drillCatalogueResponse?.nodeList || []

      return dataSource
    })
}

// 目录 Cascader load-data 选项
export const catalogueLoadData = ({ requestData, requestConfig }) => {
  return axios
    .post(
      '/gateway/sk/drill/topic/queryCatalogueByParent',
      {
        drillCatalogueRequest: requestData,
      },
      requestConfig,
    )
    .then(response => {
      const drillCatalogueResponse = response.data
      const { nodeList = [] } = drillCatalogueResponse || {}
      return nodeList
    })
}

export const queryTopicByCatalogue = ({ requestData, requestConfig }) => {
  return axios
    .post('/gateway/sk/drill/topic/queryTopicOptionByCatalogue', requestData, requestConfig)
    .then(response => {
      const drillCatalogueResponse = response.data
      const dataSource = drillCatalogueResponse?.drillOptionDTOList || []

      return dataSource
    })
}
