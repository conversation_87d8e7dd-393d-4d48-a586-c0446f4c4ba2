import axios from 'axios'

const gateway = axios.create({
  withCredentials: true,
  baseURL: process.env.VUE_APP_ENV === 'development' ? process.env.VUE_APP_API_HOST : '',
})

export function queryUserInfoById() {
  return gateway
    .get('/gateway/sk/drill/queryUserInfoById')
    .then(response => response.data)
    .then(data => {
      if (data.code !== 200) throw new Error(data.message)
      return data.userList?.[0]
    })
}
