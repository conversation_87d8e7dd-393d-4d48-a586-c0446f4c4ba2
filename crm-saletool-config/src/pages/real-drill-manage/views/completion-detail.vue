<template>
  <div class="completion-detail-container">
    <div class="header">
      <div class="container-title">
        <span class="head-title">真人对练记录详情</span>
        <mtd-button v-if="false" type="primary" icon="mtdicon mtdicon-share-o" class="share-button"
          >分享</mtd-button
        >
      </div>
      <BaseInformation ref="basicInformation" :event-bus="eventBus"></BaseInformation>
    </div>
    <div class="bottom-content">
      <ExerciseContent class="exercise"></ExerciseContent>
      <ScoreRate class="score" @basicPost="basicPost"></ScoreRate>
    </div>
  </div>
</template>
<script>
import { BaseInformation, ExerciseContent, ScoreRate } from '../components/index'
import { getEnv } from '@/lib/utils'
import psb from '@nibfe/platform-sdk'
import { REDIRECT_URL } from '@/lib/constants'
export default {
  components: {
    BaseInformation,
    ExerciseContent,
    ScoreRate,
  },
  data() {
    // ...#/completion-detail?id=1234
    return {
      eventBus: false,
    }
  },
  created() {
    if (getEnv().isApolloPlatform) {
      psb.config('1705025948hm73ss', {
        masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
        redirectUrl: process.env.NODE_ENV === 'development' ? null : REDIRECT_URL,
      })
    }
  },
  methods: {
    basicPost(val) {
      this.eventBus = !this.eventBus
    },
  },
}
</script>
<style lang="scss" scoped>
.exercise::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 1px; /* 线的宽度 */
  background-color: #efefef; /* 线的颜色 */
}
.bottom-content {
  display: flex;
  flex: 1;
  min-height: 600px;
}
.exercise {
  flex: 60%;
  position: relative;
}
.depart {
  flex: 0.05%;
}
.score {
  flex: 39%;
}
.completion-detail-container {
  display: flex;
  min-width: 1000px;
  height: 100vh;
  flex-direction: column;
  min-height: 600px;
  .container-title {
    width: 100%;
    padding: 16px;
    display: flex;
    .head-title {
      align-self: center;
      margin-left: 10px;
      font-size: 18px;
      font-weight: 600;
      color: black;
    }
    .share-button {
      font-weight: 500;
      margin-left: auto;
      font-size: 14px;
    }
  }
}
</style>
