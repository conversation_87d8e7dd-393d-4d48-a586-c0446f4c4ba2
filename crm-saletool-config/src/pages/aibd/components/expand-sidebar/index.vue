<template>
  <div class="quality-inspection-results-container" :style="containerStyle">
    <div v-show="showSiderbar">
      <div
        v-for="(item, index) in items"
        :key="index"
        class="quality-inspection-results-sidebar"
        @click="expandTab(item.value)"
      >
        <i :class="item.icon"></i>
        <span>{{ item.label }}</span>
      </div>
    </div>
    <div v-show="!showSiderbar">
      <div v-if="items.length > 1" class="quality-inspection-results-header">
        <mtd-tabs v-model="activeTab" :show-line="false" @change="expandTab">
          <mtd-tab-pane
            v-for="(item, index) in items"
            :key="index"
            :label="item.label"
            :value="item.value"
          >
            <slot :name="item.value"></slot>
          </mtd-tab-pane>
        </mtd-tabs>
      </div>
      <slot v-else-if="items.length === 1" :name="items[0].value"></slot>
      <div
        class="toggle-button"
        :class="toggleButtonClass"
        :style="toggleButtonStyle"
        @click="changeSidebar"
      >
        <div class="toggle-content">
          <span class="toggle-text">收起</span>
          <i :class="`toggle-icon ${toggleIconStyle}`"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExpandSidebar',
  props: {
    items: {
      type: Array,
      default: () => [],
    },
    expandWidth: {
      type: String,
      default: '27%',
    },
    toggleIconStyle: {
      type: String,
      default: 'mtdicon-right-thick',
    },
  },
  data() {
    return {
      showSiderbar: false, //默认展开
      activeTab: 'qualityInspectionResult',
    }
  },
  computed: {
    containerStyle() {
      return {
        width: this.showSiderbar ? '64px' : this.expandWidth,
        transition: 'width 0.3s ease',
      }
    },
    toggleButtonClass() {
      return this.toggleIconStyle === 'mtdicon-right-thick' ? 'right-style' : 'left-style'
    },
  },
  methods: {
    //展开/收起
    changeSidebar() {
      this.showSiderbar = !this.showSiderbar
    },
    //切换tab
    expandTab(val) {
      this.showSiderbar = false
      this.activeTab = val
    },
  },
}
</script>
<style lang="scss" scoped>
.quality-inspection-results-container {
  position: relative;
  border-right: 1px solid rgba(17, 25, 37, 0.05);

  .quality-inspection-results-sidebar {
    width: 64px;
    height: 64px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #166ff7;

    i {
      font-size: 20px;
      margin-bottom: 5px;
    }

    span {
      font-size: 12px;
      text-align: center;
    }
  }

  width: 27%;

  .quality-inspection-results-header {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      height: 16px;
      width: 4px;
      left: 0px;
      background-color: var(--color-primary);
      border-radius: 0px 2px 2px 0px;
      margin-top: 13px;
    }
  }

  ::v-deep {
    .mtd-tabs-nav-container {
      margin: 13px 0 0 18px;
      color: #222222;
    }
  }

  .toggle-button {
    position: fixed;
    top: 45%;
    transform: translateY(-50%);
    width: 20px;
    height: 90px;
    background: #ffffff;
    border: 1px solid #f0f0f0;
    cursor: pointer;
    color: #666666;
    display: flex;
    align-items: center;

    .toggle-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px 0;
      font-size: 12px;

      .toggle-text {
        writing-mode: vertical-rl;
        text-orientation: upright;
        letter-spacing: 2px;
      }

      .toggle-icon {
        margin-top: 10px;
      }
    }
  }
  .right-style {
    position: absolute;
    left: 0;
    transform: translateX(-100%);
    border-radius: 28px 0 0 28px;
    border-right: none;
  }

  .left-style {
    position: absolute;
    right: 0;
    transform: translateX(100%);
    border-radius: 0 28px 28px 0;
    border-left: none;
  }
}
</style>
