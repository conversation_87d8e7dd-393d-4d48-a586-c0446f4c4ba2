<template>
  <UniversalBox title="沟通记录" class="call-record-container">
    <template #action>
      <div class="action">
        <span>展开全部评论</span>
        <mtd-switch
          v-model="expandAllComments"
          size="small"
          @change="changeExpandAllComments"
        ></mtd-switch>
      </div>
    </template>

    <DialogScrollContainer
      class="call-record-content"
      :data-source="dialogList"
      :left-aligned-role="ROLE.MERCHANT"
      :audio-address="basicInformation.dialogVoiceUrl"
      :sequence-id="sequenceId"
      :show-audio-player="showAudioPlayer"
      v-on="$listeners"
    >
      <!-- 对话内容 -->
      <template #sentence="{ item: data }">
        <div v-if="data.formattedStartTime && data.formattedEndTime" class="timeline">
          {{ formatDuration(data.startTime) }} ~ {{ formatDuration(data.endTime) }}
        </div>
        <div
          :class="['chat-content', data.role === ROLE.MERCHANT ? 'left-align' : 'right-align']"
          :data-sequence-id="data.sequenceId"
        >
          <!-- 头像 -->
          <div class="avatar">
            <div v-if="data.role === ROLE.MERCHANT" class="mer-avatar">商户</div>
            <img v-else :src="getAvatar(data.user)" alt="" />
          </div>
          <!-- 对话段落 -->
          <div class="content reverse-content">
            <template v-if="data.contentType === CALL_INFO_TYPE_ENUMS.PHOTO">
              <img
                :src="data.content"
                alt=""
                class="media-content media-image"
                @click="handlePreview(data.content)"
              />
            </template>
            <template v-else-if="data.contentType === CALL_INFO_TYPE_ENUMS.VIDEO">
              <video :src="data.content" controls class="media-content media-video"></video>
            </template>
            <template v-else>{{ data.content }}</template>

            <div
              v-if="data.markLabelConfig"
              :style="{
                [data.role === ROLE.MERCHANT ? 'right' : 'left']: 0,
                transform: `translateX(calc(${
                  data.role === ROLE.MERCHANT ? '100% + 6px' : '-100% - 6px'
                }))`,
              }"
              class="exhibition"
            >
              <!-- 添加评论 -->
              <span
                v-if="!data.comment"
                class="add-comment operation"
                @click="handleExpandComment(data, 'add')"
              >
                <mtd-tooltip content="评论" size="small" placement="top">
                  <i class="mtdicon-add-message"></i>
                </mtd-tooltip>
              </span>
              <!-- 动作/命中/评论ICON -->
              <div v-if="data.relateAction || data.comment" class="hit operation">
                <!-- 动作 -->
                <mtd-tooltip content="历史动作" size="small" placement="top">
                  <span v-if="data.relateAction" @click="handleAction(data.sequenceId)">
                    <i class="mtdicon-bolt-fill theme-icon"></i>
                  </span>
                </mtd-tooltip>

                <span
                  v-if="data.relateAction && data.comment"
                  class="vertical-line line-back"
                ></span>
                <span v-if="data.comment" @click="handleExpandComment(data, 'edit')">
                  <i class="mtdicon-comment-fill theme-icon"></i>
                  <i
                    class="theme-icon"
                    :class="data.expandComment ? 'mtdicon-up-thick' : 'mtdicon-down-thick'"
                  ></i>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="data.markLabelConfig"
          class="detailed"
          :style="{
            [data.role === ROLE.MERCHANT ? 'margin-left' : 'margin-right']: '44px',
            alignItems: data.role === ROLE.MERCHANT ? 'flex-start' : 'flex-end',
          }"
        >
          <!-- 命中说明、评论内容 -->
          <template v-if="data.expandComment">
            <CommentPicker
              :key="data.key"
              :editable="data.reviseComment"
              :recheck-editable="data.recheckEditable"
              :comment="data.comment"
              :spot-check-content="data.spotCheckContent"
              :is-recheck="isRecheck"
              :action-id="actionId"
              :sequence-id="data.sequenceId"
              :tags="data.markLabelConfig"
              :recheck-tags="data.spotCheckConfig"
              :loading="data.loading"
              :values.sync="data.commentInfo"
              :recheck-info.sync="data.recheckInfo"
              @edit="editComment(data)"
              @recheck="recheckComment(data)"
              @cancel="cancelComment(data)"
              @submit="other => submitComment(data, other)"
            />
          </template>
        </div>
      </template>
      <template #markers="{ audioDuration }">
        <div class="markers">
          <div
            v-for="marker in markData"
            :key="marker.operationName"
            class="marker"
            :style="{ left: `${(marker.dialogVoiceSeconds / audioDuration) * 99 + 0.5}%` }"
          >
            <mtd-tooltip :content="marker.operationName" placement="top">
              <div class="marker-dot"></div>
            </mtd-tooltip>
          </div>
        </div>
      </template>
    </DialogScrollContainer>
  </UniversalBox>
</template>

<script>
import { defineComponent } from '@vue/composition-api'

import avatar from '@/assets/<EMAIL>'
import { errorReportOnly } from '@/lib/utils'
import CommentPicker from '../comment-picker/index.vue'
import API from '../../api'
import { TAG_COLOR, ROLE } from '../../lib/dict'
import { DialogScrollContainer, UniversalBox } from '@/components'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import { ACTION_TYPE_ENUMS, CALL_INFO_TYPE_ENUMS } from '../../lib/dict'
import imagePreview from 'image-preview-vue'
import 'image-preview-vue/lib/imagepreviewvue.css'

dayjs.extend(duration)

const callRecord2DialogItem = data => ({
  ...data,
  loading: false, // 提交评论loading
  expandComment: data.comment, // 展开评论
  reviseComment: !data.comment, // 新增/修改评论
  key: `${data.sequenceId}-${data?.comment?.version}`,
  commentInfo: {
    markResult: data.comment?.markResult || null,
    markComment: data.comment?.markComment || '',
    problemCategories: data.comment?.problemCategories || null,
  },
  recheckInfo: {
    markResult: data.spotCheckContent?.markResult || null,
    markComment: data.spotCheckContent?.markComment || '',
  },
})

export default defineComponent({
  name: 'CallRecord',
  components: {
    UniversalBox,
    DialogScrollContainer,
    CommentPicker,
  },
  props: {
    // 当前页面选中的行为ID
    actionId: {
      type: Number,
      default: 0,
    },
    basicInformation: {
      type: Object,
      default: () => ({}),
    },
    lx: {
      type: Object,
      default: () => ({}),
    },

    sequenceId: {
      type: Number,
      default: 0,
    },
    taskBatchId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      ROLE,
      TAG_COLOR,
      defaultAvatar: avatar, // 默认头像
      currentTime: 0, // 当前播放时间
      expandAllComments: true, // 展开所有评论
      dialogList: [],
      localWatchSequenceId: this.sequenceId,
      markData: [],
      actionType: '',
      showAudioPlayer: true,
      CALL_INFO_TYPE_ENUMS,
      isRecheck: false, // 是否可进行抽检
    }
  },
  watch: {
    actionId: {
      immediate: true,
      handler(val) {
        if (val) this.getData()
      },
    },
    taskBatchId: {
      immediate: true,
      handler(val) {
        if (val) {
          API.hasSpotCheckAuth({ taskBatchId: val, sourceId: this.$route.query.taskId })
            .then(res => {
              this.isRecheck = !!res?.data
            })
            .catch(errorReportOnly)
        }
      },
    },
  },
  created() {
    this.getPerformActionData()
  },
  methods: {
    handlePreview(currentImgUrl) {
      const allImages = this.dialogList
        .filter(item => item.contentType === this.CALL_INFO_TYPE_ENUMS.PHOTO)
        .map(item => item.content)
      //当前图片
      const currentIndex = allImages.indexOf(currentImgUrl)

      imagePreview({
        initIndex: currentIndex,
        images: allImages,
      })
    },
    // 获取数据
    getData() {
      API.getDialogRecord({ actionId: this.actionId })
        .then(res => {
          this.dialogList = res.data?.dialogSentenceList?.map(callRecord2DialogItem) || []
          this.expandAllComments = this.dialogList.length > 0

          const sequenceId = this.sequenceId
          if (sequenceId) {
            this.$emit('update:sequence-id', sequenceId)

            this.$nextTick(() => {
              const element = this.$el.querySelector(
                `.chat-content[data-sequence-id="${sequenceId}"]`,
              )
              if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' })
              }
            })
          }
          //播放条标注
          this.markData =
            res.data?.actionOperationList?.map(item => ({
              operationName: item.operationName,
              dialogVoiceSeconds: item.dialogVoiceSeconds / 1000,
            })) || []
        })
        .catch(errorReportOnly)
    },
    getPerformActionData() {
      API.getPerformActionInfo({ taskId: this.$route.query.taskId })
        .then(res => {
          const selectedAction = res.data?.find(item => item.actionId === this.actionId) ?? {}
          this.showAudioPlayer = selectedAction.actionType !== ACTION_TYPE_ENUMS.QW_CHAT
        })
        .catch(errorReportOnly)
    },

    // 刷新列表数据，并保留指定的展开评论状态
    refreshWithExpandComment(expandedSequenceIdList = []) {
      this.expandAllComments = true

      const expandedSequenceIdSet = new Set(expandedSequenceIdList)

      API.getDialogRecord({ actionId: this.actionId })
        .then(res => {
          this.dialogList =
            res.data?.dialogSentenceList?.map(callRecord2DialogItem).map(item => ({
              ...item,
              expandComment: expandedSequenceIdSet.has(item.sequenceId),
            })) || []
        })
        .catch(errorReportOnly)
    },
    //操作定位
    handleAction(val) {
      this.$emit('update:sequence-id', val)
    },

    // 获取头像
    getAvatar(data) {
      return data?.avatar || this.defaultAvatar
    },
    // 展开全部评论
    changeExpandAllComments(val) {
      val && this.lx.mc('b_gc_m_pnl5b5u6_mc')
      this.expandAllComments = val
      this.dialogList.forEach(item => {
        item.comment && (item.expandComment = val)
      })
    },
    // 展开/关闭全部评论
    handleExpandAllComments() {
      this.expandAllComments = this.dialogList.every(item => {
        return !item.comment || (item.comment && item.expandComment)
      })
    },
    // 展开/关闭评论
    handleExpandComment(data, type) {
      data.expandComment = !data.expandComment
      !data.expandComment && this.lx.mc('b_gc_m_pnl5b5u6_mc')
      type === 'edit' && this.handleExpandAllComments()
    },
    // 编辑评论
    editComment(data) {
      data.reviseComment = true
      this.lx.mc('b_gc_m_1kfvxy1b_mc')
    },
    // 抽检
    recheckComment(data) {
      data.reviseComment = true
      data.recheckEditable = true
    },
    // 取消评论
    cancelComment(data) {
      data.commentInfo = {
        markResult: data.comment?.markResult || null,
        markComment: data.comment?.markComment || '',
        problemCategories: data.comment?.problemCategories || null,
      }
      if (!data.comment) {
        data.expandComment = false
      } else {
        data.reviseComment = false
      }
      data.recheckEditable = false
    },
    // 提交评论
    submitComment(data, other) {
      if (!data.commentInfo.markResult) {
        this.$mtd.message.error('请全部填写后再提交')
        return
      }

      if (
        data.markLabelConfig?.find(item => item.code === data.commentInfo.markResult)
          ?.mustComment &&
        !data.commentInfo.markComment
      ) {
        this.$mtd.message.error('请全部填写后再提交')
        return
      }

      if (data.recheckEditable) {
        if (typeof data.recheckInfo.markResult !== 'number' && !data.recheckInfo.markResult) {
          this.$mtd.message.error('请全部填写后再提交')
          return
        }
      }

      this.lx.mc('b_gc_m_0dqwo3tv_mc')

      const params = {
        actionId: this.actionId,
        dialogMarkInfo: {
          sequenceId: data.sequenceId,
          content: data.content,
          role: data.role,
          dialogRoundCnt: data.dialogRoundCnt,
          comment: { ...data.commentInfo, version: other?.version || undefined },
        },
      }

      if (data.recheckEditable) {
        params.dialogMarkInfo.spotCheckContent = {
          ...data.recheckInfo,
          version: other?.version || undefined,
        }
      }

      data.loading = true
      API.submitDialogComment({ request: params })
        .then(() => {
          this.$mtd.message.success('评论成功')

          const expandedSequenceIdList = this.dialogList
            .filter(item => item.expandComment)
            .map(item => item.sequenceId)

          expandedSequenceIdList.push(data.sequenceId)
          this.refreshWithExpandComment(expandedSequenceIdList)
        })
        .catch(errorReportOnly)
        .finally(() => {
          data.loading = false
        })
    },
    formatDuration(timestamp) {
      if (!timestamp) return ''
      return dayjs.duration(timestamp, 'milliseconds').format('mm:ss')
    },
  },
})
</script>

<style lang="scss" scoped>
.universal-box {
  padding: 24px 30px 0 16px !important;
}

.call-record-container {
  flex: 1;
  border-right: 1px solid rgba($color: #111925, $alpha: 0.05);

  .action {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .call-record-content {
    padding-top: 24px;
    padding-left: 15px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .audio-player {
      border-top: 0;
      border-color: #e5e5e5;
    }
  }

  .downloadButton {
    margin-top: 3px;
    padding-left: 0;
  }

  .dialogue-box {
    flex: 1;
    overflow: auto;
    padding: 16px;
    border: 1px solid #e5e5e5;
    border-bottom: 0;
    border-radius: 8px 8px 0 0;
    background: #f8f8f8;
    transition: all 0.3s ease-in-out;

    .dialogue-item {
      margin-bottom: 16px;

      .chat-content {
        margin-bottom: 8px;
        display: flex;
        gap: 8px;

        // 对话框最多占据容器宽度的70%
        $chat-content-width: 88%;
        $chat-content-margin: calc(100% - $chat-content-width);

        &.left-align {
          margin-right: $chat-content-margin;
        }

        &.right-align {
          margin-left: $chat-content-margin;
        }

        .avatar {
          .mer-avatar,
          img {
            width: 36px;
            height: 36px;
            vertical-align: middle;
            border-radius: 50%;
          }

          .mer-avatar {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            color: var(--chat-content-color);
            border: 0.5px solid #e8e8e8;
            background-color: var(--color-primary);
          }
        }

        .content {
          position: relative;
          padding: 8px 12px;
          border-radius: 4px;
          background-color: #fff;
          border: 1px solid transparent;
        }

        .exhibition {
          position: absolute;
          bottom: 0;
          display: flex;
          gap: 4px;

          .operation {
            height: 20px;
            padding: 3px 4px;
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 12px;
            border-radius: 4px;
          }

          .add-comment {
            display: none;
            background: #fff;
          }

          .hit {
            background-color: var(--chat-operation-background);

            > span {
              display: flex;
              align-items: center;
              gap: 1px;
            }

            .line-back {
              background-color: rgba(22, 111, 247, 0.08);
            }

            .quantity {
              color: var(--color-primary);
            }
          }
        }
      }

      &.active {
        .chat-content .content {
          border: 1px solid #166ff7;
        }
      }

      .timeline {
        margin-left: 45px;
        text-align: left;
        color: #999;
        font-size: 12px;
      }

      .detailed {
        margin-right: 44px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .hit-description,
        .comment-content,
        .commented {
          width: 300px;
          border-radius: 6px;
        }

        .hit-description {
          margin-bottom: 8px;
          background: #f0f0f0;

          .header {
            padding-left: 12px;
            height: 36px;
            display: flex;
            gap: 2px;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 1px solid rgba($color: #ccc, $alpha: 0.6);
          }

          .content {
            padding: 8px 12px;
            transition: background 0.3s ease;

            .sub-link-title {
              color: var(--color-primary);
              font-weight: 500;
            }

            &:hover {
              background: rgba(22, 111, 247, 0.06);
            }
          }
        }

        .comment-content {
          padding: 8px 12px;
          background: #fff;

          .title {
            font-weight: 500;
          }

          .scoring {
            margin: 12px 0 8px;
            display: flex;
            gap: 8px;
            font-size: 12px;
            color: #666;
          }

          ::v-deep {
            .comment-textarea {
              .mtd-textarea-wrapper {
                width: 100%;

                textarea.mtd-textarea {
                  resize: none;
                }
              }
            }
          }

          .btn {
            margin-top: 8px;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
          }
        }

        .commented {
          background-color: #fff;

          .header {
            padding: 0 12px;
            height: 36px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #f0f0f0;

            span {
              font-size: 12px;
              color: #999;
            }

            span:nth-child(1) {
              margin-right: 8px;
              color: #222;
              font-size: 14px;
              font-weight: 500;
            }

            img {
              margin-right: 4px;
              width: 20px;
              height: 20px;
              border-radius: 50%;
              vertical-align: middle;
              border: 0.5px solid #e8e8e8;
            }
          }

          .user-info {
            max-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .commented-content {
            padding: 12px;

            a {
              color: var(--color-primary);
              float: right;
              font-size: 12px;
              text-decoration: none;
            }
          }
        }
      }

      &:hover {
        .exhibition .add-comment {
          display: flex;
        }
      }

      &.reverse {
        .chat-content {
          flex-direction: row-reverse;

          .reverse-content {
            color: #222222;
            background-color: #dbebff;
          }
        }

        .timeline {
          margin-right: 45px;
          text-align: right;
        }
      }
    }
  }
}

.clearfix::after {
  content: '';
  display: block;
  clear: both;
}

.vertical-line {
  margin: 0 4px;
  width: 1px;
  height: 12px;
  background-color: #e5e5e5;
}

.theme-icon {
  color: var(--chat-icon-color);
}

.markers {
  position: absolute;
  top: 17px;
  left: 0;
  right: 0;
  height: 3px;
  z-index: 1;
  flex: 1;
  width: 99%;
  -webkit-appearance: none;
  border-radius: 1px 1px 1px 1px;
}

.marker {
  position: absolute;
  bottom: -4px;
  transform: translateX(-50%);
  cursor: pointer;
  z-index: 10;
  margin-left: 5px;

  .marker-dot {
    width: 8px;
    height: 8px;
    background-color: var(--color-primary, #166ff7);
    border-radius: 50%;
  }
}

.media-content {
  max-height: 400px;

  &.media-image {
    max-width: 400px;
  }

  &.media-video {
    max-width: 450px;
  }
}
</style>
