<template>
  <span class="score-tag">
    <mtd-tag
      v-for="(tag, index) in tags"
      :key="index"
      :class="{ 'active-tag': tag.code === value }"
      type="ghost"
      size="small"
      @click="handleTagClick(tag)"
    >
      {{ tag.label }}
    </mtd-tag>
  </span>
</template>

<script>
export default {
  name: 'ScoreTag',
  props: {
    tags: {
      type: Array,
      default: () => [],
    },
    value: {
      type: [Number, String],
      default: '',
    },
  },
  methods: {
    handleTagClick(tag) {
      this.$emit('input', this.value === tag.code ? '' : tag.code)
    },
  },
}
</script>

<style lang="scss" scoped>
.score-tag {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.mtd-tag {
  cursor: pointer;
}

.active-tag {
  color: var(--color-primary);
  border-color: var(--color-primary);
}
</style>
