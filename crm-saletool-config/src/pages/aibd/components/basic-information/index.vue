<template>
  <!-- 基础信息 -->
  <UniversalBox :title="`基础信息 (任务ID: ${$route.query.taskId})`">
    <!-- 下载通话质检明细 -->
    <template #action>
      <a href="javascript:;" @click="downloadDetailed">
        <mtd-icon
          style="font-size: 14px; color: #166ff7"
          name="mtdicon-download-o"
        />下载通话质检明细
      </a>
    </template>

    <!-- 基础信息内容 -->
    <div class="basic-information-content">
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">门店名称：</label>
        <div class="basic-information-content-value">{{ basicInformation.dpShopName }}</div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">门店ID：</label>
        <div class="basic-information-content-value">
          <span>
            {{ basicInformation.dpShopId }}
            <ContentCopy :content="basicInformation.dpShopId" />
          </span>
        </div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">商户电话：</label>
        <div class="basic-information-content-value">
          <PhoneNum :phone="basicInformation.shopOwnerPhone" />
        </div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">销售：</label>
        <div class="basic-information-content-value">
          {{ basicInformation.salesName }}/{{ basicInformation.salesMisId }}
        </div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">呼出时间：</label>
        <div class="basic-information-content-value">{{ basicInformation.callOutTime }}</div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">持续时间：</label>
        <div class="basic-information-content-value">{{ basicInformation.callSeconds }}秒</div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">通话ID：</label>
        <div class="basic-information-content-value">
          <span>
            {{ basicInformation.callId }}
            <ContentCopy :content="basicInformation.callId" />
          </span>
        </div>
      </div>
      <div class="basic-information-content-box">
        <label class="basic-information-content-label">通话场景：</label>
        <div class="basic-information-content-value">{{ basicInformation.scene }}</div>
      </div>
    </div>
  </UniversalBox>
</template>

<script>
import API from '../../api'
import { ContentCopy, PhoneNum, UniversalBox } from '@/components'

export default {
  name: 'BasicInformation',
  components: {
    UniversalBox,
    ContentCopy,
    PhoneNum,
  },
  props: {
    basicInformation: {
      type: Object,
      default: () => ({}),
    },
    lx: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    // 下载通话质检明细
    downloadDetailed() {
      this.lx.mc('b_gc_m_1b5feb80_mc')
      API.downloadCallQualityInspection({ actionId: this.basicInformation.actionId })
        .then(res => this.$mtd.message.success(res.message || '下载成功'))
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.basic-information-content {
  padding: 20px 0 25px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  border-bottom: 1px solid rgba($color: #111925, $alpha: 0.05);

  .basic-information-content-box {
    width: calc(25% - 12px);
    display: flex;

    .basic-information-content-label {
      text-align: right;
      font-size: 14px;
      color: #666;
      width: 70px;
    }

    .basic-information-content-value {
      flex: 1;
      align-self: flex-start;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      font-size: 14px;
      word-break: break-all;
    }
  }
}

.vertical-line {
  margin: 0 4px;
  width: 1px;
  height: 12px;
  background-color: #e5e5e5;
}

.width-70 {
  width: 70px;
}

.width-98 {
  width: 98px;
}
</style>
