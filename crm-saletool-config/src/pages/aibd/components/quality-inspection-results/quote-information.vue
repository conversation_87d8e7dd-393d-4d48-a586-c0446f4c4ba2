<template>
  <div class="quote-information-container">
    <!-- 放在第一个标题右边 -->
    <div class="copy-icon-wrapper">
      <ContentCopy
        v-if="Object.keys(allTtsParams).length"
        :content="formatTtsParams(allTtsParams)"
      />
    </div>
    <QuoteInformationTreeNode
      v-for="(info, index) in extendReferenceModelDTOList"
      :key="index"
      :node="info"
    />
  </div>
</template>

<script>
import API from '../../api'
import { errorReportOnly, safeJSONParse } from '@/lib/utils'
import { ContentCopy } from '@/components'
import QuoteInformationTreeNode from './quote-information-tree-node.vue'
export default {
  name: 'QuoteInformation',
  components: {
    QuoteInformationTreeNode,
    ContentCopy,
  },
  props: {
    actionId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      referenceInfo: {},
    }
  },
  computed: {
    extendReferenceModelDTOList() {
      return safeJSONParse(this.referenceInfo.extendReferenceModelDTOListJson)
    },
    allTtsParams() {
      return this.extractAllTtsParams()
    },
  },
  watch: {
    actionId: {
      immediate: true,
      handler(val) {
        if (val) this.refresh()
      },
    },
  },
  methods: {
    refresh() {
      this.getReferenceInfo()
    },
    //获取引用信息
    getReferenceInfo() {
      return API.getReferenceInfo({ actionId: this.actionId })
        .then(res => {
          this.referenceInfo = res.data || {}
        })
        .catch(errorReportOnly)
    },
    /**
     * 提取所有节点的 ttsParams
     * @returns {Object} 合并后的 ttsParams
     */
    extractAllTtsParams() {
      const result = {}
      const extractParams = node => {
        // 合并当前节点的 ttsParams
        if (node.ttsParams) {
          Object.assign(result, node.ttsParams)
        }
        // 递归处理子节点
        if (node.children && node.children.length) {
          node.children.forEach(child => extractParams(child))
        }
      }
      // 处理所有根节点
      this.extendReferenceModelDTOList?.forEach(node => extractParams(node))
      return result
    },
    formatTtsParams(ttsParams) {
      return JSON.stringify(ttsParams, null, 2)
    },
  },
}
</script>

<style lang="scss" scoped>
.quote-information-container {
  overflow: auto;
  max-height: calc(100vh - 160px);
  .copy-icon-wrapper {
    display: flex;
    justify-content: flex-end;
    padding: 0 9px;
    margin-bottom: -26px;
  }
}
</style>
