<template>
  <div class="session-information-content">
    <div
      v-for="extendInfo in basicInformation.extendedCallInfoList || []"
      :key="extendInfo.title"
      class="session-information-content-box"
    >
      <div class="session-information-content-label">{{ extendInfo.title }}：</div>
      <div class="session-information-content-value">
        <template v-if="extendInfo.rule === SESSION_RULES_ENUMS.CONTENT_COPY">
          <span v-html="extendInfo.content"></span>
          <ContentCopy :content="extendInfo.content" />
        </template>
        <template v-else-if="extendInfo.rule === SESSION_RULES_ENUMS.PHONE_NUM_VISIBLE">
          <PhoneNum :phone="extendInfo.content" />
        </template>
        <template v-else>
          <span v-html="extendInfo.content"></span>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import { ContentCopy, PhoneNum } from '@/components'
import { SESSION_RULES_ENUMS } from '../../lib/dict'

export default {
  name: 'SessionInformation',
  components: {
    ContentCopy,
    PhoneNum,
  },
  props: {
    // 当前页面选中的行为ID
    actionId: {
      type: Number,
      default: 0,
    },
    basicInformation: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      SESSION_RULES_ENUMS,
    }
  },
}
</script>
<style lang="scss" scoped>
.session-information-content {
  overflow: auto;
  max-height: calc(100vh - 160px);
  margin: 8px;
  border-radius: 6px;
  background: #f8f8f8;

  .session-information-content-box {
    padding: 10px;
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    ::v-deep {
      .mtd-tab-content {
        display: none;
      }
    }

    .session-information-content-label {
      margin-left: 18px;
      text-align: left;
      color: #050505e6;
      font-size: 12px;
      width: 38%;
      font-weight: bold;
    }
    .session-information-content-value {
      flex: 1;
      align-self: flex-start;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      font-size: 12px;
      word-break: break-all;
    }
  }
}
</style>
