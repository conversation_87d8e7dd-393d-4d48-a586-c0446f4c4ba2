<template>
  <ExpandSidebar :items="QUALITY_INSPECTION_SIDEBAR_ITEMS">
    <template #qualityInspectionResult>
      <QualityInspection :basic-information="basicInformation" :action-id="actionId" :lx="lx" />
    </template>
    <template #sessionInformation>
      <SessionInformation :basic-information="basicInformation" />
    </template>
    <template #referenceInformation>
      <QuoteInformation :action-id="actionId" />
    </template>
  </ExpandSidebar>
</template>

<script>
import QualityInspection from './quality-inspection.vue'
import SessionInformation from './session-information.vue'
import QuoteInformation from './quote-information.vue'
import ExpandSidebar from '../expand-sidebar/index.vue'
import { QUALITY_INSPECTION_SIDEBAR_ITEMS } from '../../lib/dict'

export default {
  name: 'QualityInspectionResults',
  components: {
    QualityInspection,
    SessionInformation,
    QuoteInformation,
    ExpandSidebar,
  },
  props: {
    // 当前页面选中的行为ID
    actionId: {
      type: Number,
      default: 0,
    },
    basicInformation: {
      type: Object,
      default: () => ({}),
    },
    lx: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      QUALITY_INSPECTION_SIDEBAR_ITEMS,
    }
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .mtd-tabs-nav-container {
    margin: 13px 0 0 18px;
    color: #222222;
  }

  .universal-box {
    height: 100%;
    padding: 15px 16px 0;
    .quality-inspection-results-content {
      flex: 1;
      overflow: auto;
      padding-right: 15px;
      max-height: calc(100vh - 230px);
    }
  }

  .universal-header {
    margin-top: -4px;
    padding-bottom: 12px;

    &::before {
      display: none;
    }

    .universal-title {
      display: flex;
      align-items: center;
      margin-left: -9px;
    }

    span {
      font-size: 12px;
      color: #999;
    }

    .vertical-line {
      margin: 0 4px;
      width: 1px;
      height: 12px;
      background-color: #e5e5e5;
    }

    img {
      margin: 0 4px 0 8px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      vertical-align: middle;
    }
  }

  .mtd-table {
    .mtd-table-header {
      border-radius: 6px;
      overflow: hidden;

      th {
        border-bottom: 0;
        background-color: #f8f8f8;
      }
    }

    .mtd-table-body {
      tr.hover > td,
      tr:hover > td {
        background-color: rgba($color: #f8f8f8, $alpha: 0.4);
      }
    }

    .deviation {
      margin-left: -25px;
    }

    .mtd-table-expand-icon {
      font-size: 20px;
    }
  }

  .quality-title-textarea {
    margin-top: 20px;
    width: 100%;

    .mtd-textarea {
      height: 100px;
    }

    &.mtd-textarea-readonly {
      .mtd-textarea {
        background-color: #f5f5f5;
        cursor: default;
      }
    }
  }
}
</style>
