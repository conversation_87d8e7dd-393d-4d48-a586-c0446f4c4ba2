<template>
  <div class="quote-information-tree-node">
    <div class="quote-information-header">
      <div class="quote-information-list-title">{{ node.title }}</div>
    </div>
    <div v-if="node.extendInfoList && node.extendInfoList.length" class="quote-information-content">
      <div v-for="(extendInfo, i) in node.extendInfoList" :key="i" class="quote-information-box">
        <label class="quote-information-label">{{ extendInfo.title }}:</label>
        <span class="quote-information-value">{{ formatContent(extendInfo.content) }}</span>
      </div>
    </div>
    <div v-if="node.children && node.children.length" class="children">
      <QuoteInformationTreeNode
        v-for="(child, index) in node.children"
        :key="index"
        :node="child"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuoteInformationTreeNode',
  props: {
    node: {
      type: Object,
      required: true,
    },
  },
  methods: {
    formatContent(content) {
      return content?.replace(/<\/br\s*\/?>/gi, '\n') || ''
    },
  },
}
</script>

<style lang="scss" scoped>
.quote-information-tree-node {
  margin-bottom: 16px;
  .quote-information-content {
    margin: 8px;
    border-radius: 6px;
    background: #f8f8f8;

    .quote-information-box {
      padding: 10px;
      display: flex;
      flex-wrap: wrap;
      width: 100%;
    }

    .quote-information-label {
      margin: 0 18px;
      text-align: left;
      color: #050505e6;
      font-size: 12px;
      font-weight: bold;
    }

    .quote-information-value {
      flex: 1;
      align-self: flex-start;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      font-size: 12px;
      margin: 0 2px;
      word-break: break-all;
      white-space: pre-wrap;
    }
  }

  .quote-information-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 9px;

    .quote-information-list-title {
      margin: 5px 0;
      font-size: 15px;
      font-weight: 500;
      color: #050505e6;
    }
  }
  .children {
    margin-left: 20px;
  }
}
</style>
