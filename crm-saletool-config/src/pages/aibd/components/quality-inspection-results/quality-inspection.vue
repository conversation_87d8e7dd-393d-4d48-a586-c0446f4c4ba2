<template>
  <UniversalBox>
    <template #title>
      <template v-if="markUser && markUser.reviewerMisId">
        <img :src="getAvatar(markUser)" alt="" />
        <span>{{ markUser.reviewerName }}/{{ markUser.reviewerMisId }}</span>
        <span class="vertical-line"></span>
        <span>{{ markUser.createTime }}</span>
      </template>
    </template>
    <template #action>
      <mtd-button v-if="isEdit" :loading="loading" type="primary" @click="submitResult">
        提交
      </mtd-button>
      <mtd-button v-else @click="editResult">编辑</mtd-button>
    </template>
    <div class="quality-inspection-results-content">
      <!-- 整体结果 -->
      <div class="quality-title">
        <span class="sort">01</span>
        <span class="title">整体结果</span>
      </div>

      <mtd-table v-if="isEdit" :data="overAllResultData">
        <mtd-table-column prop="title" label="打分项" />
        <mtd-table-column align="center" label="人工质检分" width="173">
          <template #default="{ row, $index }">
            <div v-if="$index === 0">
              <ScoreTag v-model="row.tagResult" :tags="row.markLabelConfig" />
            </div>
            <div v-else-if="$index === 1" class="select-input-wrapper">
              <mtd-select
                v-model="row.reasonResult"
                style="width: 149px"
                multiple
                clearable
                :field-names="{ label: 'reasonLabel', value: 'reasonLabel' }"
              >
                <mtd-option
                  v-for="option in row.unConvertReasonConfig"
                  :key="option.reasonLabel"
                  :label="option.reasonLabel"
                  :value="option.reasonLabel"
                >
                  <template #default>
                    <span>{{ option.reasonLabel }}</span>
                    <div
                      v-if="option.showCommentBox && row.reasonResult.includes(option.reasonLabel)"
                    >
                      <mtd-input
                        v-model="row.reasonComment"
                        placeholder="请输入内容"
                        :max-length="20"
                        show-count
                        class="overall-input"
                      />
                    </div>
                  </template>
                </mtd-option>
              </mtd-select>
            </div>
          </template>
        </mtd-table-column>
      </mtd-table>
      <div v-else>
        <div :style="OVERALL_STYLE[overAllResultData[0].tagResult]" class="overall-results">
          {{ getTagLabel(overAllResultData[0]) || '暂无结果' }}
        </div>
        <div class="overall-results-reason">
          <span class="overall-results-reason-title">未转化原因：</span>
          <span class="overall-results-reason-content">{{
            determineUnconvertReason(overAllResultData[1])
          }}</span>
        </div>
      </div>

      <!-- 思路环节 -->
      <div class="quality-title">
        <span class="sort">02</span>
        <span class="title">思路环节</span>
      </div>

      <mtd-table row-key="title" :data="linkResultData" :expand-row-keys="linkExpandRowKeys">
        <mtd-table-column type="expand" width="27">
          <template #default="{ row }">
            <div class="subrule-box">
              <div v-for="(subRule, index) in row.subRules" :key="index" class="subrule-item">
                <i
                  class="mtdicon-triangle-right arrow"
                  :class="{ 'arrow-rotate': subRule.expand }"
                  style="cursor: pointer"
                  @click="subRule.expand = !subRule.expand"
                ></i>
                <div class="subrule-content">
                  <span class="subrule-title">
                    {{ subRule.title }}
                  </span>
                  <div v-show="subRule.expand" class="semantic-box">
                    <div
                      v-for="(semantic, i) in subRule.semanticList"
                      :key="i"
                      class="semantic-item"
                    >
                      <span class="semantic-name"> {{ semantic.semanticName }}： </span>
                      <span class="semantic-value">
                        {{ semantic.semanticValue }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </mtd-table-column>
        <mtd-table-column prop="title" label-class="deviation" label="打分项" />
        <mtd-table-column align="left" label="人工质检分" width="134">
          <template #default="{ row }">
            <ScoreTag v-if="isEdit" v-model="row.tagResult" :tags="row.markLabelConfig" />
            <mtd-tag
              v-else-if="typeof row.tagResult === 'number'"
              type="ghost"
              :theme="TAG_COLOR[row.tagResult] || 'green'"
              size="small"
            >
              {{ getTagLabel(row) }}
            </mtd-tag>
            <span v-else>-</span>
          </template>
        </mtd-table-column>
      </mtd-table>

      <!-- 违规红线 -->
      <div class="quality-title">
        <span class="sort">03</span>
        <span class="title">违规红线</span>
      </div>

      <mtd-table :data="violationResultData">
        <mtd-table-column prop="title" label="打分项" />
        <mtd-table-column align="left" label="人工质检分" width="134">
          <template #default="{ row }">
            <ScoreTag v-if="isEdit" v-model="row.tagResult" :tags="row.markLabelConfig" />
            <mtd-tag
              v-else-if="typeof row.tagResult === 'number'"
              type="ghost"
              :theme="ILLEGAL_TAG_COLOR[row.tagResult]"
              size="small"
            >
              {{ getTagLabel(row) }}
            </mtd-tag>
            <span v-else>-</span>
          </template>
        </mtd-table-column>
      </mtd-table>
      <div>
        <mtd-textarea
          v-model="comment"
          :placeholder="isEdit ? '请输入评价' : ''"
          :class="['quality-title-textarea', { 'mtd-textarea-readonly': !isEdit }]"
          :max-length="500"
          show-count
          :readonly="!isEdit"
        >
        </mtd-textarea>
      </div>
    </div>
  </UniversalBox>
</template>

<script>
import avatar from '@/assets/<EMAIL>'
import API from '../../api'
import { errorReportOnly } from '@/lib/utils'
import { ScoreTag } from '../index'
import { UniversalBox } from '@/components'
import { TAG_COLOR, OVERALL_STYLE, ILLEGAL_TAG_COLOR } from '../../lib/dict'

const initialValue = {
  markUser: {},
  overAllResultData: [{ tagResult: '', reasonResult: [] }],
  linkResultData: [],
  violationResultData: [],
  comment: '',
}
export default {
  name: 'QualityResult',
  components: {
    UniversalBox,
    ScoreTag,
  },
  props: {
    actionId: {
      type: Number,
      default: 0,
    },
    basicInformation: {
      type: Object,
      default: () => ({}),
    },
    lx: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      TAG_COLOR,
      OVERALL_STYLE,
      ILLEGAL_TAG_COLOR,
      defaultAvatar: avatar, // 默认头像
      loading: false, // loading
      isEdit: false, // 是否正在编辑
      linkExpandRowKeys: [], // 思路环节展开行key
      version: '',
      ...initialValue,
    }
  },
  watch: {
    actionId: {
      immediate: true,
      handler(val) {
        if (val) this.refresh()
      },
    },
  },
  methods: {
    async refresh() {
      await this.getData()
      this.setStateByInspectResult()
    },
    // 获取头像
    getAvatar(data) {
      return data?.avatar || this.defaultAvatar
    },
    // 获取标签文案
    getTagLabel(row) {
      const { markLabelConfig = [], tagResult } = row
      return markLabelConfig.find(item => item.code === tagResult)?.label || ''
    },
    //转化原因
    determineUnconvertReason(row) {
      if (!row || !row.reasonResult || !row.reasonResult.length) {
        return '-'
      }
      const { reasonResult = [], reasonComment } = row
      return reasonResult
        .map(reasonLabel => {
          const reason = row.unConvertReasonConfig.find(it => it.reasonLabel === reasonLabel)
          if (reason && reason.showCommentBox) {
            return `${reason.reasonLabel}原因：${reasonComment || '-'}`
          }
          return reason ? reason.reasonLabel : ''
        })
        .join(', ')
    },
    // 获取质检结果数据
    getData() {
      this.clearData()
      return API.getInspectionResult({ actionId: this.actionId })
        .then(res => {
          const data = res.data
          if (!data) return
          this.version = data.version || ''
          this.markUser = data.markUser || null
          this.isEdit = !data.markUser
          this.overAllResultData = data.overAllResults?.map(item => {
            return {
              ...item,
              tagResult: item.markResult?.markResult ?? '',
              reasonResult: item.markResult?.unConvertReasons?.map(item => item.reasonLabel) ?? [],
              reasonComment:
                item.markResult?.unConvertReasons?.find(reason => reason.otherReason)
                  ?.otherReason ?? '',
            }
          })
          this.linkResultData = data?.linkResult?.map(item => {
            return {
              ...item,
              tagResult: item.markResult?.markResult ?? '',
              subRules: item.subRules.map(subRule => ({
                ...subRule,
                expand: false,
              })),
            }
          })
          this.violationResultData = data?.violationResult?.map(item => {
            return {
              ...item,
              tagResult: item.markResult?.markResult ?? '',
            }
          })
          this.comment = data.comment || ''
        })
        .catch(errorReportOnly)
    },
    clearData() {
      Object.assign(this.$data, initialValue)
    },
    setStateByInspectResult() {
      // 没有质检的通话，进入页面时默认为编辑态
      if (typeof this.overAllResultData[0].tagResult !== 'number') {
        this.isEdit = true
      }
      this.checkIfOnlyOneRuleAndExpand()
    },
    // 检查是否只有一个规则，并展开
    checkIfOnlyOneRuleAndExpand() {
      const linkResultData = this.linkResultData

      if (linkResultData.length !== 1) return

      this.linkExpandRowKeys = [linkResultData[0].title]

      if (linkResultData[0].subRules.length !== 1) return

      linkResultData[0].subRules[0].expand = true
    },
    // 编辑质检结果
    editResult() {
      this.isEdit = true
      this.lx.mc('b_gc_m_ks0v09oo_mc')
    },
    // 处理参数
    handleParams() {
      const {
        basicInformation,
        overAllResultData,
        linkResultData,
        violationResultData,
        version,
        comment,
      } = this

      return {
        actionId: basicInformation.actionId,
        inspectionMarkInfo: {
          version,
          overAllResults: overAllResultData.map(item => ({
            title: item.title,
            markResult: {
              markResult: item.tagResult,
              unConvertReasons: item.reasonResult.map(reasonLabel => {
                const reason = (item.unConvertReasonConfig || []).find(
                  it => it.reasonLabel === reasonLabel,
                )
                return reason
                  ? {
                      reasonLabel: reasonLabel,
                      otherReason: reason.showCommentBox ? item.reasonComment : '',
                    }
                  : {}
              }),
            },
          })),

          linkResult: linkResultData.map(item => ({
            title: item.title,
            markResult: {
              markResult: item.tagResult,
            },
          })),
          violationResult: violationResultData.map(item => ({
            title: item.title,
            markResult: {
              markResult: item.tagResult,
            },
          })),
          comment: comment,
        },
      }
    },
    // 提交质检结果
    submitResult() {
      const params = this.handleParams()
      if (!params) return
      this.lx.mc('b_gc_m_drq2blqh_mc')
      this.loading = true
      API.submitInspectionResult({ request: params })
        .then(() => {
          this.$mtd.message.success('提交成功')
          this.isEdit = false
          this.getData()
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.quality-inspection-results-container {
  position: relative;

  .overall-input {
    width: 100%;
    margin-top: 8px;
  }

  .quality-inspection-results-sidebar {
    width: 64px;
    height: 64px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #166ff7;

    i {
      font-size: 20px;
      margin-bottom: 5px;
    }

    span {
      font-size: 12px;
      text-align: center;
    }
  }

  width: 27%;

  .quality-inspection-results-header {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      height: 16px;
      width: 4px;
      left: 0px;
      background-color: var(--color-primary);
      border-radius: 0px 2px 2px 0px;
      margin-top: 13px;
    }
  }

  ::v-deep {
    .mtd-tabs-nav-container {
      margin: 13px 0 0 18px;
      color: #222222;
    }

    .universal-box {
      height: 100%;
      padding: 15px 16px 0;
      .quality-inspection-results-content {
        flex: 1;
        overflow: auto;
        padding-right: 15px;
        max-height: calc(100vh - 230px);
      }
    }

    .universal-header {
      margin-top: -4px;
      padding-bottom: 24px;

      &::before {
        display: none;
      }

      .universal-title {
        display: flex;
        align-items: center;
        margin-left: -9px;
      }

      span {
        font-size: 12px;
        color: #999;
      }

      .vertical-line {
        margin: 0 4px;
        width: 1px;
        height: 12px;
        background-color: #e5e5e5;
      }

      img {
        margin: 0 4px 0 8px;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        vertical-align: middle;
      }
    }

    .mtd-table {
      .mtd-table-header {
        border-radius: 6px;
        overflow: hidden;

        th {
          border-bottom: 0;
          background-color: #f8f8f8;
        }
      }

      .mtd-table-body {
        tr.hover > td,
        tr:hover > td {
          background-color: rgba($color: #f8f8f8, $alpha: 0.4);
        }
      }

      .deviation {
        margin-left: -25px;
      }

      .mtd-table-expand-icon {
        font-size: 20px;
      }
    }

    .quality-title-textarea {
      margin-top: 20px;
      width: 100%;

      .mtd-textarea {
        height: 100px;
      }

      &.mtd-textarea-readonly {
        .mtd-textarea {
          background-color: #f5f5f5;
          cursor: default;
        }
      }
    }
  }

  .quality-title {
    margin: 18px 0 16px;
    font-size: 16px;

    &:first-of-type {
      margin-top: 0;
    }

    .sort {
      margin-right: 2px;
      font-family: Meituan Type, serif;
      font-size: 16px;
      font-weight: bold;
      color: var(--quality-sort-title-color);
    }

    .title {
      font-weight: 500;
    }
  }

  .overall-results {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    font-weight: 500;
    border-radius: 10px;
    background: rgba(0, 168, 90, 0.06);
  }

  .overall-results-reason {
    min-height: 60px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(134, 130, 130, 0.04);
    margin-top: 12px;
    padding: 10px;
    height: auto;
    overflow: hidden;

    .overall-results-reason-title {
      font-weight: bold;
      color: #222222;
      white-space: nowrap;
    }

    .overall-results-reason-content {
      margin: 5px;
    }
  }

  .subrule-box {
    width: 100%;
    display: flex;
    flex-direction: column;

    .subrule-item {
      padding: 12px 0 12px 32px;
      flex: 1;
      display: flex;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: rgba($color: #f8f8f8, $alpha: 0.4);
      }

      .arrow {
        height: 22px;
        font-size: 20px;
        transition: transform 0.2s ease-in-out;
        color: rgba(0, 0, 0, 0.5);

        &.arrow-rotate {
          transform: rotate(90deg);
        }
      }

      .subrule-content {
        flex: 1;

        .subrule-title {
          font-size: 14px;
        }

        .semantic-box {
          margin-top: 12px;
          padding: 8px;
          width: 100%;
          border-radius: 6px;
          background: #f8f8f8;

          .semantic-item {
            font-size: 12px;

            .semantic-name {
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .toggle-button {
    position: fixed;
    right: 27%;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 90px;
    background: #ffffff;
    border: 1px solid #f0f0f0;
    border-right: none;
    border-radius: 28px 0 0 28px;
    cursor: pointer;
    color: #666666;
    display: flex;
    align-items: center;

    .toggle-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px 0;
      font-size: 12px;

      .toggle-text {
        writing-mode: vertical-rl;
        text-orientation: upright;
        letter-spacing: 2px;
      }

      .toggle-icon {
        margin-top: 10px;
      }
    }
  }

  .quality-inspection-content {
    overflow: auto;
    max-height: calc(100vh - 160px);
    margin: 8px;
    border-radius: 6px;
    background: #f8f8f8;

    .quality-inspection-box {
      padding: 10px;
      display: flex;
      flex-wrap: wrap;
      width: 100%;
    }

    .quality-inspection-label {
      margin: 0 18px;
      text-align: left;
      color: #050505e6;
      font-size: 12px;
      font-weight: bold;
    }

    .quality-inspection-value {
      flex: 1;
      align-self: flex-start;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      font-size: 12px;
      margin: 0 2px;
      word-break: break-all;
    }
  }
}
</style>
