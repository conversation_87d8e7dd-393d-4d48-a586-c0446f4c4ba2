<template>
  <div class="comment-picker">
    <!-- 编辑态 -->
    <div v-if="editable" class="comment-content">
      <div class="title">请评价该会话</div>
      <div class="scoring">
        <span>打分:</span>
        <ScoreTag v-model="innerValues.markResult" :tags="tags" />
      </div>
      <div v-if="problemCategoryLabels" class="scoring">
        <span>错误原因:</span>
        <mtd-cascader
          v-model="innerValues.problemCategories"
          :data="problemCategoryLabels"
          size="small"
          :multiple="true"
          checked-strategy="children"
          :field-names="{
            label: 'problemCategory',
            value: 'problemCategory',
            children: 'childProblemCategory',
          }"
          style="flex: 1"
          popper-class="comment-cascader"
        />
      </div>
      <div class="comment-textarea">
        <mtd-textarea
          :value="
            innerValues.markComment ||
            getTagAttributeByCode(innerValues.markResult, 'commentTemplate', '')
          "
          placeholder="请输入评论"
          :autosize="{ minRows: 6, maxRows: 8 }"
          :max-length="500"
          @input="val => (innerValues.markComment = val)"
          @keydown="handleKeydown"
        ></mtd-textarea>
      </div>
      <template v-if="recheckEditable">
        <div class="title" style="margin-top: 8px">请对评论进行复检</div>
        <div class="scoring">
          <span>是否通过:</span>
          <ScoreTag v-model="recheckValue.markResult" :tags="recheckTags" />
        </div>
        <div class="comment-textarea">
          <mtd-textarea
            v-model="recheckValue.markComment"
            placeholder="请输入备注说明"
            :autosize="{ minRows: 6, maxRows: 8 }"
            :max-length="200"
            show-count
          ></mtd-textarea>
        </div>
      </template>
      <div class="btn">
        <mtd-button size="small" @click="handleCancel">取消</mtd-button>
        <mtd-button :loading="loading" type="primary" size="small" @click="handleSubmit">
          确认
        </mtd-button>
      </div>
    </div>

    <!-- 评论展示态 -->
    <CommentCard v-else-if="currentComment" :comment="currentComment" title="评论">
      <template #headerRight>
        <mtd-picker
          v-model="commentVersion"
          :clearable="false"
          size="small"
          :options="commentVersionList"
          @change="handleCommentVersionChange"
          @update:visible="handleUpdateVisible"
        />
      </template>
      <mtd-tag type="ghost" :theme="TAG_COLOR[currentComment.markResult]" size="small">
        {{ getTagAttributeByCode(currentComment.markResult, 'label') }}
      </mtd-tag>
      <mtd-tag
        v-for="itemCategorys of currentComment.problemCategories"
        :key="itemCategorys.join('-')"
        theme="black"
        size="small"
        style="max-width: 300px; margin-left: 4px"
      >
        {{ itemCategorys.filter(Boolean).join('-') }}
      </mtd-tag>
      <div class="mark-comment">
        {{ currentComment.markComment }}
        <a href="javascript:;" @click="handleEdit">编辑</a>
        <a v-if="isRecheck && !spotCheckContent" href="javascript:;" @click="handleRecheck">
          复检
        </a>
      </div>
    </CommentCard>

    <!-- 抽检评论展示 -->
    <CommentCard
      v-if="spotCheckContent"
      :comment="spotCheckContent"
      title="复检评论"
      class="recheck-comment"
    >
      <div>检核的版本: V{{ spotCheckContent.version }}</div>
      <mtd-tag type="ghost" :theme="RECHECK_TAG_COLOR[spotCheckContent.markResult]" size="small">
        {{ getRecheckTagAttributeByCode(spotCheckContent.markResult, 'label') }}
      </mtd-tag>
      <div class="mark-comment">
        {{ spotCheckContent.markComment }}
      </div>
    </CommentCard>
  </div>
</template>

<script>
import { defineComponent } from '@vue/composition-api'
import { ScoreTag } from '../index'
import { TAG_COLOR, RECHECK_TAG_COLOR } from '../../lib/dict'
import { safeJSONParse, errorReportOnly } from '@/lib/utils'
import API from '../../api'
import CommentCard from './comment-card.vue'

export default defineComponent({
  name: 'CommentPicker',
  components: {
    ScoreTag,
    CommentCard,
  },
  props: {
    editable: {
      type: Boolean,
      default: false,
    },
    values: {
      type: Object,
      default: () => ({
        markResult: null,
        markComment: '',
        problemCategories: [],
      }),
    },
    comment: {
      type: Object,
      default: null,
    },
    tags: {
      type: Array,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    // 抽检信息
    spotCheckContent: {
      type: Object,
      default: null,
    },
    recheckInfo: {
      type: Object,
      default: () => ({
        markResult: null,
        markComment: '',
      }),
    },
    // 是否可抽检
    isRecheck: {
      type: Boolean,
      default: false,
    },
    // 当前页面选中的行为ID
    actionId: {
      type: Number,
      default: 0,
    },
    sequenceId: {
      type: Number,
      default: 0,
    },
    // 抽检编辑
    recheckEditable: {
      type: Boolean,
      default: false,
    },
    recheckTags: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      TAG_COLOR,
      innerValues: {
        ...this.values,
      },
      recheckValue: {
        ...this.recheckInfo,
      },
      RECHECK_TAG_COLOR,
      // 评论版本列表加载
      pickerLoading: false,
      // 评论版本列表是否加载过
      loaded: false,
      // 评论版本列表
      commentVersionList: [
        {
          value: this.comment?.version,
          label: `V${this.comment?.version}`,
        },
      ],
      // 评论版本
      commentVersion: this.comment?.version,
      commentVersionMap: {},
    }
  },
  computed: {
    problemCategoryLabels() {
      const labelStr = this.getTagAttributeByCode(
        this.innerValues.markResult,
        'problemCategoryLabels',
      )
      return safeJSONParse(labelStr)
    },
    currentComment() {
      return this.commentVersionMap[this.commentVersion] || this.comment
    },
  },
  watch: {
    values: {
      handler(val) {
        this.innerValues = val
        if (val.problemCategories == null) {
          this.innerValues.problemCategories = []
        }
      },
      deep: true,
      immediate: true,
    },
    innerValues: {
      handler(val) {
        this.$emit('update:values', val)
      },
      deep: true,
    },
    recheckInfo: {
      handler(val) {
        this.recheckValue = val
      },
      deep: true,
      immediate: true,
    },
    recheckValue: {
      handler(val) {
        this.$emit('update:recheckInfo', val)
      },
      deep: true,
    },
  },
  methods: {
    getTagAttributeByCode(code, attribute, defaultValue = '') {
      return this.tags.find(item => item.code === code)?.[attribute] || defaultValue
    },
    getRecheckTagAttributeByCode(code, attribute, defaultValue = '') {
      return this.recheckTags.find(item => item.code === code)?.[attribute] || defaultValue
    },
    handleKeydown(e) {
      if (e.code === 'Enter' && !e.shiftKey) {
        e.preventDefault()
        this.handleSubmit()
      }
    },
    handleEdit() {
      this.$emit('edit')
    },
    handleCancel() {
      this.$emit('cancel')
    },
    handleSubmit() {
      this.$emit('submit', { version: this.commentVersion })
    },
    handleRecheck() {
      this.$emit('recheck')
    },
    // 评论版本下拉框显示隐藏
    handleUpdateVisible(event) {
      if (event && !this.loaded) {
        // 获取评论版本列表
        this.pickerLoading = true
        API.getCommnetVersionList({
          request: { actionId: this.actionId, sequenceId: this.sequenceId },
        })
          .then(res => {
            this.loaded = true
            if (res?.versionList?.length) {
              this.commentVersionList = res.versionList.map(item => ({
                label: `V${item}`,
                value: item,
              }))
              this.commentVersion = this.commentVersionList[0]?.value
              this.$set(this.commentVersionMap, this.commentVersion, this.comment)
            }
          })
          .catch(errorReportOnly)
          .finally(() => {
            this.pickerLoading = false
          })
      }
    },
    // 评论版本切换
    handleCommentVersionChange(val) {
      API.getSpecifyVersionComment({
        request: {
          actionId: this.actionId,
          sequenceId: this.sequenceId,
          commentVersion: val,
        },
      })
        .then(res => {
          if (res?.comment) {
            const { comment } = res
            this.$set(this.commentVersionMap, val, comment)
            const commentInfo = {
              markResult: comment?.markResult,
              markComment: comment?.markComment,
              problemCategories: comment?.problemCategories,
            }
            this.$emit('update:values', commentInfo)
          }
        })
        .catch(errorReportOnly)
    },
  },
})
</script>

<style lang="scss">
.comment-cascader {
  .mtd-cascader-menu-item {
    .mtd-cascader-menu-item-checkbox {
      display: inline-block; // 默认显示复选框
    }

    //产品不希望父节点的复选框实现全选功能，因此隐藏掉父节点的复选框。
    &:has(.mtd-cascader-menu-item-expand-icon) .mtd-cascader-menu-item-checkbox {
      display: none;
    }
  }
}
</style>
<style lang="scss" scoped>
.comment-picker {
  width: 78%;

  .comment-content {
    padding: 8px 12px;
    background: #fff;

    .title {
      font-weight: 500;
    }

    .scoring {
      margin: 12px 0 8px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: #666;
    }

    .comment-textarea {
      ::v-deep .mtd-textarea-wrapper {
        width: 100%;

        textarea.mtd-textarea {
          resize: none;
        }
      }
    }

    .btn {
      margin-top: 8px;
      display: flex;
      justify-content: flex-end;
      gap: 8px;
    }
  }
}

.recheck-comment {
  margin-top: 8px;
}
</style>
