<template>
  <div class="commented">
    <div class="header">
      <div class="header-left">
        <span>{{ title }}</span>
        <img :src="getAvatar(comment)" alt="" />
        <mtd-tooltip placement="top" :content="`${comment.reviewerName}/${comment.reviewerMisId}`">
          <span class="user-info"> {{ comment.reviewerName }}/{{ comment.reviewerMisId }} </span>
        </mtd-tooltip>
        <span class="vertical-line"></span>
        <span>{{ comment.createTime }}</span>
      </div>
      <slot name="headerRight" />
    </div>
    <div class="commented-content clearfix">
      <slot />
    </div>
  </div>
</template>

<script>
import defaultAvatar from '@/assets/<EMAIL>'
export default {
  name: 'CommentCard',
  props: {
    comment: {
      type: Object,
      default: null,
    },
    title: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      defaultAvatar,
    }
  },
  methods: {
    getAvatar(data) {
      return data?.avatar || this.defaultAvatar
    },
  },
}
</script>

<style lang="scss" scoped>
.commented {
  background-color: #fff;

  .header {
    padding: 0 12px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;

    .header-left {
      display: flex;
      align-items: center;
    }

    span {
      font-size: 12px;
      color: #999;
    }

    span:nth-child(1) {
      margin-right: 8px;
      color: #222;
      font-size: 14px;
      font-weight: 500;
    }

    img {
      margin-right: 4px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      vertical-align: middle;
      border: 0.5px solid #e8e8e8;
    }
  }

  .user-info {
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .commented-content {
    padding: 12px;

    .mark-comment {
      overflow: auto;
      margin-top: 10px;
      white-space: pre-line;
    }

    a {
      margin-left: 6px;
      color: var(--color-primary);
      float: right;
      font-size: 12px;
      text-decoration: none;
    }
  }
}

.vertical-line {
  margin: 0 4px;
  width: 1px;
  height: 12px;
  background-color: #e5e5e5;
}
</style>
