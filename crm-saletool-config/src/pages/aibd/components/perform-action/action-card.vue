<template>
  <mtd-card :id="cardId" :class="cardClass" shadow="never" @click.native="handleClick">
    <template #default>
      <mtd-tooltip :content="getFullTitle(data)" placement="top">
        <div class="history-action-content-card-title">
          <div class="history-action-content-card-title-pre">
            <span class="text-ellipsis">
              {{ getFullTitle(data) }}
            </span>
          </div>
          <mtd-tag
            v-if="!isSubAction && data.status"
            type="ghost"
            :style="
              CALL_STYLE[data.status] || {
                color: '#00A85A',
                background: 'rgba(0, 168, 90, 0.06)',
                fontWeight: 'normal',
              }
            "
            size="small"
          >
            {{ getCallType(data.status) }}
          </mtd-tag>
        </div>
      </mtd-tooltip>
      <!-- 扩展信息 -->
      <div v-for="(extend, i) in extendInfoList" :key="i" class="history-action-description">
        <label class="history-action-description-label">{{ extend.title }}：</label>
        <div class="history-action-description-value">{{ extend.content }}</div>
      </div>
    </template>
  </mtd-card>
</template>
<script>
import {
  ACTION_TYPE_LABEL_ENUMS,
  CALL_TYPE_LABEL_ENUMS,
  SUB_ACTION_LABEL_ENUMS,
  CALL_STYLE,
  SUB_ACTION_STYLE,
  ACTION_TYPE_ENUMS,
} from '../../lib/dict'
export default {
  props: {
    data: {
      type: Object,
      required: true,
    },
    isSubAction: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ACTION_TYPE_LABEL_ENUMS,
      CALL_TYPE_LABEL_ENUMS,
      SUB_ACTION_LABEL_ENUMS,
      CALL_STYLE,
      SUB_ACTION_STYLE,
      ACTION_TYPE_ENUMS,
    }
  },
  computed: {
    cardId() {
      return this.isSubAction ? `actionItem-${this.data.sequenceId}` : ''
    },
    cardClass() {
      if (this.isSubAction) {
        return {
          'history-action-content-card-child': true,
          active: this.isActive,
        }
      }
      return this.isActive ? 'history-action-content-selected-box' : 'history-action-card'
    },
    extendInfoList() {
      return this.data?.extendInfoList || []
    },
  },
  methods: {
    //卡片点击
    handleClick() {
      if (this.isSubAction) {
        this.$emit('card-click', this.data.sequenceId)
      } else {
        //isClickTrigger：判断是否为自主点击"主卡片"，做 初次进入页面和点击"主卡片"的区别。
        this.$emit('card-click', this.data.actionId, { isClickTrigger: true })
      }
    },
    //动作类型
    getActionType(actionType) {
      return ACTION_TYPE_LABEL_ENUMS[actionType] || ''
    },
    //通话类型
    getCallType(callType) {
      return CALL_TYPE_LABEL_ENUMS[callType] || ''
    },
    getFullTitle(data) {
      if (this.isSubAction) {
        return `${this.data.operationName}(${this.data.operationStartTime})`
      }
      switch (data.actionType) {
        case ACTION_TYPE_ENUMS.QW_CHAT:
          return `${this.getActionType(data.actionType)}(${data.actionStartTime})`
        case ACTION_TYPE_ENUMS.IVR:
          return `${this.getActionType(data.actionType)}(${data.actionStartTime} ${data.minutes}分${
            data.formatSeconds
          }秒)`
        default:
          return ''
      }
    },
    getSubActionResult(val) {
      return SUB_ACTION_LABEL_ENUMS[val] || ''
    },
  },
}
</script>
<style lang="scss" scoped>
.history-action-content-selected-box {
  background-color: #f0f6ff;
}

.history-action-content-card-title {
  font-size: 16px;
  font-weight: 500;
  color: #222222;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  gap: 2px;

  .history-action-content-card-title-pre {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .mtd-tag {
    flex-shrink: 0;
  }

  .text-ellipsis {
    margin-right: 4px;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.history-action-content-card-child {
  width: calc(100% - 49px) !important;
  margin: 12px 0 12px 50px;

  .history-action-description-subvalue {
    font-size: 14px;
    color: #666666;
  }

  &.active {
    border: 1px solid #166ff7 !important;
    background-color: #f0f6ff;
  }
}

.history-action-row {
  display: flex;
  align-items: flex-start;

  .history-action-card {
    &:hover {
      background-color: rgba(102, 102, 102, 0.06);
      cursor: pointer;
    }
  }
}

::v-deep {
  .mtd-card {
    border: none;
  }

  .mtd-card-body {
    padding: 8px;
  }

  .mtd-card-title {
    width: 100%;
    font-size: 14px;
    font-weight: 400;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .mtd-btn-text {
      margin-top: -3px;
    }
  }
}

.history-action-description {
  display: flex;
  overflow: hidden;
  color: #666666;
  font-size: 14px;
  margin-bottom: 4px;

  .history-action-description-label {
    text-align: left;
    white-space: nowrap;
    min-width: 50px;
  }

  .history-action-description-value {
    flex: 1;
    display: flex;
    overflow: hidden;
    text-overflow: ellipsis;
    align-items: center;
    flex-wrap: wrap;
    align-self: flex-start;
    word-break: break-all;
  }
}
</style>
