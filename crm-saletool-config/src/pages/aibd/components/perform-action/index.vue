<template>
  <ExpandSidebar
    :items="HISTORY_ACTION_ITEMS"
    toggle-icon-style="mtdicon-left-thick"
    expand-width="24%"
  >
    <template #historyAction>
      <div>
        <UniversalBox class="history-action-box">
          <template #title>历史动作</template>

          <div class="history-action-content">
            <mtd-steps direction="vertical" dot :active="formatActionInformation.length">
              <mtd-step v-for="(info, stepIndex) in formatActionInformation" :key="stepIndex">
                <template #description>
                  <div class="history-action-row">
                    <i
                      v-if="info.showOperations"
                      class="mtdicon-triangle-right arrow"
                      :class="{ 'arrow-rotate': info.expand }"
                      style="cursor: pointer"
                      @click.stop="handleExpand(stepIndex)"
                    ></i>
                    <ActionCard
                      :data="info"
                      :step-index="stepIndex"
                      :is-active="info.actionId === selectedActionId"
                      :class="{ 'card-offsets': !info.showOperations && !isCancelOffset }"
                      @card-click="handlePerformId"
                    />
                  </div>
                  <div v-if="info.expand" ref="scrollContent">
                    <ActionCard
                      v-for="(item, index) in subActInfo"
                      :key="index"
                      :data="item"
                      :is-sub-action="true"
                      :step-index="stepIndex"
                      :is-active="isActive(item, index)"
                      @card-click="handleCardClick"
                    >
                    </ActionCard>
                  </div>
                </template>
              </mtd-step>
            </mtd-steps>
          </div>
        </UniversalBox>
      </div>
    </template>
  </ExpandSidebar>
</template>
<script>
import { UniversalBox } from '@/components'
import API from '../../api'
import { errorReportOnly } from '@/lib/utils'
import ExpandSidebar from '../expand-sidebar/index.vue'
import { HISTORY_ACTION_ITEMS } from '../../lib/dict'
import ActionCard from './action-card.vue'

export default {
  components: {
    UniversalBox,
    ExpandSidebar,
    ActionCard,
  },
  props: {
    sequenceId: {
      type: Number,
      default: 0,
    },
    selectedActionId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      actionInformation: [],
      subActInfo: [],
      HISTORY_ACTION_ITEMS,
    }
  },
  computed: {
    //计算时间
    formatActionInformation() {
      return this.actionInformation.map(info => {
        let totalSeconds = 0

        if (!info.actionSeconds) {
          return {
            ...info,
            minutes: 0,
            formatSeconds: 0,
            expand: info.expand || false,
            descriptions: Object.fromEntries(info.descriptions || []),
          }
        } else {
          totalSeconds = parseInt(info.actionSeconds, 10)
          const minutes = Math.floor(totalSeconds / 60)
          const formatSeconds = totalSeconds % 60
          return {
            ...info,
            minutes,
            formatSeconds,
            expand: info.expand || false,
            descriptions: Object.fromEntries(info.descriptions || []),
          }
        }
      })
    },
    //用于控制卡片偏移。如果所有的动作都没有子卡片，则取消每个子卡片的偏移。
    isCancelOffset() {
      return this.formatActionInformation.every(item => !item.showOperations)
    },
  },
  watch: {
    sequenceId() {
      this.scrollToActiveCard(`#actionItem-${this.sequenceId}`)
    },
  },
  created() {
    this.getPerformActionData()
  },
  methods: {
    // 获得历史动作信息
    getPerformActionData() {
      API.getPerformActionInfo({ taskId: this.$route.query.taskId })
        .then(res => {
          const data = res.data || {}
          this.actionInformation = data.map(it => ({
            ...it,
            descriptions: new Map([
              ['动作ID：', it.actionId],
              ['联系人：', it.contactPhone],
              ['沟通摘要：', it.summary],
              ['执行结果：', it.actionResult],
              ['挂断原因：', it.hangUpReason],
            ]),
          }))

          if (this.actionInformation.length > 0) {
            let targetActionCard
            //如果url有默认高亮的卡片，targetActionCard为该url参数对应卡片。否则默认高亮第一个卡片。
            if (this.selectedActionId) {
              targetActionCard = this.actionInformation.find(
                item => item.actionId === Number(this.selectedActionId),
              )
            } else {
              targetActionCard = this.actionInformation[0]
            }

            if (targetActionCard) {
              this.$set(targetActionCard, 'expand', true)
              this.handlePerformId(targetActionCard.actionId)
              //定位
              this.scrollToActiveCard('.history-action-content-selected-box')
              this.$emit('update:selected-action-id', targetActionCard.actionId)
            }
          }
        })
        .catch(errorReportOnly)
    },
    //获取历史动作子信息
    getSubPerformActionData(actionId) {
      API.getDialogRecord({ actionId })
        .then(res => {
          const data = res.data?.actionOperationList || []
          this.subActInfo = data
        })
        .catch(errorReportOnly)
    },
    //“主卡片”高亮的方式：1、初次进入页面时，url参数上携带对应卡片的 actionId。2、自主选择“主卡片”高亮。
    //举例：url参数：https：//.....?actionId=第一个卡片id&sequenceId=第二句话；
    //   (1)当初次进入页面时isClicktrigger默认值的false，卡片 & sequenceId正常高亮。
    //   (2)自主切换时，isClicktrigger为true，卡片高亮切换 & sequenceId高亮值null（保证切换时不保留上次的高亮语句）
    handlePerformId(actionId, { isClickTrigger = false } = {}) {
      this.actionInformation.forEach(item => {
        if (item.actionId !== actionId) {
          this.$set(item, 'expand', false)
        }
      })

      const targetActionCard = this.actionInformation.find(item => item.actionId === actionId)
      if (targetActionCard) {
        this.$set(targetActionCard, 'expand', true)
      }
      this.$emit('update:selected-action-id', actionId)

      //点击时sequenceId置为null
      if (isClickTrigger) {
        this.$emit('update:sequenceId', null)
      }
      //子卡片信息
      this.subActInfo = []
      this.getSubPerformActionData(actionId)
    },
    handleExpand(index) {
      // 关闭所有卡片的展开状态
      this.actionInformation.forEach((item, i) => {
        if (i !== index) {
          this.$set(item, 'expand', false)
        }
      })

      const isExpanded = this.actionInformation[index].expand
      this.$set(this.actionInformation[index], 'expand', !isExpanded)

      if (!isExpanded) {
        this.handlePerformId(this.actionInformation[index].actionId, index, {
          isClickTrigger: true,
        })
      }
    },

    //子卡片选中
    handleCardClick(sequenceId) {
      this.$emit('update:sequenceId', sequenceId)
    },
    isActive(item, index) {
      if (item.sequenceId !== null) {
        return item.sequenceId === this.sequenceId
      }
    },
    //卡片定位
    scrollToActiveCard(selector) {
      this.$nextTick(() => {
        const element = this.$el.querySelector(selector)
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.history-action-box {
  text-align: left;
  max-height: calc(100vh - 115px);
  overflow: hidden;

  .history-action-content {
    margin-top: 30px;
    overflow: auto;
    height: 100%;

    .history-action-row {
      display: flex;
      align-items: flex-start;

      .arrow {
        height: 22px;
        font-size: 20px;
        transition: transform 0.2s ease-in-out;
        color: rgba(0, 0, 0, 0.5);
        margin-right: 8px;

        &.arrow-rotate {
          transform: rotate(90deg);
        }
      }
    }
  }

  &.universal-box {
    padding: 24px 4px 0 16px;
  }

  ::v-deep {
    .universal-header {
      height: 21px;
    }
    .mtd-step-finish-next .mtd-step-head-line-last {
      background-color: rgba(0, 0, 0, 0.1);
    }

    .mtd-steps-vertical .mtd-step-dot .mtd-step-head {
      margin-top: 14px;
    }

    .mtdicon-triangle-right {
      margin-top: 6px;
    }

    .mtd-step-main {
      width: 90%;
      margin-bottom: 13px;
      padding: 5px 5px 0;
    }

    .mtd-card {
      width: calc(100% - 28px);
      flex-shrink: 1;
      border: none;
    }

    .mtd-card-title {
      width: 100%;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .mtd-btn-text {
        margin-top: -3px;
      }
    }
  }
}
.card-offsets {
  margin-left: 28px;
}
</style>
