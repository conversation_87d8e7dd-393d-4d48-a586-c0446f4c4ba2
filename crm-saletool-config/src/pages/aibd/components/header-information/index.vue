<template>
  <div class="container">
    <div class="left-info">
      <!-- 门店信息 -->

      <UniversalBox>
        <template #title>
          <label>{{ headInformation.dpShopName }}（{{ headInformation.dpShopId }}）</label>
        </template>
      </UniversalBox>

      <label class="label-info"
        >{{ headInformation.scene }} · {{ headInformation.taskBatchName }} · 任务ID：{{
          headInformation.taskId
        }}</label
      >
    </div>

    <!-- 下载通话质检明细 -->
    <div class="download">
      <mtd-button type="text" ghost class="downloadButton" @click="downloadDetailed">
        <mtd-icon name="mtdicon-download-o" />
        下载通话质检明细
      </mtd-button>
    </div>
  </div>
</template>
<script>
import API from '../../api'
import { errorReportOnly } from '@/lib/utils'
import { UniversalBox } from '@/components'

export default {
  name: 'HeadInformation',
  components: { UniversalBox },
  props: {
    lx: {
      type: Object,
      default: () => ({}),
    },
    actionId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      headInformation: {},
    }
  },
  created() {
    this.getHeaderData()
  },
  methods: {
    //获得基础信息
    getHeaderData() {
      API.getInspectionBaseInfo({ taskId: this.$route.query.taskId })
        .then(res => {
          const data = res.data || {}
          this.headInformation = data
          this.$emit('update:task-batch-id', data?.taskBatchId || null)
        })
        .catch(errorReportOnly)
    },
    // 下载通话质检明细
    downloadDetailed() {
      this.lx.mc('b_gc_m_1b5feb80_mc')
      API.downloadCallQualityInspection({ actionId: this.actionId })
        .then(res => this.$mtd.message.success(res.message || '下载成功'))
        .catch(errorReportOnly)
    },
  },
}
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 24px;
  border-bottom: 1px solid rgba(17, 25, 37, 0.05);

  .left-info {
    display: flex;
    .label-info {
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 21px;
      vertical-align: center;
    }
  }
  .download {
    margin: 18px 8px 0 0;
    .downloadButton {
      color: #166ff7;
    }
  }
}
</style>
