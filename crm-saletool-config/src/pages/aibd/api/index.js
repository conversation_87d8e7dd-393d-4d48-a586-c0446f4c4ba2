import request from '../lib/request'

export default {
  // 获取当前登陆人信息
  queryLoginUserInfo() {
    return request('/wangxiaohui/gateway/crm-aibd/common/queryLoginUserInfo', {
      method: 'get',
    })
  },

  // 获取会话基础信息
  getActionBaseInfo(query) {
    return request('/wangxiaohui/gateway/crm-aibd/Inspection/getActionBaseInfoV2', {
      method: 'get',
      params: query,
    })
  },
  //获得顶部基础信息
  getInspectionBaseInfo(query) {
    return request('/wangxiaohui/gateway/crm-aibd/Inspection/getInspectionBaseInfo', {
      method: 'get',
      params: query,
    })
  },
  //获得历史执行动作信息
  getPerformActionInfo(query) {
    return request('/wangxiaohui/gateway/crm-aibd/Inspection/actionList', {
      method: 'get',
      params: query,
    })
  },

  // 获取通话记录（包含动作操作明细）
  getDialogRecord(query) {
    return request('/wangxiaohui/gateway/crm-aibd/Inspection/getDialogRecord', {
      method: 'get',
      params: query,
    })
  },

  // 获取质检结果
  getInspectionResult(query) {
    return request('/wangxiaohui/gateway/crm-aibd/Inspection/getInspectionResultV2', {
      method: 'get',
      params: query,
    })
  },

  // 保存评论
  submitDialogComment(data) {
    return request('/wangxiaohui/gateway/crm-aibd/Inspection/submitDialogComment', {
      method: 'post',
      data,
    })
  },

  // 提交质检结果
  submitInspectionResult(data) {
    return request('/wangxiaohui/gateway/crm-aibd/Inspection/submitInspectionResult', {
      method: 'post',
      data,
    })
  },

  // 下载通话质检明细
  downloadCallQualityInspection(query) {
    return request('/wangxiaohui/gateway/crm-aibd/Inspection/download', {
      method: 'get',
      params: query,
    })
  },
  //获取引用信息
  getReferenceInfo(query) {
    return request('/wangxiaohui/gateway/crm-aibd/Inspection/getReferenceInfo', {
      method: 'get',
      params: query,
    })
  },

  // 获取评论版本列表
  getCommnetVersionList(data) {
    return request('/wangxiaohui/gateway/crm-aibd/Inspection/getCommentVersionList', {
      method: 'post',
      data,
    })
  },

  // 根据版本号获取评论
  getSpecifyVersionComment(data) {
    return request('/wangxiaohui/gateway/crm-aibd/Inspection/getSpecifyVersionComment', {
      method: 'post',
      data,
    })
  },

  // 根据任务批次id+任务id 获取是否可抽检
  hasSpotCheckAuth(query) {
    return request('/platform/annotation/hasSpotCheckAuth', {
      method: 'get',
      params: query,
    })
  },
}
