// 埋点
export const APP_SOURCE = {
  AIBD: 0, // AIBD
  QUALITY: 1, // 销售质检
}

// 标签颜色
export const TAG_COLOR = {
  1: 'green', // 准确
  2: 'yellow', // 一般
  3: 'red', // 错误
}

// 复检标签颜色
export const RECHECK_TAG_COLOR = {
  1: 'green',
  2: 'red',
}

// 违规红线 标签颜色
export const ILLEGAL_TAG_COLOR = {
  1: 'red', // 命中
  2: 'gray', // 未命中
}

// 整体结果样式
export const OVERALL_STYLE = {
  //优秀
  0: {
    color: '#00A85A',
    background: 'rgba(0, 168, 90, 0.06)',
  },
  // 准确
  1: {
    color: '#00A85A',
    background: 'rgba(0, 168, 90, 0.06)',
  },
  // 一般
  2: {
    color: '#A36A00',
    background: 'rgba(163, 106, 0, 0.06)',
  },
  // 错误
  3: {
    color: '#FF2727',
    background: 'rgba(255, 39, 39, 0.06)',
  },
  // 暂无结果
  '': {
    color: '#999999',
    background: 'rgba(102, 102, 102, 0.06)',
  },
}

// 角色
export const ROLE = {
  SALE: 0, // 销售
  MERCHANT: 1, // 商户
}

//历史执行动作操作类型
export const ACTION_TYPE_ENUMS = {
  IVR: 'ivr', //ivr外呼
  QW_CHAT: 'qwChat', //企微会话
}
export const ACTION_TYPE_LABEL_ENUMS = {
  [ACTION_TYPE_ENUMS.IVR]: 'IVR通话',
  [ACTION_TYPE_ENUMS.QW_CHAT]: '企微会话',
}

//历史动作通话类型
export const CALL_TYPE_ENUMS = {
  CONNECT: 1, //接通
  UNCONNECT: 2, //挂断
}
export const CALL_TYPE_LABEL_ENUMS = {
  [CALL_TYPE_ENUMS.CONNECT]: '接通',
  [CALL_TYPE_ENUMS.UNCONNECT]: '未接通',
}
export const CALL_STYLE = {
  1: {
    color: '#00A85A',
    background: 'rgba(0, 168, 90, 0.06)',
    border: '1px solid rgba(0, 168, 90, 0.1)',
    fontWeight: '400',
  },
  2: {
    color: '#FF2727',
    background: 'rgba(255, 39, 39, 0.06)',
    border: '1px solid rgba(255, 39, 39, 0.1)',
    fontWeight: '400',
  },
}
//子内容状态
export const SUB_ACTION_ENUMS = {
  FINISH: 1, //已完成
  UNFINISH: 2, //未完成
}
export const SUB_ACTION_LABEL_ENUMS = {
  [SUB_ACTION_ENUMS.FINISH]: '已完成',
  [SUB_ACTION_ENUMS.UNFINISH]: '未完成',
}
export const SUB_ACTION_STYLE = {
  1: {
    color: '#00A85A',
  },
  2: {
    color: '#FF2727',
  },
}

//质检标签页
export const QUALITY_INSPECTION_SIDEBAR_ITEMS = [
  { label: '质检结果', value: 'qualityInspectionResult', icon: 'mtdicon-schedule' },
  { label: '会话信息', value: 'sessionInformation', icon: 'mtdicon-page-o' },
  { label: '引用信息', value: 'referenceInformation', icon: 'mtdicon-info-circle-o' },
]
//历史动作页
export const HISTORY_ACTION_ITEMS = [
  { label: '历史动作', value: 'historyAction', icon: 'mtdicon-history' },
]
//会话信息展示类型枚举
export const SESSION_RULES_ENUMS = {
  CONTENT_COPY: 1, //复制
  PHONE_NUM_VISIBLE: 2, //显示
}

//通话消息类型
export const CALL_INFO_TYPE_ENUMS = {
  TEXT: 1, //文本
  PHOTO: 2, //图片
  VIDEO: 3, //视频
  VOICE: 4, //语音
}
