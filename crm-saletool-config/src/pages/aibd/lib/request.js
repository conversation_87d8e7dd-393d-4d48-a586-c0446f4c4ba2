import axios from 'axios'
import { Message } from '@ss/mtd-vue2'

const config = {
  baseURL: process.env.VUE_APP_API_HOST_XIAOZHI || '',
  timeout: Number(process.env.VUE_APP_HTTP_TIMEOUT),
  withCredentials: true,
}

const instance = axios.create(config)

instance.interceptors.response.use(
  response => {
    const { data } = response || {}
    if (data?.code !== 200) {
      Message({
        type: 'error',
        message: data?.message || '系统异常，请稍后重试',
      })
      return Promise.reject(data)
    }
    return data
  },
  error => {
    Message({
      type: 'error',
      message: error.message || '系统异常，请稍后重试',
    })
    return Promise.reject(error)
  },
)

export default instance
