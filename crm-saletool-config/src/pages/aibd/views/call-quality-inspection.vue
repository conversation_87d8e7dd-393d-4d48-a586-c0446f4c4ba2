<template>
  <div class="call-quality-inspection-container">
    <HeaderInformation
      :task-batch-id.sync="taskBatchId"
      :basic-information="basicInformation"
      :lx="lx"
      :action-id="selectedId"
    />
    <div class="bottom-content">
      <PerformAction :selected-action-id.sync="selectedId" :sequence-id.sync="sequenceId" />
      <CallRecord
        :key="selectedId"
        :action-id="selectedId"
        :basic-information="basicInformation"
        :lx="lx"
        :sequence-id.sync="sequenceId"
        :task-batch-id="taskBatchId"
      />

      <QualityInspectionResults
        :action-id="selectedId"
        :basic-information="basicInformation"
        :lx="lx"
      />
    </div>
  </div>
</template>

<script>
import { LXUtils } from '@/lib/lx'
import API from '../api'
import {
  HeaderInformation,
  CallRecord,
  QualityInspectionResults,
  PerformAction,
} from '../components'
import { valLabGeneratorMap } from '../lib/lx'
import { errorReportOnly } from '@/lib/utils'

export default {
  name: 'CallQualityInspection',
  components: {
    HeaderInformation,
    CallRecord,
    QualityInspectionResults,
    PerformAction,
  },
  data() {
    return {
      basicInformation: {},
      lx: null,
      taskBatchId: null,
      selectedId: this.$route.query.actionId ? Number(this.$route.query.actionId) : null,
      actionId: null,
      actionData: null,
      sequenceId: this.$route.query.sequenceId ? Number(this.$route.query.sequenceId) : null,
    }
  },
  watch: {
    selectedId: {
      immediate: true,
      handler(val) {
        if (val) this.getData()
      },
    },
  },
  created() {
    this.lx = new LXUtils('gc_m', {
      cid: 'c_gc_m_ybsld2mi',
      appnm: 'dp_apollo_pc',
      valLabGenerator: valLabGeneratorMap.misIdAndTenantId(),
    })
  },
  methods: {
    getData() {
      API.getActionBaseInfo({ actionId: this.selectedId })
        .then(res => {
          const data = res.data || {}
          this.basicInformation = data
        })
        .catch(errorReportOnly)
    },
  },
}
</script>

<style lang="scss" scoped>
.call-quality-inspection-container {
  height: 100%;
  padding-bottom: 16px;
  display: flex;
  flex-direction: column;

  .bottom-content {
    flex: 1;
    display: flex;
  }
}
</style>
