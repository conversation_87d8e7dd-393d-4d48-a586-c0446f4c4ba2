<template>
  <basic-page title="新建自动补贴" class="contaienr">
    <form-provider :form="createMatchAutomationForm">
      <match-automation-form />
    </form-provider>
    <template #bottom>
      <div class="button-group">
        <mtd-button @click="cancelHandler">取消</mtd-button>
        <mtd-button type="primary" :disabled="submitting" @click="submitHandler"
          >提交确认</mtd-button
        >
      </div>
    </template>
  </basic-page>
</template>

<script>
import { Message } from '@ss/mtd-vue2'
import { FormProvider } from '@nibfe/dz-form-render'
import { cloneDeep } from 'lodash'
import psb from '@nibfe/platform-sdk'

import { BasicPage } from '@/components'
import { errorHandlerGenerator, reportError } from '@/lib/utils'

import {
  MatchAutomationForm,
  createMatchAutomationForm,
  packageCreateSubmitValues,
  changeCitiesValue,
} from '../components'

import { saveActivity } from '../api'

const createFailedErrorHandler = errorHandlerGenerator('创建失败')

export default {
  components: {
    FormProvider,
    MatchAutomationForm,
    BasicPage,
  },
  data() {
    return {
      createMatchAutomationForm: createMatchAutomationForm.Create(),
      submitting: false,
    }
  },
  mounted() {
    psb.config('1701415292kvxeyv', {
      masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
    })
  },
  methods: {
    submitHandler() {
      this.submitting = true

      this.createMatchAutomationForm
        .validate()
        .then(async () => {
          let cityStr = ''
          const values = cloneDeep(this.createMatchAutomationForm.values)
          if (values.cityIds) {
            try {
              await changeCitiesValue(values.cityIds, 'mtName').then(res => {
                if (res.resultCode === 0) {
                  const list = []
                  res.cityList.map(item => {
                    list.push(item.cityId)
                  })
                  cityStr = list.join()
                }
              })
            } catch (e) {
              reportError({
                error: '请求异常',
                content: {
                  response: e,
                },
              })
            }
          }
          // 判断是否填写完成
          const autoPriceRule = values?.automationSubsidyAmount?.autoPriceRule || []
          autoPriceRule.forEach(item => {
            const hasMinMax = obj => obj?.hasOwnProperty('min') && obj?.hasOwnProperty('max')
            const lacksMinMax = obj => !obj?.hasOwnProperty('min') && !obj?.hasOwnProperty('max')
            const isValid = obj => hasMinMax(obj) || lacksMinMax(obj)

            if (isValid(item.scale) && isValid(item.swingUser) && isValid(item.supplier)) {
            } else {
              throw new TypeError('规则必须填全最大值最小值')
            }
          })
          const packagedValues = packageCreateSubmitValues(values, cityStr)

          return saveActivity(packagedValues)
        })
        .then(() => {
          Message({
            type: 'success',
            message: '创建成功，即将返回列表页',
          })

          setTimeout(() => {
            this.$router.back()
            this.submitting = false
          }, 500)
        })
        .catch(error => {
          if (error instanceof Error) {
            /* 其他 Error 错误提示 */
            createFailedErrorHandler(error)
          } else {
            // eslint-disable-next-line no-console
            console.error('[表单校验失败]', error)
          }

          this.submitting = false
        })
    },
    cancelHandler() {
      this.$router.back()
    },
  },
}
</script>

<style lang="less" scoped>
.container {
  .match-automation-form {
    height: 100%;
    padding-top: 6px;
  }
}

.button-group {
  display: flex;
  gap: 12px;
  margin-left: auto;
}
</style>
