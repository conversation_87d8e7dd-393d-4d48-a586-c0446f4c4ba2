<template>
  <div id="entertainment-container" class="entertainment-container">
    <div class="config-module">
      <div id="list"></div>
    </div>
  </div>
</template>
<script>
import graphCfg from '@nibfe/ccc-lowcode-render'
import psb from '@nibfe/platform-sdk'
export default {
  components: {},
  data() {
    return {}
  },
  created() {
    graphCfg({
      el: '#list',
      tenantId: 3,
      pageType: 'chart', // 图表类型
      remoteURL: process.env.VUE_APP_API_HOST,
      appEnv: process.env.VUE_APP_ENV,
      module: 'autoSubsidy-list',
      templateId: process.env.VUE_APP_ENV === 'production' ? 198 : 2146,
      modelId: process.env.VUE_APP_ENV === 'production' ? 291 : 1511,
      initParams: {
        terminal: 0, //来自于PC，必填
        planSource: 0, //非来自拜访创建的，建议填0
      },
    }).then(params => {
      const { instance, eventBus } = params || {}
      this.$bus = eventBus

      eventBus.$on('toOut', res => {
        this.pageMessage = res
      })
    })
  },
  mounted() {
    psb.config('1701415213s4vrj3', {
      masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
    })
  },
}
</script>
