<template>
  <basic-page :title="title" class="contaienr">
    <section v-show="innerMode === 'edit'" class="match-automation-form edit">
      <form-provider :form="editMatchAutomationForm">
        <match-automation-form />
      </form-provider>
    </section>
    <section v-show="innerMode === 'preview'" class="match-automation-form preview">
      <form-provider :form="previewMatchAutomationForm">
        <match-automation-form />
      </form-provider>
    </section>

    <template #bottom>
      <div class="button-group">
        <mtd-button @click="cancelHandler">{{
          innerMode === 'preview' ? '返回' : '取消'
        }}</mtd-button>
        <mtd-button
          v-if="innerMode === 'edit'"
          type="primary"
          :disabled="submitting"
          @click="updateHandler"
          >保存</mtd-button
        >
      </div>
    </template>
  </basic-page>
</template>

<script>
import { Message } from '@ss/mtd-vue2'
import { FormProvider } from '@nibfe/dz-form-render'
import { cloneDeep } from 'lodash'
import psb from '@nibfe/platform-sdk'

import { BasicPage } from '@/components'
import {errorHandlerGenerator, reportError} from '@/lib/utils'

import {
  MatchAutomationForm,
  createMatchAutomationForm,
  parseInputValues,
  packageUpdateSubmitValues,
  changeCitiesValue
} from '../components'

import { queryActivity, updateActivity } from '../api'

const queryFailedErrorHandler = errorHandlerGenerator('查询失败')
const updateFailedErrorHandler = errorHandlerGenerator('更新失败')

export default {
  components: {
    FormProvider,
    MatchAutomationForm,
    BasicPage,
  },
  data() {
    return {
      previewMatchAutomationForm: createMatchAutomationForm.Preview(),
      editMatchAutomationForm: createMatchAutomationForm.Edit(),

      activityData: null,
      activityId: null,
      /* 运行时实际指定的表单模式 */
      innerMode: null,
      /* 通过 query 参数指定的表单模式 */
      targetMode: null,

      submitting: false,
    }
  },
  computed: {
    title() {
      return this.innerMode === 'edit'
        ? '编辑自动补贴'
        : this.innerMode === 'preview'
        ? '查看自动补贴'
        : ''
    },
  },
  watch: {
    // hash route 变更时主动触发 data 变更，主要是便于本地调试
    $route: {
      handler(to) {
        this.activityId = to?.query?.activityId ?? null
        this.targetMode = to?.query?.mode ?? null

        this.refreshDetail()
      },
      immediate: true,
    },
  },
  mounted() {
    psb.config('17014152257gizfi', {
      masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
    })
  },
  methods: {
    updateHandler() {
      this.submitting = true

      this.editMatchAutomationForm
        .validate()
        .then(() => {
          const values = cloneDeep(this.editMatchAutomationForm.values)
          const packagedValues = packageUpdateSubmitValues(values, {
            activityId: this.activityId,
          })

          return updateActivity(packagedValues)
        })
        .then(() => {
          Message({
            type: 'success',
            message: '更新成功，即将返回列表页',
          })

          setTimeout(() => {
            this.$router.back()
            this.submitting = false
          }, 500)
        })
        .catch(error => {
          if (error instanceof Error) {
            /* 其他 Error 错误提示 */
            updateFailedErrorHandler(error)
          } else {
            // eslint-disable-next-line no-console
            console.error('[表单校验失败]', error)
          }

          this.submitting = false
        })
    },
    cancelHandler() {
      this.$router.back()
    },
    async refreshDetail() {
      try {
        const activity = await queryActivity({
          activityIds: [this.activityId],
          departmentTypeEnumSet: ['DAOZONG'],
          applyToolTypeEnum: 'PDA',
        })
        this.activityData = activity
        const activityStatus = activity.activityBaseInfoModel.activityStatus

        // 编辑模式: 活动未生效 or 进行中状态 允许编辑部分字段
        if ((activityStatus === 1 || activityStatus === 2) && this.targetMode === 'edit') {
          this.innerMode = 'edit'

          const values = parseInputValues(activity)

          let cityStr = ''
          if (values.cityIds) {
            try {
              await changeCitiesValue(values.cityIds, "mtId").then(res => {
                if (res.resultCode === 0) {
                  const list = []
                  res.cityList.map(item => {
                    list.push(item.cityName)
                  })
                  cityStr = list.join('，')
                }
              })
            } catch (e) {}
          }
          values.cityIds = cityStr
          this.editMatchAutomationForm.reset()
          this.editMatchAutomationForm.setValues(values)

          const listLength = values.costControl.specificCustomerBudgetList?.length || 0
          const priceRuleLength = values?.automationSubsidyAmount?.autoPriceRule?.length || 0

          // 以下结果为 true，即 FormPath 无法准确定位数组项中的子节点，所以只能写成循环的形式了
          // FormPath.parse('costControl.specificCustomerBudgetList.*[0:1].delete').match('costControl.specificCustomerBudgetList')
          for (let i = 0; i < listLength; ++i) {
            this.editMatchAutomationForm.setFieldState(
              `costControl.specificCustomerBudgetList.${i}.delete`,
              { display: 'hidden' },
            )
          }
          for (let i = 0; i < priceRuleLength; ++i) {
            setTimeout(() => {
              this.editMatchAutomationForm.setFieldState(
                `automationSubsidyAmount.autoPriceRule.${i}.deleteList`,
                { display: 'hidden' }
              )
            }, 2000)
          }

          this.editMatchAutomationForm.setFieldState(
            `costControl.specificCustomerBudgetList.*[${listLength}:].customerId`,
            { pattern: 'editable' },
          )
          // 编辑模式下添加规则出价不显示
          this.editMatchAutomationForm.setFieldState(
            `automationSubsidyAmount.autoPriceRule.createList`,
            { display: 'hidden' }
          )

          // 「进行中」状态，预算只能加不能减
          if (activityStatus === 2) {
            this.editMatchAutomationForm.setFieldState(`costControl.fullBudget`, field => {
              field.setComponentProps({
                min: field.value ?? 0,
              })
            })

            this.editMatchAutomationForm.setFieldState(
              `costControl.singleCustomerBudget`,
              field => {
                field.setComponentProps({
                  min: field.value ?? 0,
                })
              },
            )

            for (let i = 0; i < listLength; ++i) {
              this.editMatchAutomationForm.setFieldState(
                `costControl.specificCustomerBudgetList.${i}.budgetUpperLimit`,
                field => {
                  field.setComponentProps({
                    min: field.value ?? 0,
                  })
                },
              )
            }
          }
        }
        // 预览模式
        else {
          this.innerMode = 'preview'

          const values = parseInputValues(activity)
          let cityStr = ''
          if (values.cityIds) {
            try {
              await changeCitiesValue(values.cityIds, "mtId").then(res => {
                if (res.resultCode === 0) {
                  const list = []
                  res.cityList.map(item => {
                    list.push(item.cityName)
                  })
                  cityStr = list.join('，')
                }
              })
            } catch (e) {
              reportError({
                error: '请求异常',
                content: {
                  response: e,
                },
              })
            }
          }
          values.cityIds = cityStr
          // await this.previewMatchAutomationForm.reset()
          this.previewMatchAutomationForm.setValues(values)

          const listLength = values.costControl.specificCustomerBudgetList?.length || 0
          const priceRuleLength = values?.automationSubsidyAmount?.autoPriceRule?.length || 0

          for (let i = 0; i < listLength; ++i) {
            this.previewMatchAutomationForm.setFieldState(
              `costControl.specificCustomerBudgetList.${i}.delete`,
              {
                display: 'none',
              },
            )
          }

          for (let i = 0; i < priceRuleLength; ++i) {
            this.previewMatchAutomationForm.setFieldState(
              `automationSubsidyAmount.autoPriceRule.${i}.deleteList`,
              { display: 'hidden' }
            )
          }
          // 预览模式下添加规则出价不显示
          this.previewMatchAutomationForm.setFieldState(
            `automationSubsidyAmount.autoPriceRule.createList`,
            { display: 'hidden' }
          )


        }
      } catch (error) {
        queryFailedErrorHandler(error)
      }
    },
  },
}
</script>

<style lang="less" scoped>
.container {
  .match-automation-form {
    height: 100%;
    padding-top: 6px;
  }
}

.button-group {
  display: flex;
  gap: 12px;
  margin-left: auto;
}
</style>
