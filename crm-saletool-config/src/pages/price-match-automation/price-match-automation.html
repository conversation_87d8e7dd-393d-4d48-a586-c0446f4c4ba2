<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <script src="https://appsec-mobile.meituan.com/h5guard/H5guard.js"></script>
    <script>
      'use strict'
      !(function (u, d) {
        var t = 'owl',
          e = '_Owl_',
          n = 'Owl',
          r = 'start',
          c = 'error',
          p = 'on' + c,
          f = u[p],
          h = 'addEventListener',
          l = 'attachEvent',
          v = 'isReady',
          b = 'dataSet'
        ;(u[t] =
          u[t] ||
          function () {
            try {
              u[t].q = u[t].q || []
              var e = [].slice.call(arguments)
              e[0] === r ? (u[n] && u[n][r] ? u[n][r](e[1]) : u[t].q.unshift(e)) : u[t].q.push(e)
            } catch (e) {}
          }),
          (u[e] = u[e] || {
            preTasks: [],
            pageData: [],
            use: function (e, t) {
              this[v] ? u[n][e](t) : this.preTasks.push({ api: e, data: [t] })
            },
            run: function (t) {
              if (!(t = this).runned) {
                ;(t.runned = !0),
                  (t[b] = []),
                  (u[p] = function () {
                    t[v] || t[b].push({ type: 'jsError', data: arguments }), f && f.apply(u, arguments)
                  }),
                  u[h] &&
                    u[h]('unhandledrejection', function (e) {
                      t[v] || t[b].push({ type: 'jsError', data: [e] })
                    })
                var e = function (e) {
                  !t[v] && e && t[b].push({ type: 'resError', data: [e] })
                }
                u[h] ? u[h](c, e, !0) : u[l] && u[l](p, e)
                var n = 'MutationObserver',
                  r = u[n] || u['WebKit' + n] || u['Moz' + n],
                  a = u.performance || u.WebKitPerformance,
                  s = 'disableMutaObserver'
                if (r && a && a.now)
                  try {
                    var i = -1,
                      o = u.navigator.userAgent
                    ;-1 < o.indexOf('compatible') && -1 < o.indexOf('MSIE')
                      ? (new RegExp('MSIE (\\d+\\.\\d+);').test(o), (i = parseFloat(RegExp.$1)))
                      : -1 < o.indexOf('Trident') && -1 < o.indexOf('rv:11.0') && (i = 11),
                      -1 !== i && i <= 11
                        ? (t[s] = !0)
                        : (t.observer = new r(function (e) {
                            t.pageData.push({ mutations: e, startTime: a.now() })
                          })).observe(d, { childList: !0, subtree: !0 })
                  } catch (e) {}
                else t[s] = !0
              }
            },
          }),
          u[e].runned || u[e].run()
      })(window, document)
    </script>
    <!-- 非国际化版本，请引入如下 DNS 配置 -->
    <link rel="dns-prefetch" href="//lx1.meituan.net" />
    <link rel="dns-prefetch" href="//lx2.meituan.net" />
    <link rel="dns-prefetch" href="//plx.meituan.com" />
    <script>
      // 在 head 标签内，其他静态资源之前，以 script 内联方式引入以下的 JS 内容（以下这部分不能动）
      !(function (win, doc, ns) {
        win['_MeiTuanALogObject'] = ns
        if (!win[ns]) {
          var _LX = function () {
            var t = function () {
              var inst = function () {
                inst.q.push([arguments, +new Date()])
              }
              inst.q = []
              t.q.push([arguments, inst])
              return inst
            }
            t.q = []
            t.t = +new Date()
            _LX.q.push([arguments, t])
            return t
          }
          _LX.q = _LX.q || []
          _LX.l = +new Date()
          win[ns] = _LX
        }
      })(window, document, 'LXAnalytics')
      // 灵犀种子代码结束（以上这部分不能动）
    </script>

    <!-- 非国际化版本，请在 body 标签内，其它业务 JS 之前引入如下静态资源 -->
    <script src="//lx.meituan.net/lx.5.min.js" type="text/javascript" async></script>
  </head>

  <body>
    <noscript>
      <strong
        >We're sorry but crm-saletool-config doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong
      >
    </noscript>
    <script crossorigin="anonymous" src="//www.dpfile.com/app/owl/static/owl_1.10.1.js"></script>
    <script>
      /* eslint-disable no-undef */
      owl &&
        owl('start', {
          project: 'com.sankuai.gcfe.crm.saletoolconfig',
          devMode: <%= VUE_APP_ENV === "development" %>,
          webVersion: window._AWP_DEPLOY_VERSION,
        })
    </script>

    <!-- 此处引入的所有 CDN 资源全部服务于低代码框架，非低代码框架页面请严格使用 NPM 依赖，不要调用全局导入的任何变量 -->
    <script src="https://s3plus.meituan.net/base-cdn/vue-2.6.14.min.js"></script>
    <script src="https://s3.meituan.net/static-prod01/com.sankuai.ddematerialsvue.deploy-files/<EMAIL>"></script>
    <script src="https://s3.meituan.net/static-prod01/com.sankuai.dde.lowcode.engine-files/<EMAIL>"></script>
    <script src="https://s3.meituan.net/static-prod01/com.sankuai.dde.lowcode.engine-files/formily.umd.js"></script>
    <script src="https://s3.meituan.net/mnpm-cdn/@ss-mtd-vue2-1.2.15/index.js"></script>
    <script src="https://s3.meituan.net/static-prod01/com.sankuai.mtdv.charts.static-files/<EMAIL>"></script>
    <script src="https://s3.meituan.net/static-prod01/com.sankuai.mtdv.charts.static-files/<EMAIL>"></script>
    <script src="https://s3.meituan.net/mnpm-cdn/@ss-mtdv-charts-1.0.8-beta.6/dist/index.umd.js"></script>
    <script src="https://s3.meituan.net/static-prod01/com.sankuai.dde.lowcode.engine-files/DzFormMtd-0.3.9-test.8.js"></script>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
