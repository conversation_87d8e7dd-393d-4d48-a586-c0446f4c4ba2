import { request } from '../lib'

export function saveActivity(params) {
  return request.post('/meishi-gateway/mkt/activity/save', params)
}

export function updateActivity(params) {
  return request.post('/meishi-gateway/activity/bindStock', params)
}

export function queryActivity(params) {
  return request.post('/meishi-gateway/mkt/activity/query', params).then(response => {
    const activity = response.activityQueryResultModels?.[0]
    if (!activity) return Promise.reject(new Error('查询结果为空'))
    return activity
  })
}
export function getCityInfo(params) {
  return request.post('/meishi-gateway/mkt/activity/getCityInfo', params)
}
