<script>
import { defineComponent } from '@vue/composition-api'
import { Field, VoidField, ArrayField, ObjectField } from '@nibfe/dz-form-render'
import { FormItem, Input, Select, DatePicker, Checkbox, Radio, InputNumber } from '@nibfe/dz-form-mtd-vue2'
import { Card } from '@ss/mtd-vue2'
import dayjs from 'dayjs'

export default defineComponent({
  setup() {
    return () => {
      return (
        <mtd-form class="match-automation-form">
          <mtd-card title="基本信息" class="margin-bottom-wrapper">
            <Field
              name="activeName"
              title="方案标题"
              required
              decorator={[FormItem]}
              component={[Input]}
            />
            <ObjectField name="subsidyEffectiveness" title="补贴生效情况" decorator={[FormItem]}>
              <div class="margin-bottom-wrapper inline-flex-content">
                <Field
                  name="bmlProductSubSideCount"
                  title="补贴成功数量"
                  component={[InputNumber, { min: 0 }]}
                  decorator={[
                    FormItem,
                    {
                      labelWidth: '160px',
                      addonAfter: () => (
                        <div class="flex-vertically-center">
                          个
                        </div>
                      ),
                    },
                  ]}
                />
              </div>
              <div class="margin-bottom-wrapper inline-flex-content">
                <Field
                  name="bmlProductCount"
                  title="整体数量"
                  component={[InputNumber, { min: 1 }]}
                  decorator={[
                    FormItem,
                    {
                      labelWidth: '1260px',
                      addonAfter: () => (
                        <div className="flex-vertically-center">
                          个
                        </div>
                      ),
                    },
                  ]}
                />
              </div>
            </ObjectField>
            <Field
              name="[startTime, endTime]"
              title="方案有效期"
              required
              decorator={[FormItem]}
              component={[
                DatePicker,
                {
                  type: 'daterange',
                  options: {
                    disabledDate: targetDate => {
                      const daysDiff = dayjs(targetDate).diff(dayjs(), 'day')
                      return daysDiff < 0
                    },
                  },
                },
              ]}
            ></Field>
            <Field
              name="budgetAccount"
              title="预算号"
              required
              decorator={[FormItem]}
              component={[Input]}
            />
            <Field
            name="dzBusiness"
            title="业务线"
            required=""
            decorator={[
              FormItem,
            ]}
            description=""
            dataSource={[
              {
                label: '休娱',
                value: '休娱',
              },
              {
                label: '丽人',
                value: '丽人',
              },
              {
                label: '综发',
                value: '综发',
              },
            ]}
            component={[Select, {
              placeholder: '请选择业务线',
              clearable: true,
            }]}
          />
            <Field
            name="dimensionType"
            title="商品类型"
            required=""
            decorator={[
              FormItem,
            ]}
            dataSource={[
              {
                label: '团单ID',
                value: 'productId',
              },
              {
                label: '医美预付SKU_ID',
                value: 'skuId',
              }
            ]}
            component={[Select,{
              placeholder: '请选择商品类型',
              clearable: true
            }]}
          />
            <ObjectField name="costControl" title="成本控制" decorator={[FormItem]}>
              <div class="margin-bottom-wrapper inline-flex-content">
                <Field
                  name="fullBudget"
                  title="总预算金额"
                  required
                  component={[InputNumber, { min: 0, precision: 2 }]}
                  decorator={[
                    FormItem,
                    {
                      labelWidth: '160px',
                      addonAfter: () => (
                        <div class="flex-vertically-center">
                          元&nbsp;
                          <mtd-tooltip content="活动整体的预算不超过多少" placement="top">
                            <mtd-icon name="mtdicon-question-circle-o" />
                          </mtd-tooltip>
                        </div>
                      ),
                    },
                  ]}
                />
              </div>

              <div class="margin-bottom-wrapper inline-flex-content">
                <Field
                  name="singleCustomerBudget"
                  title="单客户预算金额"
                  required
                  component={[InputNumber, { min: 0, precision: 2 }]}
                  decorator={[
                    FormItem,
                    {
                      labelWidth: '160px',
                      addonAfter: () => (
                        <div class="flex-vertically-center">
                          元&nbsp;
                          <mtd-tooltip content="单个客户的预算不超过多少" placement="top">
                            <mtd-icon name="mtdicon-question-circle-o" />
                          </mtd-tooltip>
                        </div>
                      ),
                    },
                  ]}
                />
              </div>

              <ArrayField name="specificCustomerBudgetList">
                {({ field }) => {
                  const arrayValue = field.value || []

                  return (
                    <div class="inline-array-list-container">
                      <div class="inline-array-list-title">
                        <span>特殊客户预算上限</span>
                        <VoidField name="create">
                          <mtd-button
                            class="add-item"
                            type="primary"
                            onClick={() => arrayValue.push({})}
                          >
                            添加
                          </mtd-button>
                        </VoidField>
                      </div>
                      <div class="inline-array-list">
                        {arrayValue.map((item, index) => {
                          return (
                            <ObjectField key={index} name={index}>
                              <div class="inline-array-list-item">
                                <Field
                                  name="customerId"
                                  title="统一客户ID"
                                  component={[Input]}
                                  decorator={[FormItem, { labelWidth: '160px' }]}
                                  required
                                />
                                <Field
                                  name="budgetUpperLimit"
                                  title="客户预算上限"
                                  component={[InputNumber, { min: 0, precision: 2 }]}
                                  decorator={[FormItem, { labelWidth: '160px' }]}
                                  required
                                />
                                <VoidField name="delete">
                                  <mtd-button onClick={() => arrayValue.splice(index, 1)}>
                                    删除
                                  </mtd-button>
                                </VoidField>
                              </div>
                            </ObjectField>
                          )
                        })}
                      </div>
                    </div>
                  )
                }}
              </ArrayField>
              <p className="warning" style="color: red">
                注：
                <br />
                <span>1）活动总预算金额达补贴上限后，补贴会自行失效，活动在线期间支持上调预算金额</span>
                <br />
                <span class="sku">2）统一单客户预算设置后，当单客户平台补贴部分消耗达预算上限，补贴会自行失效；若不设置，则所有客户共享总补贴预算</span>
                <br />
                <span class="sku">3）如需设置特殊单客户预算，可以添加“统一客户ID”和对应“活动期间单客户预算上限，活动期间支持上调、下调和新增，不支持删除！</span>
              </p>
            </ObjectField>
            <ObjectField name="userRestrict" title="用户限制" decorator={[FormItem]}>
              <div class="margin-bottom-wrapper inline-flex-content">
                <Field
                  name="userCount"
                  title="单用户本活动享受次数限制"
                  component={[InputNumber, { min: 1 }]}
                  decorator={[
                    FormItem,
                    {
                      labelWidth: '160px',
                      addonAfter: () => (
                        <div class="flex-vertically-center">
                          件
                        </div>
                      ),
                    },
                  ]}
                />
              </div>

              <div class="margin-bottom-wrapper inline-flex-content">
                <Field
                  name="userDayCount"
                  title="单用户本活动每日享受次数限制"
                  component={[InputNumber, { min: 1 }]}
                  decorator={[
                    FormItem,
                    {
                      labelWidth: '1260px',
                      addonAfter: () => (
                        <div className="flex-vertically-center">
                          件
                        </div>
                      ),
                    },
                  ]}
                />
              </div>
              <div class="margin-bottom-wrapper inline-flex-content">
                <Field
                  name="userProductCount"
                  title="单用户本活动单商品享受次数限制"
                  component={[InputNumber, { min: 1 }]}
                  decorator={[
                    FormItem,
                    {
                      labelWidth: '180px',
                      addonAfter: () => (
                        <div className="flex-vertically-center">
                          件
                        </div>
                      ),
                    },
                  ]}
                />
              </div>
            </ObjectField>
            <Field
              name="cityIds"
              title="城市限制"
              decorator={[
                FormItem,
              ]}
              component={[Input, {
                style: { width: '420px'},
                placeholder: '请选择城市限制,用中文，号隔开',
                clearable: true
              }]}
            ></Field>
            <Field
              name="bmlPriority"
              title="优先级"
              required=""
              decorator={[
                FormItem,
              ]}
              dataSource={[
                {
                  label: 'P0',
                  value: 'P0',
                },
                {
                  label: 'P1',
                  value: 'P1',
                },
                {
                  label: 'P2',
                  value: 'P2',
                },
              ]}
              component={[Select,{
                placeholder: '请选择优先级',
                clearable: true
              }]}
            ></Field>
            <Field
              name="openAB"
              title="是否AB"
              required
              initialValue="2"
              decorator={[FormItem, {
                addonAfter: () => (
                  <span>
                      <div style="marginLeft: 95px">
                       注：若要进行AB实验，可不用填写选单ID，详情见流程：
                        <a href="https://km.sankuai.com/collabpage/1985873164" target="_blank">
                          流程文档
                        </a>
                      </div>
                    </span>
                ),
              }]}
              component={[Radio.Group]}
              dataSource={[
                { label: '是', value: '1' },
                { label: '否', value: '2' },
              ]}
            />
            <Field
              name="orderId"
              title="选单ID"
              decorator={[
                FormItem,
                {
                  addonAfter: () => (
                    <span>
                      <a
                        href={
                          process.env.VUE_APP_ENV === 'production'
                            ? 'https://mkt.sankuai.com/#/sieve/list'
                            : 'https://mkt.hfe.test.sankuai.com/#/sieve/list'
                        }
                        target="_blank"
                      >
                        选单系统&nbsp;
                      </a>
                      <span>
                        注：暂不支持价保商品、联合补贴商品，仅支持以下类目：
                        <a href="https://km.sankuai.com/collabpage/1985873164" target="_blank">
                          类目文档
                        </a>
                      </span>
                    </span>
                  ),
                },
              ]}
              component={[Input]}
            />
            <Field
              name="notifyUsers"
              title="周知人"
              decorator={[
                FormItem,
                {
                  addonAfter: '*周知人会收到补贴数据日报及异常通知',
                  tooltip: '请输入 mis 号并使用 ;符号分隔，例如：zhangsan; lisi',
                },
              ]}
              component={[Input]}
            />
          </mtd-card>
          <mtd-card title="补贴策略">
            <Field
              name="compareTarget"
              title="比价对象"
              required
              decorator={[FormItem]}
              component={[Radio.Group]}
              dataSource={[
                { label: '友商直播间商品', value: '1' },
                { label: '友商货架商品', value: '2' },
              ]}
            />
            <ObjectField
              name="compareCaliber"
              title="比价口径"
              decorator={[FormItem, { required: true }]}
            >
              <Field
                name="meituan"
                title="美团"
                decorator={[
                  FormItem,
                  {
                    addonAfter: () => (
                      <a href="https://km.sankuai.com/collabpage/1922177557" target="_blank">
                        价格口径说明文档
                      </a>
                    ),
                    labelWidth: 60,
                  },
                ]}
                required
                component={[Radio.Group]}
                dataSource={[
                  { label: '货架普惠价', value: '1' },
                  { label: '直播间普惠价', value: '2' },
                  { label: '特团普惠价', value: '3' },
                ]}
              />
              <Field
                name="doujia"
                title="友商"
                required
                decorator={[FormItem, { labelWidth: 60 }]}
                component={[Radio.Group]}
                dataSource={[
                  { label: '售价', value: '101' },
                  { label: '商补价', value: '102' },
                ]}
              />
            </ObjectField>
            <Field
              name="bmlConstraint"
              title="BML限制"
              decorator={[FormItem]}
              component={[Checkbox]}
            >
              商补价格不 lose
            </Field>
            <ObjectField
              name="constraints"
              title="成本控制"
              decorator={[FormItem,
                { tooltip: '如果多选 生效时取最低的', required: true }
              ]}
              validator={[
                (value, rule, { field, form}) => {
                  if (value.enableAbsoluteDiff || value.enablePercentDiff) {
                    return true
                  }
                  return '请选择成本控制策略'
                }
              ]}>
              <div class="inline-input-with-checkbox">
                <Field name="enableAbsoluteDiff" component={[Checkbox]} />
                <div class="inline-input-item">
                  <span>价差小于</span>
                  <Field
                    name="absoluteDiff"
                    component={[InputNumber, { min: 0, precision: 2 }]}
                    decorator={[FormItem]}
                    required
                  />
                  <span>元</span>
                </div>
              </div>

              <div class="inline-input-with-checkbox">
                <Field name="enablePercentDiff" component={[Checkbox]} />
                <div class="inline-input-item">
                  <span>价差比&le;</span>
                  <Field
                    name="percentDiff"
                    component={[InputNumber, { min:0, precision: 2 }]}
                    decorator={[FormItem]}
                    required
                  />
                  <span>%</span>
                  <mtd-tooltip content="（美团价-友商价）/美团价">
                    <mtd-icon name="question-circle-o" />
                  </mtd-tooltip>
                </div>
              </div>

              <Field name="__triggerValidate"></Field>
            </ObjectField>

            <VoidField
              name="subsidyType"
              title="补贴类型"
              decorator={[FormItem, { required: true }]}
            >
              平台立减
            </VoidField>

            <Field
              name="subsidyChannel"
              title="补贴渠道"
              decorator={[FormItem]}
              component={[Radio.Group]}
              required
              dataSource={[
                { label: '不限', value: 'all' },
                { label: '直播', value: 'liveStreaming' },
                { label: '特价团购', value: 'costEffective' },
              ]}
            />

            <ObjectField
              name="automationSubsidyAmount"
              title="自动补贴金额"
              decorator={[FormItem, { required: true }]}
            >
              <div class="flex-vertically-center">
                <Field
                  name="autoPrice"
                  component={[Radio.Group]}
                  dataSource={[
                    { label: '规则出价', value: 1 }
                  ]}
                  decorator={[FormItem]}
                  validator={[{ required: true, message: '请选择自动补贴金额的策略' }]}
                />
              </div>
              <ArrayField name="autoPriceRule">
                {({ field }) => {
                  const arrayValue = field.value
                  return (
                    <div class="auto-price-grid">
                      {
                        arrayValue.map((item, index) => {
                          return (
                            <div class="auto-price-card">
                              <ObjectField
                                key={index}
                                name={index}
                              >
                                <div class="auto-price-label">
                                  <span style={{ fontWeight: 600 }}>价优目标{index + 1}</span>
                                  {
                                    arrayValue.length > 1 ? <VoidField name="deleteList">
                                      <mtd-button type="text" onClick={() => field.remove(index)}>
                                        删除
                                      </mtd-button>
                                    </VoidField> : null
                                  }
                                </div>
                                <div>
                                  <ObjectField
                                    name="scale"
                                    title="规模占比"
                                    decorator={[FormItem]}
                                  >
                                    <div class="auto-price-form">
                                      <Field
                                        name="min"
                                        component={[InputNumber, {  min:-1, max:10000, placeholder: '最小值', precision: 2 }]}
                                        decorator={[FormItem]}
                                      />
                                      <span>&nbsp;-&nbsp;</span>
                                      <Field
                                        name="max"
                                        component={[InputNumber, { min:0, max:10000, placeholder: '最大值', precision: 2 }]}
                                        decorator={[FormItem]}
                                      />
                                    </div>
                                  </ObjectField>
                                </div>
                                <div>
                                  <ObjectField
                                    name="swingUser"
                                    title="摇摆用户占比"
                                    decorator={[FormItem]}
                                  >
                                    <div class="auto-price-form">
                                      <Field
                                        name="min"
                                        component={[InputNumber, { min:-1, max:1, precision: 2, placeholder: '最小值' }]}
                                        decorator={[FormItem]}
                                      />
                                      <span>&nbsp;-&nbsp;</span>
                                      <Field
                                        name="max"
                                        component={[InputNumber, { min:0, max:1, precision: 2, placeholder: '最大值' }]}
                                        decorator={[FormItem]}
                                      />
                                    </div>
                                  </ObjectField>
                                </div>
                                <div>
                                  <ObjectField
                                    name="supplier"
                                    title="商补价"
                                    decorator={[FormItem]}
                                  >
                                    <div class="auto-price-form">
                                      <Field
                                        name="min"
                                        component={[InputNumber, {min: -1, max:10000, precision: 2, placeholder: '最小值' }]}
                                        decorator={[FormItem]}
                                      />
                                      <span>&nbsp;-&nbsp;</span>
                                      <Field
                                        name="max"
                                        component={[InputNumber, {min:0, max:10000, precision: 2,  placeholder: '最大值' }]}
                                        decorator={[FormItem]}
                                      />
                                    </div>
                                  </ObjectField>
                                </div>
                                <div>
                                  <ObjectField
                                    name="subsidyPrice"
                                    title="补贴金额"
                                    decorator={[FormItem, { required: true }]}
                                  >
                                    <div class="flex-vertically-center">
                                      <Field
                                        name="type"
                                        component={[Radio.Group]}
                                        dataSource={[
                                          { label: '补贴到追平', value: 1 },
                                          { label: '补贴到价优', value: 2 },
                                        ]}
                                        decorator={[FormItem]}
                                        validator={[{ required: true, message: '请选择自动补贴金额的策略' }]}
                                      />
                                      <div class="to_higher-wrapper">
                                        <Field
                                          required
                                          name="toHigher"
                                          component={[InputNumber, { min: 0, precision: 2 }]}
                                          decorator={[FormItem, { addonAfter: () => <span>元</span> }]}
                                        />
                                      </div>
                                    </div>
                                  </ObjectField>
                                </div>
                              </ObjectField>
                            </div>
                          )
                        })
                      }
                      {
                        arrayValue.length < 20 ? <VoidField name="createList">
                          <div class="auto-price-create" onClick={() => {
                            field.push({})
                          }}>
                            +增加价优目标
                          </div>
                        </VoidField> : ''
                      }
                    </div>
                  )
                }}
              </ArrayField>
              <ObjectField name="autoPriceAlgorithm" decorator={[FormItem, { required: true }]}>
                <div class="flex-vertically-center">
                  <Field
                    name="type"
                    component={[Radio.Group]}
                    dataSource={[
                      { label: '补贴到追平', value: 1 },
                      { label: '补贴到价优', value: 2 },
                    ]}
                    decorator={[FormItem]}
                    validator={[{ required: true, message: '请选择自动补贴金额的策略' }]}
                  />
                  <div class="to_higher-wrapper">
                    <Field
                      name="toHigher"
                      required
                      component={[InputNumber, { min: 0, precision: 2 }]}
                      decorator={[FormItem, { addonAfter: () => <span>元</span> }]}
                      validator={[{ required: true, message: '请输入具体值' }]}
                    />
                  </div>
                </div>
              </ObjectField>
            </ObjectField>

            <ObjectField
              name="subsidyUpperLimit"
              title="补贴上限"
              decorator={[FormItem, { required: true }]}
              validator={[
                (value, rule, { field, form}) => {
                  if (value.enableAbsoluteDiff || value.enablePercentDiff) {
                    return true
                  }
                  return '请选择补贴上限策略'
                }
              ]}
            >
              <div class="inline-input-with-checkbox">
                <Field name="enableAbsoluteDiff" component={[Checkbox]} />
                <div class="inline-input-item">
                  <span>补贴上限不超过</span>
                  <Field
                    name="absoluteDiff"
                    component={[InputNumber, { min: 0, precision: 2 }]}
                    decorator={[FormItem]}
                    required
                  />
                  <span>元</span>
                </div>
              </div>

              <div class="inline-input-with-checkbox">
                <Field name="enablePercentDiff" component={[Checkbox]} />
                <div class="inline-input-item">
                  <span>补贴价格&le;佣金</span>
                  <Field
                    name="percentDiff"
                    component={[InputNumber, { min: 0, max: 100, precision: 2 }]}
                    decorator={[FormItem]}
                    required
                  />
                  <span>%</span>
                </div>
              </div>
              <Field name="__triggerValidate"></Field>
            </ObjectField>


          </mtd-card>
        </mtd-form>
      )
    }
  },
})
</script>

<style lang="less">
.match-automation-form.mtd-form {
  overflow: auto;

  p.warning {
    color: red;
  }

  .flex-vertically-center {
    display: flex;
    align-items: center;
  }

  .to_higher-wrapper {
    margin-left: 6px;

    > .mtd-form-item {
      .formily-mtd-form-item-control-content {
        display: flex;
      }
    }
  }
  .auto-price-grid {
    width: 90%;
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
  .auto-price-card {
    width: 660px;
    height: 240px;
    background-color: #f8f8f8;
    margin-bottom: 16px;
    padding: 10px;
  }
  .auto-price-create {
    width: 660px;
    margin-bottom: 16px;
    background-color: #f8f8f8;
    text-align: center;
    line-height: 220px;
    font-size: 24px;
    border: 2px dashed #ddd;
    cursor: pointer;
  }
  .auto-price-label {
    display: flex;
    justify-content: space-between;
  }
  .auto-price-form {
    display: flex;
    margin-bottom: 10px;
  }

  .margin-bottom-wrapper:not(:last-child) {
    margin-bottom: 16px;
  }

  .inline-flex-content
    > .mtd-form-item
    > .mtd-form-item-content
    > .formily-mtd-form-item-control
    > .formily-mtd-form-item-control-content {
    display: flex;
  }

  .inline-input-with-checkbox:not(:last-child) {
    margin-bottom: 8px;
  }

  .inline-input-with-checkbox {
    display: flex;
    align-items: center;

    label.mtd-checkbox {
      margin-right: 6px;
    }

    .inline-input-item {
      display: inline-flex;
      align-items: center;

      .mtd-input-wrapper,
      .mtd-input-number-wrapper {
        margin: 0px 6px;
        border-radius: 4px;
      }

      i {
        margin-left: 6px;
      }
    }
  }

  .inline-array-list-container {
    .inline-array-list-title {
      .mtd-btn.add-item {
        margin-left: 8px;
      }

      margin-bottom: 16px;
    }

    .inline-array-list {
      .inline-array-list-item:not(:last-child) {
        margin-bottom: 8px;
      }

      .inline-array-list-item {
        display: flex;
        gap: 8px;

        > .mtd-form-item {
          display: flex;
        }
      }
    }
  }

  .mtd-radio-group-line {
    gap: 12px;

    .mtd-radio-text {
      padding-right: 0;
    }
  }
}
p.warning {
  color: red;
}
</style>
