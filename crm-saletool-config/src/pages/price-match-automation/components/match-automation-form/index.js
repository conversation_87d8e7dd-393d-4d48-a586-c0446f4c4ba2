import { FormPath, createForm, onFormInit, onFieldChange, onFieldValidateEnd } from '@nibfe/dz-form-core'
import dayjs from 'dayjs'
import Vue from 'vue'

import {onFieldInitAndValueChange, onFormInitAndReset} from '@/lib/form-hooks'
import { cloneDeep } from 'lodash'

import MatchAutomationForm from './form.vue'

const createMatchAutomationForm = {
  /**
   * 基础逻辑，各个模式通用
   *
   * @param {import("@nibfe/dz-form-core").IFormProps} configuration
   * @returns {import("@nibfe/dz-form-core").Form}
   */
  Base(configuration = {}) {
    return createForm({
      ...configuration,
      effects() {
        onFieldInitAndValueChange('openAB', (field, form) => {
          const type = field.value
          Vue.nextTick(() => {
            form.setFieldState('orderId', {
              display: type == '1' ? 'hidden' : 'visible'
            })
            form.setFieldState('automationSubsidyAmount.autoPrice', {
              dataSource: type == '1' ? [
                {label: '规则出价', value: 1},
                {label: '算法出价', value: 2},
              ] : [
                {label: '规则出价', value: 1},
              ]
            })
          })
        })
        onFieldInitAndValueChange('dimensionType', (field, form) => {
          const type = field.value
          if (type === 'skuId') {
            form.setFieldState('costControl.singleCustomerBudget', {
              display: 'hidden',
              value: ''
            })
            form.setFieldState('costControl.specificCustomerBudgetList', {
              display: 'hidden',
            })
            document.querySelectorAll('.sku').forEach(item => {
              item.style.display = 'none'
            })
          } else {
            form.setFieldState('costControl.singleCustomerBudget', {
              display: 'visible',
            })
            form.setFieldState('costControl.specificCustomerBudgetList', {
              display: 'visible',
            })
            document.querySelectorAll('.sku').forEach(item => {
              item.style.display = 'inline-block'
            })
          }
        })
        configuration.effects?.()
      },
    })
  },
  /**
   * 创建模式
   *
   * @returns {import("@nibfe/dz-form-core").Form}
   */
  Create() {
    return this.Base({
      effects() {
        onFormInit(form => {
          form.setFieldState('subsidyEffectiveness', {
            display: 'none',
          })
        })
        onFieldInitAndValueChange('constraints.enableAbsoluteDiff', (field, form) => {
          const enabled = field.value ?? false

          form.setFieldState('constraints.absoluteDiff', {
            value: null,
            disabled: !enabled,
          })
        })
        onFieldInitAndValueChange('dimensionType', (field, form) => {
          const type = field.value
          form.setFieldState('automationSubsidyAmount.autoPrice', {
            value: ''
          })
          form.setFieldState('openAB', {
            disabled: type === 'skuId',
            value: '2'
          })
        })

        // formily 的 objectField/arrayField 主动触发校验
        onFieldInitAndValueChange("constraints.*", (field, form) => {
          form.validate('constraints').catch(()=>{})
        })
        // formily 的 objectField/arrayField 校验完成主动更新ui
        onFieldValidateEnd('constraints', (field, form) => {
          Vue.nextTick().then(() => {
            form.setFieldState('constraints.__triggerValidate', {
              display: "none"
            })
            form.setFieldState('constraints.__triggerValidate', {
              display: "visible"
            })
          })
        })

        onFieldInitAndValueChange('constraints.enablePercentDiff', (field, form) => {
          const enabled = field.value ?? false

          form.setFieldState('constraints.percentDiff', {
            value: null,
            disabled: !enabled,
          })
        })

        // formily 的 objectField/arrayField 主动触发校验
        onFieldInitAndValueChange("subsidyUpperLimit.*", (field, form) => {
          form.validate('subsidyUpperLimit').catch(()=>{})
        })
        // formily 的 objectField/arrayField 校验完成主动更新ui
        onFieldValidateEnd('subsidyUpperLimit', (field, form) => {
          Vue.nextTick().then(() => {
            form.setFieldState('subsidyUpperLimit.__triggerValidate', {
              display: "none"
            })
            form.setFieldState('subsidyUpperLimit.__triggerValidate', {
              display: "visible"
            })
          })
        })

        onFieldInitAndValueChange('subsidyUpperLimit.enableAbsoluteDiff', (field, form) => {
          const enabled = field.value ?? false

          form.setFieldState('subsidyUpperLimit.absoluteDiff', {
            value: null,
            disabled: !enabled,
          })
        })

        onFieldInitAndValueChange('subsidyUpperLimit.enablePercentDiff', (field, form) => {
          const enabled = field.value ?? false

          form.setFieldState('subsidyUpperLimit.percentDiff', {
            value: null,
            disabled: !enabled,
          })
        })
        //  规则出价
        onFieldChange('automationSubsidyAmount.autoPriceRule.*.subsidyPrice.type', (field, form) => {
          const type = field.value ?? null
          const autoPriceRule = cloneDeep(form.values.automationSubsidyAmount.autoPriceRule)
          if (autoPriceRule && autoPriceRule.length > 0) {
            autoPriceRule.map((item, index) => {
              form.setFieldState(`automationSubsidyAmount.autoPriceRule.${index}.subsidyPrice.toHigher`, {
                disabled: item.subsidyPrice?.type !== 2,
              })
            })
          }
        })
        onFieldInitAndValueChange('automationSubsidyAmount.autoPrice', (field, form) => {
          const type = form.values.dimensionType ?? 'productId'
          const value = field.value
          if (value == '1') {
            setTimeout(() => {
              form.setFieldState('automationSubsidyAmount.autoPriceRule',  {
                value: [{}],
                display: (type == 'productId')  ? 'visible' : 'hidden'
              })
              form.setFieldState('automationSubsidyAmount.autoPriceAlgorithm', {
                display: (type == 'skuId') ? 'visible' : 'hidden'
              })
            })
          } else {
            // 算法出价
            setTimeout(() => {
              form.setFieldState('automationSubsidyAmount.autoPriceRule',  {
                display: 'hidden'
              })
              form.setFieldState('automationSubsidyAmount.autoPriceAlgorithm', {
                display: 'hidden'
              })
            })
          }
        })
        onFieldInitAndValueChange('automationSubsidyAmount.autoPriceAlgorithm.type', (field, form) => {
          const type = field.value ?? null

          form.setFieldState('automationSubsidyAmount.autoPriceAlgorithm.toHigher', {
            value: null,
            disabled: type !== 2,
          })
        })
      },
    })
  },

  /**
   * 预览模式
   *
   * @returns {import("@nibfe/dz-form-core").Form}
   */
  Preview() {
    return this.Base({
      readPretty: true,
      effects() {
        onFormInit(form => {
          form.setFieldState('costControl.specificCustomerBudgetList.create', {
            display: 'none',
          })
          form.setFieldState('subsidyEffectiveness', {
            display: 'visible',
          })
        })
        onFieldInitAndValueChange('automationSubsidyAmount.autoPriceRule', (field, form) => {
          const autoPriceRule = cloneDeep(form.values.automationSubsidyAmount.autoPriceRule)
          if (autoPriceRule && autoPriceRule.length > 0) {
            autoPriceRule.map((item, index) => {
              const type = item.subsidyPrice.type ?? null
              if (type === 1) {
                form.setFieldState(`automationSubsidyAmount.autoPriceRule.${index}.subsidyPrice.toHigher`, {
                  display: 'hidden',
                })
              }
            })
          }
        })
        onFieldInitAndValueChange('automationSubsidyAmount.autoPriceAlgorithm.type', (field, form) => {
          const type = field.value
          if (type === 1) {
            form.setFieldState('automationSubsidyAmount.autoPriceAlgorithm.toHigher', {
              display: 'hidden',
            })
          }
        })
        onFieldInitAndValueChange('automationSubsidyAmount.autoPrice', (field, form) => {
          const type = form.values.dimensionType ?? 'productId'
          const value = field.value
          if (value == '1') {
            setTimeout(() => {
              form.setFieldState('automationSubsidyAmount.autoPriceRule',  {
                display: (type == 'productId' && value)  ? 'visible' : 'hidden'
              })
              form.setFieldState('automationSubsidyAmount.autoPriceAlgorithm', {
                display: (type == 'skuId' && value) ? 'visible' : 'hidden'
              })
            })
          } else {
            // 算法出价
            setTimeout(() => {
              form.setFieldState('automationSubsidyAmount.autoPriceRule',  {
                display: 'hidden'
              })
              form.setFieldState('automationSubsidyAmount.autoPriceAlgorithm', {
                display: 'hidden'
              })
            })
          }
        })
      },
    })
  },

  /**
   * 编辑模式
   *
   * @returns {import("@nibfe/dz-form-core").Form}
   */
  Edit() {
    return this.Base({
      pattern: 'disabled',
      effects() {
        onFormInit(form => {
          form.setFieldState('costControl.*(fullBudget, singleCustomerBudget)', {
            pattern: 'editable',
          })
          form.setFieldState('subsidyEffectiveness', {
            display: 'none',
          })
          form.setFieldState('costControl.specificCustomerBudgetList.*.budgetUpperLimit', {
            pattern: 'editable',
          })
        })
      },
    })
  },
}

export {MatchAutomationForm, createMatchAutomationForm}

export * from './transformer'
