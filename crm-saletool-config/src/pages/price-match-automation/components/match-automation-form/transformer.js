/**
 * 由于营销后端服务面向多个依赖方，未给出针对 BML 自动补贴场景的专用接口
 * 而原始接口的参数又过于复杂，对目标场景来说存在大量冗余的结构
 * 因此，这里对 更新/创建 以及 回填的数据 都提供了专门的解析函数
 */

import dayjs from 'dayjs'
import {arithmetic, safeJSONParse} from '@/lib/utils'
import {getCityInfo} from "@/pages/price-match-automation/api";

/**
 * 根据后端下发的数据解析回填到表单中的数据
 */
export function parseInputValues(source) {
  const {
    activityBaseInfoModel: { activeName,dimensionType, startTime, endTime, budgetInfoModels, notifyUsers },
    activityStockInfoModel: { timeStockInfos, timeStockDimensionInfos },
    activitySieveInfoModel: { blackWhiteListInfos },
    activityRuleInfoModel: { ruleGroupInfoModels },
    activityExtraInfoModel: { extraInfoMap }
  } = source
  // 判断是否是规则出价
  const bidType = extraInfoMap?.bidType
  let autoPriceList = [];
  if (bidType == 0 || bidType == undefined) { // 兼容历史数据
    ruleGroupInfoModels[0]?.ruleInfoModels.map((item, index) => {
      const param = {
        swingUser: {min: '', max: ''},
        scale: {min: '', max: ''},
        supplier: {min: '', max: ''},
        subsidyPrice: { type: null, toHigher: null}
      }
      item.conditionInfoModels.map(i => {
        if (i.conditionName === 'yaobaiUvRatio') {
          param.swingUser.min = arithmetic.division((i.target.split(',')[0] || 0), 100).toFixed(2)
          param.swingUser.max = arithmetic.division((i.target.split(',')[1] || 0), 100).toFixed(2)
        }
        if (i.conditionName === 'gtvRatio') {
          param.scale.min = arithmetic.division((i.target.split(',')[0] || 0), 100).toFixed(2)
          param.scale.max = arithmetic.division((i.target.split(',')[1] || 0), 100).toFixed(2)
        }
        if (i.conditionName === 'mtCommercialSubsidyPrice') {
          param.supplier.min = arithmetic.division((i.target.split(',')[0] || 0), 100).toFixed(2) + '元'
          param.supplier.max = arithmetic.division((i.target.split(',')[1] || 0), 100).toFixed(2) + '元'
        }
      })
      const target = parseFloat(item.actionInfoModel?.target)
      param.subsidyPrice.type = (() => {
        if (isNaN(target)) return null;
        if (target === 0) return 1;
        return 2
      })()
      param.subsidyPrice.toHigher = (() => {
        if (isNaN(target) || target === 0) return null;
        return arithmetic.division(target, 100).toFixed(2)
      })()
      autoPriceList.push(param)
    })
  }
  const conditionInfoModels = ruleGroupInfoModels?.[0]?.ruleInfoModels?.[0]?.conditionInfoModels
  const actionInfoModel = ruleGroupInfoModels?.[0]?.ruleInfoModels?.[0]?.actionInfoModel

  const extraData = safeJSONParse(actionInfoModel?.actionExtraData, {})

  return {
    openAB: extraInfoMap.openAB == 'true' ? '1' : '2',
    subsidyEffectiveness: {
      bmlProductSubSideCount: extraInfoMap?.bmlProductSubSideCount,
      bmlProductCount: extraInfoMap?.bmlProductCount
    },
    activeName: activeName,
    dimensionType: dimensionType,
    startTime: new Date(startTime),
    endTime: new Date(endTime),
    budgetAccount: budgetInfoModels[0].pn,
    costControl: {
      fullBudget: (() => {
        const fullBudgetItem = (timeStockInfos || []).find(item => item.activityStockSubType === 1) // 总预算金额

        const stockValue = parseFloat(fullBudgetItem?.singleTimeStockInfos?.[0]?.stockValue)

        return isNaN(stockValue) ? null : arithmetic.division(stockValue, 100).toFixed(2)
      })(),
      singleCustomerBudget: (() => {
        const singleTimeStockDimensionInfos =
          timeStockDimensionInfos?.[0]?.singleTimeStockDimensionInfos || []
        const singleCustomerBudgetItem = singleTimeStockDimensionInfos.find(
          item => item.dimensionModels?.[0]?.dimensionId === -99999,
        )

        const stockValue = parseFloat(singleCustomerBudgetItem?.stockValue)

        return isNaN(stockValue) ? null : arithmetic.division(stockValue, 100).toFixed(2)
      })(),
      specificCustomerBudgetList: (() => {
        const singleTimeStockDimensionInfos =
          timeStockDimensionInfos?.[0]?.singleTimeStockDimensionInfos || []

        return singleTimeStockDimensionInfos
          .filter(item => {
            const dimensionId = item.dimensionModels?.[0]?.dimensionId
            return dimensionId && dimensionId !== -99999
          })
          .map(item => {
            const stockValue = parseFloat(item.stockValue)

            return {
              customerId: item.dimensionModels?.[0]?.dimensionId,
              budgetUpperLimit: isNaN(stockValue) ? null : arithmetic.division(stockValue, 100).toFixed(2),
            }
          })
      })()
    },
    userRestrict: {
      userCount: (() => {
        const singleTimeStockInfos =
          (timeStockInfos || []).find(item => item.activityStockSubType === 4)?.singleTimeStockInfos
        if (singleTimeStockInfos) {
          return singleTimeStockInfos[0]?.stockValue || '-'
        }
        return '-'
      })(),
      userDayCount: (() => {
        const singleTimeStockInfos =
          (timeStockInfos || []).find(item => item.activityStockSubType === 9)?.singleTimeStockInfos
        if (singleTimeStockInfos) {
          return singleTimeStockInfos[0]?.stockValue || '-'
        }
        return '-'
      })(),
      userProductCount: (() => {
        const singleTimeStockInfos =
          dimensionType === 'productId' ? (timeStockInfos || []).find(item => item.activityStockSubType === 11)?.singleTimeStockInfos : (timeStockInfos || []).find(item => item.activityStockSubType === 23)?.singleTimeStockInfos
        if (singleTimeStockInfos) {
          return singleTimeStockInfos[0]?.stockValue || '-'
        }
        return '-'
      })()
    },
    dzBusiness: extraInfoMap.dzBusiness, // 业务线
    bmlPriority: extraInfoMap.bmlPriority, // 优先级
    cityIds: extraInfoMap.cityIds,
    orderId: blackWhiteListInfos?.[0]?.newSieveId,
    notifyUsers: (notifyUsers || []).map(item => item.userName).join(';'),
    automationSubsidyAmount: { // 自动补贴金额
      autoPrice: (bidType == 0 || bidType == undefined)? 1 : 2,
      // autoPrice: 1,
      autoPriceRule: (bidType == 0 || bidType == undefined) ? autoPriceList : null,
      // skuId
      autoPriceAlgorithm: (() => {
        const target = parseFloat(actionInfoModel?.target)
        if (isNaN(target)) return { type: null, toHigher: null }
        if (target === 0) return { type: 1, toHigher: null }
        return {
          type: 2,
          toHigher: arithmetic.division(target, 100).toFixed(2),
        }
      })(bidType == 1 || bidType == undefined),
    },
    compareTarget: extraData.subsidyDataCollectType,
    compareCaliber: {
      meituan: extraData.mtVsPriceType,
      doujia: extraData.djVsPriceType,
    },
    constraints: (() => {
      const absoluteDiff = parseFloat(extraData.subsidyPriceConditionRange?.split(',')?.[0])
      const percentDiff = parseFloat(extraData.subsidyPriceConditionRateRange?.split(',')?.[0])

      return {
        enableAbsoluteDiff: !isNaN(absoluteDiff),
        absoluteDiff: isNaN(absoluteDiff) ? null : arithmetic.division((absoluteDiff * -1), 100).toFixed(2),
        enablePercentDiff: !isNaN(percentDiff),
        percentDiff: isNaN(percentDiff) ? null : arithmetic.division((percentDiff * -1), 100).toFixed(2),
      }
    })(),
    subsidyChannel: (() => {
      const channelItem = (conditionInfoModels || []).find(
        item => item.conditionName === 'saleChannelType',
      )

      return channelItem?.target ?? 'all'
    })(),
    subsidyUpperLimit: (() => {
      const subsidyUpLimit = parseFloat(extraData.subsidyUpLimit)
      const subsidyRateUpLimit = parseFloat(extraData.subsidyRateUpLimit)
      return {
        enableAbsoluteDiff: !isNaN(subsidyUpLimit),
        absoluteDiff: isNaN(subsidyUpLimit) ? null : arithmetic.division(subsidyUpLimit, 100).toFixed(2),
        enablePercentDiff: !isNaN(subsidyRateUpLimit),
        percentDiff: isNaN(subsidyRateUpLimit) ? null : arithmetic.division(subsidyRateUpLimit, 100).toFixed(2),
      }
    })(),
    bmlConstraint: extraData.comSubsidyLoseType === '0,1',
  }
}

/**
 * 将表单值封装为面向更新接口的数据
 */
export function packageUpdateSubmitValues(formValues, additionParams) {
  const { activityId } = additionParams

  const { costControl, notifyUsers } = formValues

  return {
    activityOptSourceEnum: 'mktManagement',
    bindStockList: [
      {
        activityId: activityId,
        departmentTypes: ['DAOZONG'],
        stockUpdateStrategy: 24,
        activityUpdateStockValueModels: [
          {
            activityStockSubType: 1,
            updateType: 2,
            updateStockValue: arithmetic.multiple(costControl.fullBudget, 100), // 总预算金额
            periodValue: null,
          },
        ],
        activityAppendStockModel: {
          timeStockDimensionInfos: [
            costControl.singleCustomerBudget ? {
              stockSubType: 5062,
              activityStockSubType: 18,
              singleTimeStockDimensionInfos: [
                {
                  stockValue: arithmetic.multiple(costControl.singleCustomerBudget, 100 ), // 单客户预算金额
                  dimensionModels: [
                    {
                      dimensionType: 1900,
                      dimensionId: -99999,
                    },
                  ],
                },
              ].concat(
                (costControl.specificCustomerBudgetList || []).map(item => ({
                  // 特殊客户预算金额
                  stockValue: arithmetic.multiple( item.budgetUpperLimit, 100),
                  dimensionModels: [
                    {
                      dimensionType: 1900,
                      dimensionId: item.customerId, // 客户 ID
                    },
                  ],
                })),
              ),
            } : null,
          ].filter(Boolean),
        },
        applyToolType: 'PDA',
        outBizId: 'mktManagement_' + new Date().getTime(),
        activeStockAlarmInfos: [
          {
            receivers: JSON.stringify(
              (notifyUsers || '')
                .split(/[,，;；]/)
                .map(item => item.trim())
                .filter(Boolean),
            ),
          },
        ],
      },
    ],
  }
}

/**
 * 将表单值封装为面向创建接口的数据
 */
export function packageCreateSubmitValues(formValues, cityStr) {
  const {
    openAB,
    activeName,
    startTime,
    endTime,
    budgetAccount,
    costControl,
    userRestrict,
    orderId,
    notifyUsers,
    compareTarget,
    compareCaliber,
    constraints,
    subsidyChannel,
    automationSubsidyAmount,
    subsidyUpperLimit,
    bmlConstraint,
    bmlPriority,
    dimensionType,
    cityIds,
    dzBusiness,
  } = formValues

  const extraData = {
    subsidyUpLimit:
      typeof subsidyUpperLimit.absoluteDiff === 'number'
        ? (arithmetic.multiple(subsidyUpperLimit.absoluteDiff, 100)).toString()
        : '', // 补贴上限 元
    subsidyRateUpLimit:
      typeof subsidyUpperLimit.percentDiff === 'number'
        ? (arithmetic.multiple(subsidyUpperLimit.percentDiff, 100)).toString()
        : '', // 补贴上限 百分比
    subsidyDataCollectType: compareTarget, // 比价对象 (1, 2)
    comSubsidyLoseType: bmlConstraint ? '0,1' : '', // BML 限制
    mtVsPriceType: compareCaliber.meituan, // 比价口径 美团 (1, 2, 3)
    djVsPriceType: compareCaliber.doujia, // 比价口径 D+ (101, 102)
    bidType: automationSubsidyAmount.autoPrice == '1' ? '0' : '1', // 0: 规则出价 1:算法出价
    subsidyPriceConditionRange:
      typeof constraints.absoluteDiff === 'number'
        ? `${arithmetic.multiple((constraints.absoluteDiff ?? 0) * -1, 100)},100000000`
        : '', // 自动补贴条件 元: 用户输入值取反 * 100
    subsidyPriceConditionRateRange:
      typeof constraints.percentDiff === 'number'
        ? `${arithmetic.multiple((constraints.percentDiff ?? 0) * -1, 100)},100000000`
        : '', // 自动补贴条件 百分比: 用户输入值取反 * 100
  }
  return {
    paramModels: [
      {
        lockKey: 'mktManagementundefined',
        applyTypeEnum: 'MINUS',
        createSceneEnum: 'DZ_BML_PURSUIT_PRICE',
        activityBaseInfoModel: {
          productType: dimensionType === 'productId' ? 'DAOZONG_GROUP' : 'PAN_COMMODITY_TRADING',
          dimensionType: dimensionType,
          activeName,
          startTime: dayjs(startTime).startOf('day').valueOf(),
          endTime: dayjs(endTime).endOf('day').valueOf(),
          budgetInfoModels: [
            {
              departmentTypeEnum: 'DAOZONG',
              costType: 'meituan',
              budgetType: 2,
              pn: budgetAccount,
            },
          ],
          notifyUsers: (notifyUsers || '')
            .split(/[,，;；]/)
            .map(item => item.trim())
            .filter(Boolean)
            .map(mis => ({
              userName: mis,
            })),
          activityPromoLimitModel: { paymentRuleId: 261241 },
          freezeStock: true,
        },
        // 活动规则信息
        activityRuleInfoModel: {
          ruleGroupInfoModels: [
            {
              commonConditions: [
                cityStr ? {
                  conditionType: 'FIXED',
                  valueProcessType: null,
                  orderNum: 0,
                  conditionName: 'browseMtCityId',
                  target: cityStr,
                  calculateType: 'CONTAINED',
                  adminConditionId: null,
                  conditionActDependencyId: null,
                } : null
              ].filter(Boolean),
              ruleInfoModels: [].concat(
                dimensionType == 'productId' ?
                automationSubsidyAmount.autoPrice == '1' ? (automationSubsidyAmount.autoPriceRule || []).map((item, index) => ({
                  index: index,
                  orderNum: index,
                  conditionInfoModels: [
                    {
                      conditionType: 'STEPPED',
                      valueProcessType: null,
                      orderNum: 0,
                      target: '0-86399000',
                      conditionName: 'availableTimeOfDay',
                      calculateType: 'MULTI_BOTH_OPEN_BETWEEN',
                      adminConditionId: null,
                      conditionActDependencyId: null,
                    },
                    subsidyChannel === 'all' ? null : {
                        conditionType: 'STEPPED',
                        conditionName: 'saleChannelType',
                        target: subsidyChannel, // 补贴渠道 liveStreaming 直播 / costEffective 特价团购
                        calculateType: 'CONTAINED',
                    },
                    ((item.swingUser.min !== null && item.swingUser.min !== undefined) && (item.swingUser.max !== null && item.swingUser.max !== undefined)) ? {
                      conditionType: 'STEPPED',
                      conditionName: 'yaobaiUvRatio',
                      target: `${arithmetic.multiple((item.swingUser.min ?? 0), 100)},${arithmetic.multiple((item.swingUser.max ?? 0), 100)}`,
                      calculateType: 'BETWEEN'
                    } : null,
                    ((item.scale.min !== null && item.scale.min !== undefined) && (item.scale.max !== null && item.scale.max !== undefined)) ? {
                      conditionType: 'STEPPED',
                      conditionName: 'gtvRatio',
                      target: `${arithmetic.multiple((item.scale.min ?? 0), 100)},${arithmetic.multiple((item.scale.max ?? 0), 100)}`,
                      calculateType: 'BETWEEN'
                    } : null,
                    ((item.supplier.min !== null && item.supplier.min !== undefined) && (item.supplier.max !== null && item.supplier.max !== undefined)) ? {
                      conditionType: 'STEPPED',
                      conditionName: 'mtCommercialSubsidyPrice',
                      target: `${arithmetic.multiple((item.supplier.min ?? 0), 100)},${arithmetic.multiple((item.supplier.max ?? 0), 100)}`,
                      calculateType: 'BETWEEN'
                    } : null,
                  ].filter(Boolean),
                  actionInfoModel: {
                    target: (function () {
                      if (item.subsidyPrice?.type === 1) return 0
                      if (item.subsidyPrice?.type !== 2) return null
                      if (item.subsidyPrice?.toHigher)
                        return item.subsidyPrice.toHigher * 100
                      return null
                    })(), // 自动补贴金额 0 追平 >0 到价优 需要 * 100
                    subsidyBound: null,
                    actionTypeName: 'bmlUpThanComFixPrice',
                    actionExtraData: JSON.stringify(extraData),
                    actionActDependencyIds: null,
                  }
                })) : {
                  index: 0,
                  orderNum: 0,
                  conditionInfoModels: [
                    {
                      conditionType: 'STEPPED',
                      valueProcessType: null,
                      orderNum: 0,
                      target: '0-86399000',
                      conditionName: 'availableTimeOfDay',
                      calculateType: 'MULTI_BOTH_OPEN_BETWEEN',
                      adminConditionId: null,
                      conditionActDependencyId: null,
                    },
                    subsidyChannel === 'all' ? null : {
                      conditionType: 'STEPPED',
                      conditionName: 'saleChannelType',
                      target: subsidyChannel, // 补贴渠道 liveStreaming 直播 / costEffective 特价团购
                      calculateType: 'CONTAINED',
                    },
                  ].filter(Boolean),
                  actionInfoModel: {
                    target: 0, // 团单算法出价
                    subsidyBound: null,
                    actionTypeName: 'bmlUpThanComFixPrice',
                    actionExtraData: JSON.stringify(extraData),
                    actionActDependencyIds: null,
                  }
                } : {
                    index: 0,
                    orderNum: 0,
                    conditionInfoModels: [
                      {
                        conditionType: 'STEPPED',
                        valueProcessType: null,
                        orderNum: 0,
                        target: '0-86399000',
                        conditionName: 'availableTimeOfDay',
                        calculateType: 'MULTI_BOTH_OPEN_BETWEEN',
                        adminConditionId: null,
                        conditionActDependencyId: null,
                      },
                      subsidyChannel === 'all' ? null : {
                        conditionType: 'STEPPED',
                        conditionName: 'saleChannelType',
                        target: subsidyChannel, // 补贴渠道 liveStreaming 直播 / costEffective 特价团购
                        calculateType: 'CONTAINED',
                      },
                    ].filter(Boolean),
                    actionInfoModel: { // sku规则出价
                      target: (function () {
                        if (automationSubsidyAmount?.autoPriceAlgorithm?.type === 1) return 0
                        if (automationSubsidyAmount?.autoPriceAlgorithm?.type !== 2) return null
                        if (automationSubsidyAmount?.autoPriceAlgorithm?.toHigher)
                          return automationSubsidyAmount.autoPriceAlgorithm.toHigher * 100
                        return null
                      })(), // 自动补贴金额 0 追平 >0 到价优 需要 * 100
                      subsidyBound: null,
                      actionTypeName: 'bmlUpThanComFixPrice',
                      actionExtraData: JSON.stringify(extraData),
                      actionActDependencyIds: null,
                    }
                  }
              )
            }
          ]
        },
        activityStockInfoModel: {
          // 总预算金额
          timeStockInfos: [
            {
              activityStockSubType: 1,
              singleTimeStockInfos: [
                { stockValue: arithmetic.multiple(costControl.fullBudget, 100), periodValue: null },
              ],
              departmentTypeEnum: 'DAOZONG',
            },
            // 每人活动限购
            userRestrict.userCount ? {
              activityStockSubType: 4,
              singleTimeStockInfos: [
                { stockValue: userRestrict.userCount, periodValue: null },
              ],
              departmentTypeEnum: 'DAOZONG',
            } : null,
            // 每人每日
            userRestrict.userDayCount ? {
              activityStockSubType: 9,
              singleTimeStockInfos: [
                { stockValue: userRestrict.userDayCount, periodValue: 1 },
              ],
              departmentTypeEnum: 'DAOZONG',
            } : null,
            // 单商品
            dimensionType === 'productId' ? userRestrict.userProductCount ? {
                activityStockSubType: 11, // 团单
                singleTimeStockInfos: [
                  { stockValue: userRestrict.userProductCount, periodValue: null },
                ],
                departmentTypeEnum: 'DAOZONG',
              } : null :
              userRestrict.userProductCount ? {
                activityStockSubType: 23, // SKU
                singleTimeStockInfos: [
                  { stockValue: userRestrict.userProductCount, periodValue: null },
                ],
                departmentTypeEnum: 'DAOZONG',
              } : null,
          ].filter(Boolean),

          // 特殊客户预算金额 & 单客户预算金额
          timeStockDimensionInfos: [
            costControl.singleCustomerBudget ? {
              stockSubType: 5062,
              activityStockSubType: 18,
              singleTimeStockDimensionInfos: [
                {
                  stockValue: arithmetic.multiple(costControl.singleCustomerBudget, 100),
                  dimensionModels: [{ dimensionType: 1900, dimensionId: -99999 }],
                },
              ].concat(
                (costControl.specificCustomerBudgetList || []).map(item => ({
                  stockValue: arithmetic.multiple(item.budgetUpperLimit, 100),
                  dimensionModels: [
                    {
                      dimensionType: 1900,
                      dimensionId: item.customerId, // 客户 ID
                    },
                  ],
                })),
              ),
            } : null,
          ].filter(Boolean),
        },
        activitySieveInfoModel: {
          blackWhiteListInfos: openAB != '1' ? [
            {
              departmentTypeEnum: 'DAOZONG',
              dimensionType: dimensionType === 'productId' ? 1010 : 1202,
              sieveListType: 1,
              newSieveId: orderId,
            },
          ] : null,
          whitePropertyTypes: openAB == '1' ? [1010] : null // 开启AB必传
        },
        activityExtraInfoModel: {
          extraInfoMap: {
            promotionTheme: '-1',
            platformActiveType: '6',
            mappingMergeType: '1',
            dzBusiness: dzBusiness,
            bmlPriority: bmlPriority,
            cityIds: cityStr,
            openAB: openAB == '1', // 是否AB
            bidType: automationSubsidyAmount.autoPrice == '1' ? 0 : 1 // 0:规则出价 1:算法出价
          },
        },
        activityUserRuleInfoModel: {},
      },
    ],
    departmentTypeEnumSet: ['DAOZONG'],
    applyToolTypeEnum: 'PDA',
    activityOptSourceEnum: 'mktManagement',
  }
}

/**
 * 转化城市名
 */
export function changeCitiesValue(browseMtCityId, type) {
   return getCityInfo({
      cityInfo: browseMtCityId,
      cityQueryStrategy: type // "mtName" || "mtId"
  })
}

/**
 * 拆分字符串
 */

