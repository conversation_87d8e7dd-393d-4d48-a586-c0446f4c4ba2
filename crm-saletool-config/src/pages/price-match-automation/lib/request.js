import axios from 'axios'

/**
 * 针对 /meishi-gateway 路径相关的请求统一封装的解析逻辑
 */

const instance = axios.create({
  baseURL: process.env.VUE_APP_API_HOST || '',
  withCredentials: true,
})

instance.interceptors.request.use(config => {
  /* 有需要的话可以在这里统一为所有请求 添加参数 / Header 等 */
  return config
})

instance.interceptors.response.use(response => {
  if (!response || !response.data) {
    throw new Error('返回值为空')
  }

  const originData = response.data

  if (originData.resultCode !== 0) {
    throw new Error(originData.errorMsg || originData.msg || originData.message || '请求失败')
  }

  return originData
})

export default instance
