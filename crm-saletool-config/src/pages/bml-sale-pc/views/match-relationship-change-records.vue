<template>
  <div class="match-relationship-change-records-container">
    <div ref="ccc-container"></div>
  </div>
</template>
<script>
import psb from '@nibfe/platform-sdk'
import graphCfg from '@nibfe/ccc-lowcode-render'
import { REDIRECT_URL } from '@/lib/constants'
import { reportError, getUrlSearch } from '@/lib/utils'
import gateway from '@/lib/gateway'

export default {
  components: {},
  data() {
    return {
      pageMessage: '',
    }
  },
  mounted() {
    psb.config('1720610850dasp32', {
      masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
      redirectUrl: process.env.NODE_ENV === 'development' ? null : REDIRECT_URL,
    })
    graphCfg({
      el: this.$refs['ccc-container'],
      tenantId: 3,
      pageType: 'chart', // 图表类型
      remoteURL: process.env.VUE_APP_API_HOST,
      module: 'match-relationship-change-records',
      appEnv: process.env.VUE_APP_ENV,
      templateId: process.env.VUE_APP_ENV === 'production' ? 251 : 2360,
      // 上线前注释，使用灰度能力
      // 本地开发可开启在模版未下发的情况下拉取交互
      // modelId: process.env.VUE_APP_ENV === 'production' ? 367 : 1739,
      initParams: {
        terminal: 0, //来自于PC，必填
        planSource: 0, //非来自拜访创建的，建议填0
      },
      lib: {
        gateway,
        getUrlSearch,
        reportError,
      },
    }).catch(reportError)
  },
}
</script>

<style>
html,
body {
  padding: 0;
  margin: 0;
}
</style>

<style lang="scss" scoped>
.match-relationship-change-records-container {
  min-width: 1000px;
}
</style>
