<template>
  <div class="error-record-list-container">
    <div id="ele"></div>
  </div>
</template>
<script>
import psb from '@nibfe/platform-sdk'
import { REDIRECT_URL } from '@/lib/constants'
import graphCfg from '@nibfe/ccc-lowcode-render'
import gateway from '@/lib/gateway'
import { reportError, copyText, getUrlSearch } from '@/lib/utils'
import { LXUtils } from '@/lib/lx'

export default {
  components: {},
  data() {
    return {
      pageMessage: '',
    }
  },
  created() {
    psb.config('1704424623r45y1e', {
      masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
      redirectUrl: process.env.NODE_ENV === 'development' ? null : REDIRECT_URL,
    })
    graphCfg({
      el: '#ele',
      tenantId: 3,
      pageType: 'chart', // 图表类型
      remoteURL: process.env.VUE_APP_API_HOST,
      module: 'BML-sale-pc',
      appEnv: process.env.VUE_APP_ENV,
      templateId: process.env.VUE_APP_ENV === 'production' ? 230 : 2222,
      // 上线前注释，使用灰度能力
      // 本地开发可开启在模版未下发的情况下拉取交互
      modelId: process.env.VUE_APP_ENV === 'production' ? undefined : 1589,
      initParams: {
        terminal: 0, //来自于PC，必填
        planSource: 0, //非来自拜访创建的，建议填0
      },
      lib: {
        reportError,
        copyText,
        getUrlSearch,
        gateway,
        LXUtils: new LXUtils('gc_m', {
          cid: 'c_gc_m_uloim1yc',
          appnm: 'dp_apollo_pc',
          valLab: {
            custom: {
              source_from_pc: getUrlSearch()?.source || '',
            },
          },
        }),
      },
    }).then(params => {
      const { instance, eventBus } = params || {}
      this.$bus = eventBus
      eventBus.$on('toOut', res => {
        this.pageMessage = res
      })
    })
  },
  methods: {
    // sendMessage() {
    //   this.$bus.$emit('toInner', '我是来自外部的消息' + Math.random())
    // },
  },
}
</script>

<style lang="scss" scoped>
.error-record-list-container {
  min-width: 1100px;
}
</style>
