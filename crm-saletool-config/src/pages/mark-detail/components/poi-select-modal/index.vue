<template>
  <div>
    <mtd-modal v-bind="$attrs" title="更换匹配门店" @close="$emit('close')">
      <div class="top-area">
        <mtd-cascader
          v-model="selectedCity"
          :data="options"
          :load-data="loadDataHandler"
          placeholder="请选择省市"
        />
        <mtd-input v-model="keyword" placeholder="请输入我方门店名称/ID" />
        <mtd-button type="primary" @click="searchHandler">搜索</mtd-button>
      </div>
      <div class="main-area">
        <mtd-radio-group v-model="selectedShopId" class="shop-select">
          <mtd-radio
            v-for="{ mtPoiId, dpPoiId, poiName, address } in shopList"
            :key="mtPoiId"
            :value="mtPoiId"
            style="width: 100%"
          >
            <div class="shop-list">
              <div>
                <div>
                  <span class="ahead-info">{{ poiName }}</span>
                  <span class="second-info ahead-info"
                    >我方ID：{{ isWb ? encryptPoiId(mtPoiId) : mtPoiId }}</span
                  >
                  <span class="second-info"
                    >dpID：{{ isWb ? encryptPoiId(dpPoiId) : dpPoiId }}</span
                  >
                </div>
                <div>
                  <span class="second-info"> 地址：{{ address }} </span>
                </div>
              </div>
              <div v-if="!isWb">
                <a @click="jumpToShopDetail(dpPoiId)">去查看</a>
              </div>
            </div>
          </mtd-radio>
          <mtd-radio :value="-1"> 无匹配门店 </mtd-radio>
        </mtd-radio-group>
      </div>
      <div slot="footer" class="bottom-area">
        <mtd-button @click="$emit('close')">取消</mtd-button>
        <mtd-button type="primary" :disabled="!selectedShopId" @click="confirmHandler"
          >确认更换</mtd-button
        >
      </div>
    </mtd-modal>
    <mtd-modal
      v-model="changeShopHintVisible"
      title="系统提示"
      width="400px"
      @close="changeShopHintVisible = false"
    >
      <div>
        变更门店匹配关系后，不可再对本单进行商品标注，本次修改的内容也会还原至下发标注单时的数据，是否确认变更
      </div>
      <template #footer>
        <div>
          <mtd-button @click="changeShopHintVisible = false">取消</mtd-button
          ><mtd-button type="primary" @click="confirmChangeShop">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>
  </div>
</template>

<script>
import { errorHandler, encryptPoiId } from '@/lib/utils'
import API from '@/api/mark-detail'
import { BIZ_LINE, ADDRESS_TYPE } from '@/lib/constants'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dpProvinceId: {
      type: Number,
      default: null,
    },
    dpCityId: {
      type: Number,
      default: null,
    },
    isWb: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selectedCity: [],
      options: [],
      selectedShopId: null,
      shopList: [],
      keyword: '',
      changeShopHintVisible: false,
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.selectedCity = []
        this.shopList = []
        this.selectedShopId = null
        this.chooseDefaultCity()
      }
    },
  },
  mounted() {
    const params = {
      level: ADDRESS_TYPE.PROVINCE,
      bizLine: BIZ_LINE.ZONG,
    }
    API.queryAddress(params)
      .then(data => {
        this.options = (data ?? []).map(({ id, name }) => ({
          value: id,
          label: name,
          isLeaf: false,
        }))
      })
      .catch(errorHandler)
  },
  methods: {
    encryptPoiId,
    chooseDefaultCity() {
      if (this.dpProvinceId && this.dpCityId) {
        const params = {
          level: ADDRESS_TYPE.CITY,
          id: this.dpProvinceId,
          bizLine: BIZ_LINE.ZONG,
        }
        API.queryAddress(params)
          .then(data => {
            let cityList = (data ?? []).map(({ id, name }) => ({
              value: id,
              label: name,
              isLeaf: true,
            }))
            const provinceNode = this.options.filter(item => item.value === this.dpProvinceId)[0]
            if (provinceNode) {
              provinceNode.children = cityList
              this.selectedCity = [this.dpProvinceId, this.dpCityId]
            }
          })
          .catch(errorHandler)
      }
    },
    searchHandler() {
      if (!this.selectedCity || this.selectedCity.length < 2) {
        this.$mtd.message.error('请先选择省市')
        return
      }
      API.searchPoiByCondition({
        searchKeyword: this.keyword?.trim(),
        cityId: this.selectedCity?.[1],
      })
        .then(data => {
          this.shopList = data
        })
        .catch(errorHandler)
    },
    loadDataHandler(node, callback) {
      const params = {
        level: ADDRESS_TYPE.CITY,
        id: node.value,
        bizLine: BIZ_LINE.ZONG,
      }
      API.queryAddress(params)
        .then(list => {
          callback(
            (list ?? []).map(item => ({
              label: item.name,
              value: item.id,
              isLeaf: true,
            })),
          )
        })
        .catch(errorHandler)
    },
    confirmHandler() {
      this.changeShopHintVisible = true
    },
    confirmChangeShop() {
      this.changeShopHintVisible = false
      this.$emit('confirm', this.selectedShopId)
    },
    jumpToShopDetail(dpPoiId) {
      if (!dpPoiId) return
      window.open(`/shop/view?shopId=${dpPoiId}`)
    },
  },
}
</script>

<style lang="less" scoped>
.second-info {
  color: rgba(107, 107, 107, 0.486);
}

.top-area {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.main-area {
  .shop-select {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .ahead-info {
      margin-right: 8px;
    }

    .shop-list {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
