<template>
  <div class="basic-info-container">
    <div>下发来源：{{ SOURCE_LABEL_ENUMS[poiInfo.source] || '' }}</div>
    <div class="right-info">
      <div class="info-line">
        <div class="operate-info">
          <div class="info-item">
            标注状态：{{ ANNOTATION_STATUS_LABEL_ENUMS[poiInfo.annotationStatus] || '' }}
          </div>
          <div class="info-item">
            标注员：{{ annOperatorName || '' }}/{{ annOperatorMis || '' }}
          </div>
          <div class="info-item">标注提交时间：{{ annFinishTime || '-' }}</div>
          <div>测评类型：{{ EVALUATION_TYPE_MAP[poiInfo.source] || '-' }}</div>
        </div>
        <div v-if="isShowMarkLog" class="log-link" @click="toMarkLog">
          标注操作日志 ({{ markCount || '' }})
        </div>
      </div>
      <div v-if="isInspect" class="info-line">
        <div class="operate-info">
          <div class="info-item">
            质检状态：{{ INSPECRTION_STATUS_LABEL_ENUMS[poiInfo.inspectionStatus] || '' }}
          </div>
          <div class="info-item">
            质检员：{{ poiInfo.operatorName || '' }}/{{ poiInfo.operatorMis || '' }}
          </div>
          <div class="info-item">质检提交时间：{{ poiInfo.finishTime || '-' }}</div>
          <div>测评类型：{{ annFinishTime || '-' }}</div>
        </div>
        <div v-if="isShowInspectionLog" class="log-link" @click="toInspectionLog">
          质检操作日志 ({{ inspectionCount || '' }})
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  BIZ_LINE,
  SOURCE_LABEL_ENUMS,
  ANNOTATION_STATUS,
  ANNOTATION_STATUS_LABEL_ENUMS,
  INSPECRTION_STATUS,
  INSPECRTION_STATUS_LABEL_ENUMS,
  EVALUATION_TYPE_MAP,
} from '@/lib/constants'
import API from '@/api/mark-detail'
export default {
  props: {
    poiInfo: {
      type: Object,
      default: () => ({}),
    },
    isInspect: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      SOURCE_LABEL_ENUMS,
      ANNOTATION_STATUS_LABEL_ENUMS,
      INSPECRTION_STATUS_LABEL_ENUMS,
      EVALUATION_TYPE_MAP,
      markCount: 0, // 标注日志总量
      inspectionCount: 0, // 质检日志总量
    }
  },
  computed: {
    annOperatorName() {
      return this.isInspect ? this.poiInfo.annotationInfo?.operatorName : this.poiInfo.operatorName
    },
    annOperatorMis() {
      return this.isInspect ? this.poiInfo.annotationInfo?.operatorMis : this.poiInfo.operatorMis
    },
    annFinishTime() {
      return this.isInspect ? this.poiInfo.annotationInfo?.finishTime : this.poiInfo.finishTime
    },
    isShowMarkLog() {
      return (
        (this.isInspect && this.poiInfo.annotationInfo) ||
        (!this.isInspect && this.poiInfo.annotationStatus === ANNOTATION_STATUS.FINISH)
      )
    },
    isShowInspectionLog() {
      return (
        this.isInspect &&
        (this.poiInfo.inspectionStatus === INSPECRTION_STATUS.PASS ||
          this.poiInfo.inspectionStatus === INSPECRTION_STATUS.REJECT)
      )
    },
    annId() {
      return this.isInspect ? this.poiInfo.annotationInfo?.id : this.poiInfo.id
    },
    inspectId() {
      return this.isInspect ? this.poiInfo.id : null
    },
  },
  watch: {
    async isShowMarkLog(val) {
      if (val) {
        const mark = await this.queryLogCount(1, this.annId)
        this.markCount = mark?.data
      }
    },
    async isShowInspectionLog(val) {
      if (val) {
        const inspection = await this.queryLogCount(2, this.inspectId)
        this.inspectionCount = inspection?.data
      }
    },
  },
  methods: {
    // 操作日志接口请求
    queryLogCount(type, id) {
      if (!id) return
      return API.queryCount({
        type: type,
        annInsId: id,
        bizLine: BIZ_LINE.ZONG,
      })
    },
    toMarkLog() {
      let href = ''
      href = `/crm-saletool-config/operate-log/index.html#/log-list?annInsId=${this.annId}&type=1`
      window.open(href, '_blank')
    },
    toInspectionLog() {
      let href = ''
      href = `/crm-saletool-config/operate-log/index.html#/log-list?annInsId=${this.inspectId}&type=2`
      window.open(href, '_blank')
    },
  },
}
</script>

<style lang="scss" scoped>
.basic-info-container {
  display: flex;
  padding: 10px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  .right-info {
    flex: 1;
    margin-left: 100px;
    .info-line {
      display: flex;
      justify-content: space-between;
      .operate-info {
        display: flex;
        .info-item {
          margin-right: 30px;
          min-width: 120px;
        }
      }
      .log-link {
        color: #2a8efe;
        margin-left: 20px;
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
