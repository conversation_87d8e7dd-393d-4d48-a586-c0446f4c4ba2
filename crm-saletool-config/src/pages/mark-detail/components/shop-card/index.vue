<template>
  <div>
    <div v-if="noShop" class="no-shop-container">
      <div class="middle-area">
        <span>门店待开发</span>
      </div>
      <div class="bottom-area">
        <mtd-button
          v-if="isMT"
          :disabled="!(editable && comPoiComparable)"
          size="small"
          type="primary"
          @click="selectModalVisible = true"
          >更换匹配门店</mtd-button
        >
      </div>
    </div>
    <div v-else class="container" :class="{ 'highligth-container': shopInfo.needHighlight }">
      <div class="left-area">
        <img class="preview" src="@/assets/daozong.png" alt="图片丢失" />
      </div>
      <div class="right-area">
        <div class="top-area">
          <div class="title">
            {{ shopInfo.title }}
          </div>
          <div class="extra-area">
            <slot name="extra">
              <mtd-popover v-if="isMT && shopInfo.dpPoiId" trigger="hover" placement="top">
                <mtd-button type="text" style="align-items: start"> 查看二维码 </mtd-button>
                <div slot="content">
                  <div style="display: flex">
                    <div
                      class="main-station-jumper-container"
                      style="padding-right: 10px; border-right: 1px solid #eee"
                    >
                      <img :src="imageSource('dianping')" alt="" />
                      <div>请使用点评 App 扫码查看</div>
                      <mtd-button style="background: #ee5511" @click="toCode('dianping')">
                        查看电脑端
                      </mtd-button>
                    </div>
                    <div class="main-station-jumper-container" style="padding-left: 10px">
                      <img :src="imageSource('meituan')" alt="" />
                      <div>请使用美团 App 扫码查看</div>
                      <mtd-button style="background: #00b495" @click="toCode('meituan')">
                        查看电脑端
                      </mtd-button>
                    </div>
                  </div>
                </div>
              </mtd-popover>
            </slot>
            <slot name="extra">
              <a
                v-if="isMT && !isWb && shopInfo.dpPoiId"
                target="_blank"
                :href="`/shop/view?shopId=${shopInfo.dpPoiId}`"
                >查看门店详情</a
              >
            </slot>
          </div>
        </div>
        <div class="middle-area">
          <div class="info-list">
            <div v-for="(item, index) in shopInfo.infoList" :key="index" class="info-item">
              <span v-if="item.title" class="info-title">{{ item.title }}</span>
              <span class="info-content">{{ item.content }}</span>
            </div>
          </div>
          <div v-if="!isMT" style="margin-top: 12px">
            <span>行业ID：{{ shopInfo.comPoiId }}</span>
          </div>
        </div>
        <div class="bottom-area">
          <slot name="bottom">
            <div class="check-result">
              <mtd-select
                v-model="shopStatus"
                size="small"
                style="width: 260px"
                :disabled="!(editable && comPoiComparable)"
                @change="changeShopStatus"
              >
                <mtd-option
                  v-for="item in shopStatusOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </mtd-select>
              <mtd-textarea
                v-if="isMT && shopStatus === 2"
                v-model="shopDesc"
                :disabled="!(editable && comPoiComparable)"
                size="small"
                placeholder="请输入具体原因，最多支持输入200字"
                :max-length="200"
                show-count
                @input="changeShopStatusDesc"
              />
            </div>
            <mtd-button
              v-if="isMT"
              :disabled="!(editable && comPoiComparable)"
              size="small"
              type="primary"
              style="margin-left: 5px"
              @click="selectModalVisible = true"
              >更换匹配门店</mtd-button
            >
          </slot>
        </div>
      </div>
    </div>
    <poi-select-modal
      v-if="isMT"
      :is-wb="isWb"
      :visible="selectModalVisible"
      :model-value="selectModalVisible"
      :dp-province-id="shopInfo.dpProvinceId"
      :dp-city-id="shopInfo.dpCityId"
      @recoverOriginProductInfo="recoverOriginProductInfo"
      @confirm="changePoiConfirmHandler"
      @close="selectModalVisible = false"
    />
    <mtd-modal
      v-model="incomparableHintVisible"
      title="系统提示"
      width="400px"
      @close="recoverShopStatus"
    >
      <div>
        变更行业门店状态为不可比后，不可再对本单进行商品标注，本次修改的内容也会还原至下发标注单时的数据，是否确认变更
      </div>
      <template #footer>
        <div>
          <mtd-button @click="recoverShopStatus">取消</mtd-button
          ><mtd-button type="primary" @click="confirmChangeShopStatus">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>
  </div>
</template>

<script>
import qrCode from 'qrcode-js'
import { SHOP_STATUS_OPTIONS, MT_SHOP_STATUS_OPTIONS } from '@/lib/constants'
import PoiSelectModal from '../poi-select-modal'

export default {
  components: { PoiSelectModal },
  props: {
    noShop: {
      type: Boolean,
      default: false,
    },
    shopInfo: {
      type: Object,
      default: () => ({}),
    },
    editable: {
      type: Boolean,
      default: true,
    },
    comPoiComparable: {
      type: Boolean,
      default: true,
    },
    isMT: {
      type: Boolean,
      default: false,
    },
    isWb: {
      type: Boolean,
      default: false,
    },
    poiAnnotationExt: {
      type: Object,
      default: () => ({}),
    },
    mtPoiExt: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      selectModalVisible: false,
      shopStatus: 0,
      shopDesc: '',
      incomparableHintVisible: false,
    }
  },
  computed: {
    shopStatusOptions() {
      return this.isMT ? MT_SHOP_STATUS_OPTIONS : SHOP_STATUS_OPTIONS
    },
  },
  watch: {
    poiAnnotationExt: {
      immediate: true,
      deep: true,
      handler() {
        if (this.isMT || !this.poiAnnotationExt) return
        if (this.poiAnnotationExt.comPoiComparable) this.shopStatus = 0
        else if (this.poiAnnotationExt.comPoiNotComparableReason) {
          this.shopStatus = this.poiAnnotationExt.comPoiNotComparableReason
        } else {
          this.shopStatus = 0
        }
        this.updateShopStatus()
      },
    },
    mtPoiExt: {
      immediate: true,
      deep: true,
      handler() {
        if (!this.isMT || !this.mtPoiExt) return
        this.shopStatus = this.mtPoiExt.mtPoiOnline
        this.shopDesc = this.mtPoiExt.mtPoiNotComparableReasonDesc
      },
    },
  },
  methods: {
    changeShopInfo(name, value) {
      this.$emit('changeShopInfo', name, value)
    },
    changePoiConfirmHandler(selectedShopId) {
      this.selectModalVisible = false
      this.$emit('changeMtShop', String(selectedShopId))
    },
    updateShopStatus() {
      this.changeShopInfo('comPoiNotComparableReason', this.shopStatus)
      this.changeShopInfo('comPoiComparable', this.shopStatus === 0)
    },
    updateMtShopStatus() {
      this.changeShopInfo('mtPoiOnline', this.shopStatus)
      if (this.shopStatus !== 2) {
        this.shopDesc = ''
        this.changeShopStatusDesc()
      }
    },
    changeShopStatus() {
      if (this.isMT) {
        this.updateMtShopStatus()
      } else if (this.shopStatus !== 0 && this.poiAnnotationExt.comPoiComparable !== false) {
        // 从可比状态变为不可比需要二次确认
        this.incomparableHintVisible = true
      } else {
        this.updateShopStatus()
      }
    },
    changeShopStatusDesc() {
      this.changeShopInfo('mtPoiNotComparableReasonDesc', this.shopDesc)
    },
    recoverShopStatus() {
      if (this.poiAnnotationExt.comPoiComparable) this.shopStatus = 0
      else this.shopStatus = this.poiAnnotationExt.comPoiNotComparableReason || 0
      this.incomparableHintVisible = false
    },
    confirmChangeShopStatus() {
      this.updateShopStatus()
      this.recoverOriginProductInfo()
      this.incomparableHintVisible = false
    },
    recoverOriginProductInfo() {
      this.$emit('recoverOriginProductInfo')
    },
    toCode(type) {
      if (type === 'dianping') {
        window.open(`https://www.dianping.com/shop/${this.shopInfo.dpPoiId}`, '_blank')
      } else if (type === 'meituan') {
        window.open(`https://i.meituan.com/poi/${this.shopInfo.dpPoiId}`, '_blank')
      }
    },
    imageSource(type) {
      if (type === 'dianping') {
        return qrCode.toDataURL(
          `https://evt.dianping.com/synthesislink/2419636.html?shopId=${this.shopInfo.dpPoiId}`,
        )
      } else {
        return qrCode.toDataURL(`https://i.meituan.com/poi/${this.shopInfo.dpPoiId}`)
      }
    },
  },
}
</script>

<style lang="less" scoped>
.no-shop-container {
  background-color: #fff;
  padding: 12px;
  > .middle-area {
    width: 100%;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #b7babd;
  }
  > .bottom-area {
    display: flex;
    flex-shrink: 0;
    flex-grow: 0;
    justify-content: flex-end;
  }
}
.container {
  display: grid;
  grid-template-columns: repeat(24, 1fr);
  column-gap: 12px;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 12px;

  > .left-area {
    grid-column: 1 / 6;
    overflow: hidden;

    > img.preview {
      width: 100%;
      //height: 100%;
      object-fit: contain;
    }
  }

  > .right-area {
    grid-column: 6 / 25;
    overflow: auto;
    position: relative;
    display: flex;
    flex-direction: column;

    > .top-area {
      flex-shrink: 0;
      flex-grow: 0;
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      > .title {
        font-size: 18px;
        font-weight: bold;
        flex-shrink: 1;
      }

      > .extra-area {
        margin-left: auto;
      }
    }

    > .middle-area {
      flex-shrink: 1;
      flex-grow: 1;
      margin-bottom: 12px;

      > .info-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;

        > .info-item {
          //color: gray;

          > .info-title {
            &::after {
              content: ':';
              margin-right: 8px;
            }
          }
        }
      }
    }

    > .bottom-area {
      display: flex;
      flex-shrink: 0;
      flex-grow: 0;
      justify-content: flex-end;
      .check-result {
        display: flex;
        flex-direction: column;
      }
    }
  }
}
.highligth-container {
  background-color: #fff9de;
}
.main-station-jumper-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 200px;
  padding: 20px;

  > img {
    width: 160px;
    height: 160px;
  }

  > div {
    margin-bottom: 8px;
  }

  > button {
    background-color: rgb(160, 160, 160);
    font-size: 12px;
    color: #eeeeee;
    width: 80px;
    height: 28px;
    padding: 0;
    line-height: 28px;
  }
}
</style>
