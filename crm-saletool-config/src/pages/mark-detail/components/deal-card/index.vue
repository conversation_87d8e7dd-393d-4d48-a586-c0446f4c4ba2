<template>
  <div class="deal-card-container">
    <div class="deal-img">
      <img class="preview" src="@/assets/daozong.png" alt="图片丢失" />
      <div class="deal-type-tag">
        <mtd-tag v-if="getSopTypeTag(dealInfo.sopType)" theme="yellow" type="pure" size="large">{{
          getSopTypeTag(dealInfo.sopType) + getSopPriorityTag(dealInfo.sopPriority)
        }}</mtd-tag>
      </div>
    </div>
    <div v-if="dealInfo" class="deal-info">
      <div class="top-area">
        <mtd-tag v-if="isMT" theme="yellow" type="pure">{{
          getMtProductTag(dealInfo.mtProductType)
        }}</mtd-tag>
        <span class="deal-name">{{ name }}</span>
      </div>
      <div class="middle-area">
        <div>{{ `${isMT ? '我方' : '行业'}deal ID：${id}` }}</div>
        <div class="price-denominator-info">
          <span v-if="!isMT" style="margin-right: 3px">
            参考供应链价：￥{{ dealInfo.comCommercialSubsidyPrice }}
          </span>
          <span v-else style="margin-right: 3px">
            参考供应链价（老客）：￥{{ dealInfo.mtCommercialSubsidyPrice }}
          </span>
          <span style="margin-right: 16px">{{ `门市价：¥${shopPrice}` }}</span>
          <div class="denominator-module">
            <span>{{ `deal分母：${denominatorOrigin || '-'}` }}</span>
            <mtd-button
              v-if="showDenominatorModify"
              class="modify-text-button"
              type="text"
              :disabled="!editable"
              @click="clickToChangeDenominator = true"
              >修改</mtd-button
            >
            <mtd-input-number
              v-if="showDenominatorModifyNum"
              v-model="denominator"
              :disabled="!editable"
              :controls="false"
              @input="changeDenominator"
            />
          </div>
        </div>
        <div class="price-denominator-info">
          <span v-if="!isMT" style="margin-right: 16px">
            参考到手价：￥{{ dealInfo.comUniversalStrikePrice }}
          </span>
          <template v-else>
            <span style="margin-right: 3px">
              参考供应链价（新客）：￥{{ dealInfo.mtWorkPrice }}
            </span>
            <span style="margin-right: 3px">
              参考到手价（老客）：￥{{ dealInfo.mtConsumerPriceOldUserAmount }}
            </span>
            <span style="margin-right: 3px">
              参考到手价（新客）：￥{{ dealInfo.mtConsumerPriceNewUserAmount }}
            </span>
          </template>
        </div>
        <div v-if="isMT && matchingRelationshipSource">
          <span> 匹配关系来源: {{ matchingRelationshipSource }} </span>
        </div>
        <div v-if="isMT">
          <mtd-checkbox
            v-if="showDisputedCaseCheckbox"
            v-model="disputedCase"
            :disabled="!editable"
            @change="changeDisputedCase"
            >争议案例</mtd-checkbox
          >
          <span v-else-if="!editable && disputedCase" class="disputed-case-text">争议案例</span>
        </div>
      </div>
      <div class="bottom-area">
        <div v-if="!isMT && mtCat2IdIsOrderGoods && isDeal" class="bottom-dp">
          <div>
            <span style="margin-right: 5px">匹配的美团商品是否为预订商品</span>
            <mtd-radio-group v-model="orderGoods" :disabled="!editable" @change="radioChange">
              <mtd-radio :value="1">是</mtd-radio>
              <mtd-radio :value="0">否</mtd-radio>
            </mtd-radio-group>
          </div>
          <div v-if="orderGoods === 1" style="margin-top: 10px">
            <span style="margin-right: 5px">更改BML结果</span>
            <mtd-select
              v-model="orderBmlResult"
              size="small"
              style="width: 180px"
              :disabled="!editable"
              @change="changeBmlResult"
            >
              <mtd-option
                v-for="item in shopStatusOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </mtd-select>
          </div>
        </div>
        <div class="check-result">
          <div class="select-wrap">
            <div style="flex: 1; text-align: right">
              <mtd-select
                v-if="!isMT"
                v-model="dealStatus"
                :disabled="!editable"
                size="small"
                style="width: 260px; margin-bottom: 8px"
                @change="changeDealStatus"
              >
                <mtd-option
                  v-for="item in dealStatusOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </mtd-select>
              <mtd-textarea
                v-if="!isMT && dealStatus === 2"
                v-model="dealDesc"
                :disabled="!editable"
                size="small"
                placeholder="请输入具体原因，最多支持输入200字"
                :max-length="200"
                show-count
                @input="changeDealStatusDesc"
              />
              <mtd-select
                v-if="isMT"
                v-model="productOnline"
                :disabled="!editable"
                size="small"
                style="width: 260px; margin-bottom: 8px"
                placeholder="在线状态"
                @change="changeMtDealStatus"
              >
                <mtd-option
                  v-for="item in mtdealStatusOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </mtd-select>
            </div>
            <mtd-button
              v-if="canChange"
              size="small"
              type="primary"
              class="change"
              @click="changeUnselectComPorduct"
              >更换匹配友商商品</mtd-button
            >
          </div>
        </div>
        <mtd-button
          v-if="isMT && canRemove"
          size="small"
          type="primary"
          style="margin-left: 5px"
          :disabled="!editable"
          @click="removeFromGroup"
          >移除匹配关系</mtd-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import {
  DEAL_STATUS_OPTIONS,
  MT_DEAL_STATUS_OPTIONS,
  SOP_TYPE_ENUMS,
  SOURCE_ENUMS,
  MT_PRODUCT_TYPE,
  BOOK_STATUS_OPTIONS,
  MATCHSOURCE_ENUMS,
  MATCHSOURCE_LABEL_ENUMS,
} from '@/lib/constants'

export default {
  props: {
    dealInfo: {
      type: Object,
      default: () => ({}),
    },
    editable: {
      type: Boolean,
      default: true,
    },
    isMT: {
      type: Boolean,
      default: false,
    },
    isHigh: {
      type: Boolean,
      default: true,
    },
    source: {
      type: Number,
      default: null,
    },
    isInspect: {
      type: Boolean,
      default: false,
    },
    canRemove: {
      type: Boolean,
      default: true,
    },
    canChange: {
      type: Boolean,
      default: false,
    },
    unselectedItemIndex: {
      type: Number,
      default: 0,
    },
    canModifyNum: {
      type: Boolean,
      default: true,
    },
    mtCat2IdIsOrderGoods: {
      type: Boolean,
      default: false,
    },
    isDeal: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      name: '',
      id: '',
      shopPrice: '',
      denominator: '',
      denominatorOrigin: '',
      matchingRelationshipSource: '',
      dealStatusOptions: DEAL_STATUS_OPTIONS,
      dealStatus: 0,
      orderGoods: null,
      dealDesc: '',
      clickToChangeDenominator: false,
      disputedCase: false, // 争议案例
      productOnline: '', // 在线状态
      mtdealStatusOptions: MT_DEAL_STATUS_OPTIONS,
      orderBmlResult: '',
      shopStatusOptions: BOOK_STATUS_OPTIONS,
    }
  },
  computed: {
    showDenominatorModify() {
      return (
        this.editable &&
        this.canModifyNum &&
        this.denominator === this.denominatorOrigin &&
        !this.clickToChangeDenominator
      )
    },
    showDenominatorModifyNum() {
      return (
        (this.editable && this.canModifyNum && !this.showDenominatorModify) ||
        (!this.editable && this.denominator !== this.denominatorOrigin)
      )
    },
    showDisputedCaseCheckbox() {
      return this.editable && (this.isInspect || this.source === SOURCE_ENUMS.ALGORITHM_EVALUATION)
    },
  },
  watch: {
    dealInfo: {
      immediate: true,
      deep: true,
      handler() {
        this.setBasicProductInfo()
        this.handleOrderGoods()
        this.setMatchingRelationshipSource()
        this.handleDenominatorValues()
        this.setProductComparableStatus()
        this.setAdditionalProperties()
      },
    },
    isDeal: {
      immediate: false,
      handler() {
        this.orderGoods = null
        this.orderBmlResult = null
        this.changeDealInfo('orderGoods', this.orderGoods)
        this.changeDealInfo('orderBmlResult', this.orderBmlResult)
      },
    },
  },
  methods: {
    // 设置商品基本信息
    setBasicProductInfo() {
      this.name = this.isMT ? this.dealInfo.mtProductName : this.dealInfo.comProductName
      this.id = this.isMT ? this.dealInfo.dpProductId : this.dealInfo.comProductId
      this.shopPrice = this.isMT ? this.dealInfo.mtMarketPrice : this.dealInfo.comMarketPrice
    },
    // 处理订单商品逻辑
    handleOrderGoods() {
      this.orderGoods =
        this.mtCat2IdIsOrderGoods && this.dealInfo.orderGoods === null && this.isDeal && !this.isMT
          ? this.isHigh
            ? this.dealInfo.orderGoods
            : 0
          : this.dealInfo.orderGoods
      this.orderBmlResult = this.dealInfo.orderBmlResult
      if (!this.isHigh) {
        this.changeDealInfo('orderGoods', this.orderGoods)
      }
    },
    // 设置匹配关系来源
    setMatchingRelationshipSource() {
      this.matchingRelationshipSource = ''
      const { matchSource = null, matchSourceExt = null, isAdd = false } = this.dealInfo
      if (!isAdd && matchSource) {
        switch (matchSource) {
          case MATCHSOURCE_ENUMS.APPEAL:
          case MATCHSOURCE_ENUMS.REPORT_ERROR:
            this.matchingRelationshipSource = `${MATCHSOURCE_LABEL_ENUMS[matchSource]}${
              matchSourceExt ? `（报错ID：${matchSourceExt}）` : ''
            }`
            break
          case MATCHSOURCE_ENUMS.ANNOTATION_CHANGE:
          case MATCHSOURCE_ENUMS.ANNOTATION_NOT_CH:
            this.matchingRelationshipSource = `${MATCHSOURCE_LABEL_ENUMS[matchSource]}${
              matchSourceExt ? `（ID：${matchSourceExt}）` : ''
            }`
            break
          case MATCHSOURCE_ENUMS.ALGORITHMIC_EVALUATION:
            break
          default:
            this.matchingRelationshipSource = MATCHSOURCE_LABEL_ENUMS[matchSource] || ''
            break
        }
      }
    },
    // 处理分母原始值和当前值
    handleDenominatorValues() {
      if (this.isMT && !this.dealInfo.hasOwnProperty('mtProductGroupDealNumOrigin')) {
        this.denominatorOrigin = this.dealInfo.mtProductGroupDealNum
        this.changeDealInfo('mtProductGroupDealNumOrigin', this.denominatorOrigin)
      } else if (!this.isMT && !this.dealInfo.hasOwnProperty('comProductGroupDealNumOrigin')) {
        this.denominatorOrigin = this.dealInfo.comProductGroupDealNum
        this.changeDealInfo('comProductGroupDealNumOrigin', this.denominatorOrigin)
      }

      this.denominatorOrigin = this.isMT
        ? this.dealInfo.mtProductGroupDealNumOrigin
        : this.dealInfo.comProductGroupDealNumOrigin
      this.denominator = this.isMT
        ? this.dealInfo.mtProductGroupDealNum
        : this.dealInfo.comProductGroupDealNum
    },
    // 设置商品可比状态
    setProductComparableStatus() {
      if (this.dealInfo.comProductComparable) {
        this.dealStatus = 0
      } else if (this.dealInfo.comProductNotComparableReason) {
        this.dealStatus = this.dealInfo.comProductNotComparableReason
      } else {
        this.dealStatus = 0
        this.changeDealStatus()
      }
      this.dealDesc = this.dealInfo.comProductNotComparableReasonDesc
    },
    // 设置其他属性
    setAdditionalProperties() {
      this.disputedCase = this.dealInfo.disputedCase
      if (this.isMT) this.productOnline = this.dealInfo.mtProductOnline
    },
    changeUnselectComPorduct() {
      this.$emit('changeUnselectComProduct', this.unselectedItemIndex)
    },
    changeDealInfo(name, value) {
      this.$emit('changeDealInfo', name, value)
    },
    // 联动修改页面上所有相同deal的目标属性
    changeDealLinkInfo({ isMT, dealId, name, value }) {
      this.$emit('changeDealLinkInfo', { isMT, dealId, name, value })
    },
    changeDealStatus() {
      this.changeDealInfo('comProductNotComparableReason', this.dealStatus)
      this.changeDealInfo('comProductComparable', this.dealStatus === 0)
      if (this.dealStatus === 0) {
        this.dealDesc = ''
        this.changeDealStatusDesc()
      }
    },
    changeBmlResult() {
      this.changeDealInfo('orderBmlResult', this.orderBmlResult)
    },
    radioChange() {
      this.changeDealInfo('orderBmlResult', null)
      this.changeDealInfo('orderGoods', this.orderGoods)
    },
    changeMtDealStatus() {
      this.changeDealInfo('mtProductOnline', this.productOnline)
      this.changeDealLinkInfo({
        isMT: this.isMT,
        dealId: this.id,
        name: 'mtProductOnline',
        value: this.productOnline,
      })
    },
    changeDenominator() {
      this.changeDealInfo(
        this.isMT ? 'mtProductGroupDealNum' : 'comProductGroupDealNum',
        this.denominator,
      )
      if (this.isMT) {
        this.changeDealLinkInfo({
          isMT: this.isMT,
          dealId: this.id,
          name: 'mtProductGroupDealNum',
          value: this.denominator,
        })
      } else {
        this.changeDealInfo('comProductGroupDealNum', this.denominator)
      }
    },
    changeDealStatusDesc() {
      this.changeDealInfo('comProductNotComparableReasonDesc', this.dealDesc)
    },
    removeFromGroup() {
      this.$emit('removeFromGroup')
    },
    changeDisputedCase(disputedCase) {
      this.changeDealInfo('disputedCase', disputedCase)
    },
    getSopTypeTag(sopType) {
      let tag = ''
      switch (sopType) {
        case SOP_TYPE_ENUMS.PARITY:
          tag = '价格高优'
          break
        case SOP_TYPE_ENUMS.CHASE_ORDER:
          tag = '供给高优'
          break
      }
      return tag
    },
    getMtProductTag(productType) {
      return MT_PRODUCT_TYPE.get(productType)
    },
    getSopPriorityTag(sopPriority) {
      let tag = ''
      if (sopPriority || sopPriority === 0) {
        tag = ` P${sopPriority}`
      }
      return tag
    },
  },
}
</script>

<style lang="scss" scoped>
.deal-card-container {
  display: grid;
  grid-template-columns: repeat(24, 1fr);
  column-gap: 12px;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 12px;
  .deal-img {
    grid-column: 1 / 6;
    overflow: hidden;
    position: relative;

    > img.preview {
      width: 100%;
      //height: 100%;
      object-fit: contain;
    }

    .deal-type-tag {
      position: absolute;
      left: 5px;
      top: 5px;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
  }
  .deal-info {
    grid-column: 6 / 25;
    overflow: auto;
    position: relative;
    display: flex;
    flex-direction: column;
    .top-area {
      display: flex;
      justify-content: flex-start;
      .deal-name {
        font-weight: bold;
        font-size: 15px;
        margin-bottom: 12px;
      }
    }
    .middle-area {
      display: grid;
      gap: 12px;
      margin-bottom: 12px;
      .price-denominator-info {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
      }
      .denominator-module {
        display: inline-block;
        .modify-text-button {
          height: 20px;
          color: #0a70f5;
          :hover {
            color: #2a8efe;
          }
        }
        .mtd-input-number-wrapper {
          width: 50px;
          height: 24px;
          margin-left: 8px;
        }
      }
      .disputed-case-text {
        font-weight: 600;
        color: red;
      }
    }
    .bottom-area {
      display: flex;
      flex-shrink: 0;
      flex-grow: 0;
      justify-content: flex-end;
      .bottom-dp {
        flex: 1;
      }
      .check-result {
        display: flex;
        flex-direction: column;
      }
      .select-wrap {
        display: flex;
      }
      .change {
        width: 114px;
        margin-left: 8px;
        margin-bottom: 8px;
      }
    }
  }
}
</style>
