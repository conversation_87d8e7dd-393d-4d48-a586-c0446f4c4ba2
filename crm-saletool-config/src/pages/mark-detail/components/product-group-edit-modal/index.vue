<template>
  <mtd-modal
    v-bind="$attrs"
    :title="`${productGroupName ? '编辑' : '创建'}商品组`"
    @close="$emit('close')"
  >
    <span>商品组名：</span>
    <mtd-input v-model="newName" placeholder="请输入商品组名称" />
    <div slot="footer" class="bottom-area">
      <mtd-button @click="$emit('close')">取消</mtd-button>
      <mtd-button
        type="primary"
        :disabled="!newName || newName === productGroupName"
        @click="confirmHandler"
        >确认</mtd-button
      >
    </div>
  </mtd-modal>
</template>

<script>
export default {
  props: {
    productGroupName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      newName: '',
    }
  },
  watch: {
    productGroupName: {
      immediate: true,
      handler() {
        this.newName = this.productGroupName
      },
    },
  },
  methods: {
    confirmHandler() {
      this.$emit('confirm', this.newName)
    },
  },
}
</script>
