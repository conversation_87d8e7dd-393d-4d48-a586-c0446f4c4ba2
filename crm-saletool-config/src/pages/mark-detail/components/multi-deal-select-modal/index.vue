<template>
  <mtd-modal v-bind="$attrs" :title="title" @close="$emit('close')">
    <div class="top-area">
      <mtd-input v-model="keyword" placeholder="请输入deal名称" />
      <mtd-button type="primary" @click="searchHandler">搜索</mtd-button>
    </div>
    <div class="main-area">
      <mtd-checkbox-group v-model="selectedDealIds" class="deal-select">
        <mtd-checkbox
          v-for="dealItem in dealList"
          :key="isMT ? dealItem.dpDealId : dealItem.comProductId"
          :value="isMT ? dealItem.dpDealId : dealItem.comProductId"
          :disabled="dealItem.disabled"
          style="width: 100%"
        >
          <div class="deal-list">
            <div>
              <div>
                <mtd-tag
                  v-if="dealItem.dealType"
                  style="margin-right: 5px"
                  theme="yellow"
                  type="pure"
                  >{{ getMtProductTag(dealItem.dealType) }}</mtd-tag
                >
                <span class="ahead-info">{{
                  isMT ? dealItem.dealName : dealItem.comProductName
                }}</span>
                <mtd-tag v-if="dealItem.isChoose" :theme="dealItem.color" type="pure">{{
                  dealItem.chooseTag
                }}</mtd-tag>
              </div>
              <div>
                <span v-if="isMT" class="second-info ahead-info">
                  我方ID：{{ dealItem.dpDealId }}
                </span>
                <span v-else class="second-info ahead-info">
                  行业ID：{{ dealItem.comProductId }}
                </span>
                <span v-if="isMT" class="second-info ahead-info">
                  参考供应链价（老客）：￥{{ dealItem.mtCommercialSubsidyPrice }}
                </span>
                <span v-if="isMT" class="second-info ahead-info">
                  参考供应链价（新客）：￥{{ dealItem.mtWorkPrice }}
                </span>
                <span v-if="isMT" class="second-info ahead-info">
                  参考到手价（老客）：￥{{ dealItem.mtConsumerPriceOldUserAmount }}
                </span>
                <span v-if="isMT" class="second-info ahead-info">
                  参考到手价（新客）：￥{{ dealItem.mtConsumerPriceNewUserAmount }}
                </span>
                <span v-if="!isMT" class="second-info ahead-info red">
                  售价：¥{{ dealItem.comSalePrice }}
                </span>
                <span class="second-info">
                  门市价：¥{{ isMT ? dealItem.marketPrice : dealItem.comMarketPrice }}
                </span>
              </div>
            </div>
            <div v-if="isMT && dealItem.dealType === 1">
              <a @click="jumpToDealDetail(dealItem.dpDealId)">去查看</a>
            </div>
          </div>
        </mtd-checkbox>
      </mtd-checkbox-group>
    </div>
    <div slot="footer" class="bottom-area">
      <mtd-button @click="$emit('close')">取消</mtd-button>
      <mtd-button type="primary" @click="confirmHandler">确认</mtd-button>
    </div>
  </mtd-modal>
</template>

<script>
import { errorHandler, getUrlSearch } from '@/lib/utils'
import { BIZ_LINE, MT_PRODUCT_TYPE } from '@/lib/constants'
import API from '@/api/mark-detail'
const OPERATE_TYPE = { MARK: 'mark', INSPECTION: 'inspection' }

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    poiId: {
      type: String,
      default: '',
    },
    dateKey: {
      type: Number,
      default: null,
    },
    isMT: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '更换匹配deal',
    },
    disabledDealList: {
      type: Array,
      default: () => [],
    },
    mtInPairDealList: {
      type: Array,
      default: () => [],
    },
    comInPairDealList: {
      type: Array,
      default: () => [],
    },
    mtSopDealList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      allDealList: [],
      allSelectableDealList: [],
      selectedDealIds: [],
      selectedDeals: [],
      dealList: [],
      keyword: '',
    }
  },
  watch: {
    async visible(val) {
      if (val) {
        await this.getAllSelectableDealList()
        this.dealList = this.allSelectableDealList
        this.selectedDealIds = [] // 默认置空
        this.selectedDeals = []
      }
    },
    selectedDealIds: {
      deep: true,
      handler(val) {
        const selectedDeals = []
        ;(val || []).forEach(selectedDealId => {
          let selectedDeal =
            (this.dealList || []).find(
              item => item[this.isMT ? 'dpDealId' : 'comProductId'] === selectedDealId,
            ) || {}
          selectedDeals.push(selectedDeal)
        })
        this.selectedDeals = selectedDeals
      },
    },
  },
  methods: {
    // 获取所有可选择dealList
    async getAllSelectableDealList() {
      if (!this.poiId) return
      this.isMT ? await this.getALLMtDealList() : await this.getALLComDealList()
      // 已经在匹配中的添加标签
      this.allSelectableDealList = this.allDealList
      const listKey = this.isMT ? 'dpProductId' : 'comProductId'
      const key = this.isMT ? 'dpDealId' : 'comProductId'
      const inPairDealList = this.isMT ? this.mtInPairDealList : this.comInPairDealList
      this.allSelectableDealList.map(item => {
        if (
          inPairDealList.findIndex(inPairDealItem => inPairDealItem[listKey] === item[key]) !== -1
        ) {
          // 已经在匹配中，添加匹配中标签
          item.isChoose = true
          item.chooseTag = '匹配中'
          item.disabled = false
          item.color = 'blue'
        } else {
          item.isChoose = false
          item.chooseTag = ''
          item.disabled = false
        }
      })
    },
    // 如果是质检单传qualityInspectionId，标注单传manualAnnotationId
    adaptParamsId(params) {
      const queryParmas = getUrlSearch()
      if (queryParmas.type === OPERATE_TYPE.INSPECTION) {
        // 质检单传qualityInspectionId
        params.qualityInspectionId = queryParmas.markId
      } else {
        // 标注单传manualAnnotationId
        params.manualAnnotationId = queryParmas.markId
      }
    },
    // mt获取所有dealList
    async getALLMtDealList() {
      if (!this.poiId) return
      const params = {
        mtPoiId: this.poiId,
        bizLine: BIZ_LINE.ZONG,
      }
      // 如果是质检单传qualityInspectionId，标注单传manualAnnotationId
      this.adaptParamsId(params)
      await API.queryMtPoiDealDetail(params)
        .then(data => {
          this.allDealList = (data && data.dealList) || []
          this.mtSopDealList.forEach(sopDeal => {
            const targetDeal = this.allDealList.find(deal => {
              return (
                deal.dpDealId === sopDeal.dpProductId && deal.dealType === sopDeal.mtProductType
              )
            })
            targetDeal &&
              Object.assign(targetDeal, {
                sopType: sopDeal.sopType,
                sopPriority: sopDeal.sopPriority,
              })
          })
        })
        .catch(errorHandler)
    },
    // com获取所有dealList
    async getALLComDealList() {
      const params = {
        comPoiId: this.poiId,
        dateKey: this.dateKey,
        bizLine: BIZ_LINE.ZONG,
      }
      // 如果是质检单传qualityInspectionId，标注单传manualAnnotationId
      this.adaptParamsId(params)
      await API.queryComPoiDealDetail(params)
        .then(data => {
          this.allDealList = (data && data.comDealList) || []
        })
        .catch(errorHandler)
    },
    searchHandler() {
      const keyword = this.keyword?.trim()
      if (!keyword) {
        this.dealList = this.allSelectableDealList
        return
      }
      this.dealList = (this.allSelectableDealList || []).filter(item => {
        const name = item[this.isMT ? 'dealName' : 'comProductName']
        return name && name.includes(keyword)
      })
    },
    getMtProductTag(productType) {
      return MT_PRODUCT_TYPE.get(productType)
    },
    confirmHandler() {
      this.$emit('confirm', this.selectedDeals)
    },
    jumpToDealDetail(dpDealId) {
      if (!dpDealId) return
      window.open(`//t.dianping.com/deal/${dpDealId}`)
    },
  },
}
</script>

<style lang="less" scoped>
.second-info {
  color: rgba(107, 107, 107, 0.486);
}

.top-area {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.main-area {
  .deal-select {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .ahead-info {
      margin-right: 8px;
    }
    .red {
      color: red;
    }

    .deal-list {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
