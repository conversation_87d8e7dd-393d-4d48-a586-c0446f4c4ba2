<template>
  <div class="mark-operation-container">
    <div class="page-title-text">行业门店详情</div>
    <!-- 标注质检信息 -->
    <BasicInfo style="margin-bottom: 12px" :poi-info="poiInfo" :is-inspect="isInspect" />
    <!-- 门店信息 -->
    <div class="content-module">
      <div class="content-module-left">
        <div class="module-title module-title-flex">
          <div>行业门店</div>
          <div class="module-title-flex-item">D+测试分区: {{ poiInfo.djPoiDateKey || '--' }}</div>
        </div>
        <ShopCard
          :shop-info="comShopInfo"
          :poi-annotation-ext="poiAnnotationExt"
          :is-m-t="false"
          :editable="shopEditable"
          @changeShopInfo="changeComShopInfo"
          @recoverOriginProductInfo="recoverOriginProductInfo"
        ></ShopCard>
      </div>
      <div class="content-module-right">
        <div class="module-title module-title-flex">
          <div>我方门店</div>
          <div class="module-title-flex-item">MT测试分区: {{ poiInfo.mtPoiDateKey || '--' }}</div>
        </div>
        <ShopCard
          :shop-info="mtShopInfo"
          :mt-poi-ext="mtPoiExt"
          :is-m-t="true"
          :is-wb="isWb"
          :no-shop="!currentMtPoiId"
          :editable="shopEditable"
          :com-poi-comparable="poiAnnotationExt.comPoiComparable"
          @changeMtShop="changeMtShop"
          @changeShopInfo="changeMtShopInfo"
        ></ShopCard>
      </div>
    </div>
    <!-- 高销列表 -->
    <div>
      <div>
        <div class="content-module">
          <div class="content-module-left" style="padding-bottom: 0">
            <div class="module-title">高销列表</div>
          </div>
          <div class="content-module-right highsale" style="padding-bottom: 0">
            <div class="module-title">高销列表</div>
            <div class="module-unselected module-title" @click="showUnselectDrawer = true">
              我方无匹配关系高销团单({{ unselectList.length }})
            </div>
          </div>
        </div>
      </div>
      <div v-for="(item, index) in highSalelist" :key="item.comProduct[0].comProductId">
        <div class="content-module">
          <div class="content-module-left">
            <div class="deal-serial-number">{{ `deal${index + 1}` }}</div>
            <div class="product-group-container">
              <div class="group-deal-container">
                <DealCard
                  class="card-module"
                  :deal-info="item.comProduct[0]"
                  :is-m-t="false"
                  :is-high="true"
                  :mt-cat2-id-is-order-goods="poiInfo.mtCat2IdIsOrderGoods"
                  :is-deal="item.mtProduct.length === 0"
                  :editable="dealEditable"
                  @changeDealInfo="
                    (name, value) => {
                      item.comProduct[0][name] = value
                    }
                  "
                ></DealCard>
              </div>
            </div>
          </div>
          <div
            v-if="isNoMatchMtPoi || isChangedMtPoi"
            class="content-module-right"
            :class="{
              'top-no-padding': index !== 0,
              'bottom-no-padding': index !== highSalelist.length - 1,
            }"
          >
            <div class="no-match-module">
              <div v-if="index === 0">
                <span v-if="isNoMatchMtPoi">门店待开发，deal无法对比</span>
                <span v-else-if="isChangedMtPoi">查询到门店匹配关系更新，暂不可标注deal</span>
              </div>
            </div>
          </div>
          <div v-else class="content-module-right">
            <div
              v-if="item.mtProduct"
              class="deal-serial-number"
              style="flex-direction: row-reverse"
            >
              {{ `共${item.mtProduct.length || 0}对匹配关系` }}
            </div>
            <div class="product-group-container">
              <div class="group-deal-container">
                <DealCard
                  v-for="(mtDealItem, mtDealIndex) in item.mtProduct"
                  :key="mtDealItem.dpProductId"
                  class="card-module"
                  :deal-info="mtDealItem"
                  :is-m-t="true"
                  :editable="dealEditable"
                  :source="poiInfo.source"
                  :is-inspect="isInspect"
                  @changeDealInfo="
                    (name, value) => {
                      mtDealItem[name] = value
                    }
                  "
                  @removeFromGroup="
                    () => {
                      removeMtDealFromGroup({ type: 'highSalelist', index, mtDealIndex })
                    }
                  "
                  @changeDealLinkInfo="changeDealLinkInfo"
                ></DealCard>
              </div>
              <div class="operate-area">
                <mtd-button
                  type="text"
                  :disabled="!dealEditable"
                  @click="showAddMtDealModal({ type: 'highSalelist', index: index })"
                  >+添加匹配关系</mtd-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 低销列表 -->
    <div class="margin-to-bottom-operate-area">
      <div>
        <div class="content-module">
          <div class="content-module-left" style="padding-bottom: 0">
            <div class="module-title">低销列表</div>
          </div>
          <div class="content-module-right" style="padding-bottom: 0">
            <div class="module-title">低销列表</div>
          </div>
        </div>
      </div>
      <div v-for="(item, index) in lowSalelist" :key="item.comProduct[0].comProductId">
        <div class="content-module">
          <div class="content-module-left">
            <div class="deal-serial-number">{{ `deal${highSalelist.length + index + 1}` }}</div>
            <div class="product-group-container">
              <div class="group-deal-container">
                <DealCard
                  class="card-module"
                  :deal-info="item.comProduct[0]"
                  :is-m-t="false"
                  :is-high="false"
                  :mt-cat2-id-is-order-goods="poiInfo.mtCat2IdIsOrderGoods"
                  :is-deal="item.mtProduct.length === 0"
                  :editable="dealEditable"
                  @changeDealInfo="
                    (name, value) => {
                      item.comProduct[0][name] = value
                    }
                  "
                ></DealCard>
              </div>
            </div>
          </div>
          <div
            v-if="isNoMatchMtPoi || isChangedMtPoi"
            class="content-module-right"
            :class="{
              'top-no-padding': index !== 0,
              'bottom-no-padding': index !== lowSalelist.length - 1,
            }"
          >
            <div class="no-match-module">
              <div v-if="index === 0">
                <span v-if="isNoMatchMtPoi">门店待开发，deal无法对比</span>
                <span v-else-if="isChangedMtPoi">查询到门店匹配关系更新，暂不可标注deal</span>
              </div>
            </div>
          </div>
          <div v-else class="content-module-right">
            <div
              v-if="item.mtProduct"
              class="deal-serial-number"
              style="flex-direction: row-reverse"
            >
              {{ `共${item.mtProduct.length || 0}对匹配关系` }}
            </div>
            <div class="product-group-container">
              <div class="group-deal-container">
                <DealCard
                  v-for="(mtDealItem, mtDealIndex) in item.mtProduct"
                  :key="mtDealItem.dpProductId"
                  class="card-module"
                  :deal-info="mtDealItem"
                  :is-m-t="true"
                  :editable="dealEditable"
                  :source="poiInfo.source"
                  :is-inspect="isInspect"
                  @changeDealInfo="
                    (name, value) => {
                      mtDealItem[name] = value
                    }
                  "
                  @removeFromGroup="
                    () => {
                      removeMtDealFromGroup({ type: 'lowSalelist', index, mtDealIndex })
                    }
                  "
                  @changeDealLinkInfo="changeDealLinkInfo"
                ></DealCard>
              </div>
              <div class="operate-area">
                <mtd-button
                  type="text"
                  :disabled="!dealEditable"
                  @click="showAddMtDealModal({ type: 'lowSalelist', index: index })"
                  >+添加匹配关系</mtd-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 操作区域 -->
    <div class="bottom-operate-area">
      <div class="content-module">
        <div class="content-module-left"></div>
        <div
          v-if="type === OPERATE_TYPE.MARK && shopEditable && isSelf"
          class="content-module-right button-group"
        >
          <mtd-button
            type="primary"
            style="margin-right: 12px"
            @click="submitManualAnnotation(MANUAL_ANNOTATION_TYPE.NO_HANDLE)"
            >暂不处理</mtd-button
          >
          <mtd-button
            type="primary"
            style="margin-right: 12px"
            @click="submitManualAnnotation(MANUAL_ANNOTATION_TYPE.CANNOT_JUDGE)"
            >无法判断</mtd-button
          >
          <mtd-button
            type="primary"
            :loading="isSubmitting"
            @click="submitManualAnnotation(MANUAL_ANNOTATION_TYPE.SUBMIT)"
            >提交</mtd-button
          >
        </div>
        <div
          v-if="type === OPERATE_TYPE.INSPECTION"
          class="content-module-right inspection-button-group"
        >
          <div style="display: flex">
            <span style="margin-right: 12px; line-height: 30px">质检结果</span>
            <mtd-radio-group
              v-model="inspectionResult"
              type="line"
              style="align-items: normal"
              :disabled="!shopEditable || !isSelf"
              @change="changeRemark"
            >
              <mtd-radio-button :value="INSPECRTION_RESULT.PASS">质检通过</mtd-radio-button>
              <mtd-radio-button :value="INSPECRTION_RESULT.NOT_PASS">质检不通过</mtd-radio-button>
            </mtd-radio-group>
            <div style="margin-left: 5px">
              <mtd-cascader
                v-model="remarkCode"
                :disabled="!shopEditable || !isSelf"
                :data="remarkOptions"
                :field-names="remarkProps"
                placeholder="标签"
                :formatter="remarkFormatter"
                clearable
                @change="changeRemarkCode"
              />
            </div>
            <div style="margin-left: 5px">
              <mtd-tooltip
                :content="remark"
                placement="top-start"
                :disabled="!remark || remark.length < 12"
              >
                <mtd-textarea
                  v-model="remark"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  :disabled="!shopEditable || !isSelf"
                  :placeholder="remarkPlaceholder"
                />
              </mtd-tooltip>
            </div>
          </div>
          <mtd-button
            v-if="shopEditable && isSelf"
            type="primary"
            :loading="isSubmitting"
            @click="submitInspection"
            >提交</mtd-button
          >
        </div>
      </div>
    </div>
    <multi-deal-select-modal
      :visible="mtMultiDealSelectVisible"
      :model-value="mtMultiDealSelectVisible"
      title="更改匹配关系"
      :mt-in-pair-deal-list="mtInPairDealList"
      :com-in-pair-deal-list="comInPairDealList"
      :poi-id="currentMtPoiId"
      :mt-sop-deal-list="mtSopDealList"
      @confirm="addMtDealIntoGroup"
      @close="closeAddMtDealModal"
    />
    <deal-select-modal
      :visible="comDealSelectVisible"
      :model-value="comDealSelectVisible"
      :is-m-t="false"
      title="更换匹配友商商品"
      :com-in-pair-deal-list="comInPairDealList"
      :poi-id="(poiInfo && poiInfo.comPoiId) || null"
      :date-key="(poiInfo && poiInfo.dateKey) || null"
      @confirm="addComDealIntoGroup"
      @close="closeAddComDealModal()"
    />
    <mtd-drawer
      v-model="showUnselectDrawer"
      :title="`我方无匹配关系高销团单(${unselectList.length})`"
      width="90%"
    >
      <div class="unselect">
        <div
          v-for="(unselectedItem, unselectedItemIndex) in unselectList"
          :key="unselectedItemIndex"
          class="unselect-wrap"
        >
          <div class="unselect-list left">
            <div class="unselect-list-item">
              <div v-if="!unselectedItem.comProduct.comProductId" class="unselect-list-item-empty">
                <div>友商无匹配deal</div>
                <mtd-button
                  size="small"
                  type="primary"
                  class="change"
                  @click="changeUnselectComProduct(unselectedItemIndex)"
                  >更换匹配友商商品</mtd-button
                >
              </div>
              <DealCard
                v-else
                class="card-module"
                :deal-info="unselectedItem.comProduct"
                :is-m-t="false"
                :editable="dealEditable"
                :can-change="true"
                :unselected-item-index="unselectedItemIndex"
                @changeDealInfo="
                  (name, value) => {
                    unselectedItem.comProduct[name] = value
                  }
                "
                @changeUnselectComProduct="changeUnselectComProduct"
              ></DealCard>
            </div>
          </div>
          <div class="unselect-list">
            <DealCard
              class="card-module"
              :deal-info="unselectedItem.mtProduct"
              :editable="dealEidtable"
              :can-remove="false"
              :is-m-t="true"
              :can-modify-num="false"
              @changeDealInfo="
                (name, value) => {
                  unselectedItem.mtProduct[name] = value
                }
              "
            ></DealCard>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="footer-btn-wrapper">
          <mtd-button @click="showUnselectDrawer = false">取消</mtd-button>
          <mtd-button v-if="dealEditable" type="primary" @click="handleUnselectSure"
            >确认</mtd-button
          >
        </div>
      </template>
    </mtd-drawer>
  </div>
</template>

<script>
import psb from '@nibfe/platform-sdk'
import {
  ShopCard,
  DealCard,
  MultiDealSelectModal,
  DealSelectModal,
  BasicInfo,
} from '../components/index'
import { getUrlSearch, safeJSONParse, errorHandler, jumpUrl, encryptPoiId } from '@/lib/utils'
import {
  REDIRECT_URL,
  PRODUCT_TYPE,
  MANUAL_ANNOTATION_TYPE,
  INSPECRTION_RESULT,
  BIZ_LINE,
  ANNOTATION_STATUS,
  INSPECRTION_STATUS,
  SOURCE_ENUMS,
} from '@/lib/constants'
import API from '@/api/mark-detail'
import { Message } from '@ss/mtd-vue2'
import { cloneDeep } from 'lodash'

const OPERATE_TYPE = { MARK: 'mark', INSPECTION: 'inspection' }
const ACCESS_CONTROL_TYPE = { EDIT: 'editable', READ: 'readOnly' }

export default {
  components: { ShopCard, DealCard, MultiDealSelectModal, DealSelectModal, BasicInfo },
  data() {
    return {
      PRODUCT_TYPE,
      MANUAL_ANNOTATION_TYPE,
      ACCESS_CONTROL_TYPE,
      OPERATE_TYPE,
      INSPECRTION_RESULT,
      markId: null,
      type: '', // 区分标注还是质检
      accessControl: null, // 区分只读还是可编辑
      originMtPoiId: null, // 原始匹配信息美团门店id
      poiInfo: {},
      originPoiInfo: {},
      comShopInfo: {},
      mtShopInfo: {},
      poiAnnotationExt: {}, // 标注门店扩展信息
      mtPoiExt: {}, // 美团门店扩展信息
      productInfo: {}, // deal标注信息
      originProductInfo: {}, // 原始算法匹配结果信息保留
      highSalelist: [], // 高销列表
      lowSalelist: [], // 低销列表
      highSalelistCache: [], // 缓存高销列表
      lowSalelistCache: [], // 缓存低销列表
      mtSopDealList: [], //mt-poi下所有发单的deal
      isChangedMtPoi: false,
      isNoMatchMtPoi: false,
      inspectionResult: null, // 质检结果
      remarkCode: [], // 质检标签
      remarkProps: {
        label: 'name',
        value: 'code',
        children: 'children',
      },
      remarkOptions: [],
      remarkPlaceholder: '请输入质检备注',
      remark: null, // 质检备注
      isSubmitting: false, //正在提交
      userInfo: {}, // 用户信息
      currentUpdateInfo: {}, // 当前更新匹配关系的deal位置信息
      mtMultiDealSelectVisible: false,
      comDealSelectVisible: false,
      showUnselectDrawer: false,
      currentUnselectItemIndex: null,
      noMatchingRelationList: [],
    }
  },
  computed: {
    shopEditable() {
      return this.accessControl === ACCESS_CONTROL_TYPE.EDIT && this.notDone
    },
    unselectList() {
      const unselectMtDealList =
        this.noMatchingRelationList.filter(
          item => this.mtInPairDealIdList.indexOf(item.dpProductId) == -1,
        ) || []
      this.mtSopDealList.forEach(sopDeal => {
        const targetDeal = unselectMtDealList.find(deal => {
          return (
            deal.dpProductId === sopDeal.dpProductId && deal.mtProductType === sopDeal.mtProductType
          )
        })
        targetDeal &&
          Object.assign(targetDeal, {
            sopType: sopDeal.sopType,
            sopPriority: sopDeal.sopPriority,
          })
      })
      return unselectMtDealList.map(item => {
        return {
          mtProduct: item,
          comProduct: [],
        }
      })
    },
    isWb() {
      return this.userInfo && this.userInfo.outsourcing
    },
    isSelf() {
      return this.poiInfo.operatorMis === this.userInfo?.userMis
    },
    notDone() {
      return (
        (this.type === OPERATE_TYPE.MARK &&
          this.poiInfo.annotationStatus === ANNOTATION_STATUS.TODO) ||
        (this.type === OPERATE_TYPE.INSPECTION &&
          this.poiInfo.inspectionStatus === INSPECRTION_STATUS.TODO)
      )
    },
    isInspect() {
      return this.type === OPERATE_TYPE.INSPECTION
    },
    // 匹配中的mtDeal
    mtInPairDealList() {
      const allList = this.highSalelist.concat(this.lowSalelist)
      const mtDealList = []
      allList.map(item => {
        // eslint-disable-next-line no-extra-semi
        ;(item.mtProduct || []).forEach(mtProductItem => {
          mtDealList.push(mtProductItem)
        })
      })
      return mtDealList
    },
    mtInPairDealIdList() {
      return this.mtInPairDealList.map(item => item.dpProductId).filter(id => id)
    },
    // 匹配中的comDeal
    comInPairDealList() {
      const allList = this.highSalelist.concat(this.lowSalelist)
      const comDealList = []
      allList.map(item => {
        if (item.mtProduct && item.mtProduct.length) {
          // eslint-disable-next-line no-extra-semi
          ;(item.comProduct || []).forEach(comProductItem => {
            comDealList.push(comProductItem)
          })
        }
      })
      return comDealList
    },
  },
  watch: {
    shopEditable() {
      this.dealEditable =
        !this.isChangedMtPoi && this.shopEditable && this.poiAnnotationExt.comPoiComparable
    },
  },
  created() {
    psb.config('16951264969y09p5', {
      masterOrigin:
        process.env.NODE_ENV === 'development' ? 'http://apollo.nibcrm.test.sankuai.com/' : '',
      redirectUrl: process.env.NODE_ENV === 'development' ? null : REDIRECT_URL,
    })
  },
  async mounted() {
    this.handleUrlParams()
    this.getUserInfo()
    await this.getDetail()
  },
  methods: {
    changeUnselectComProduct(unselectedItemIndex) {
      this.comDealSelectVisible = true
      this.currentUnselectItemIndex = unselectedItemIndex
    },
    handleUnselectSure() {
      const hasChangedItemList = this.unselectList.filter(item => item.comProduct.comProductId)
      hasChangedItemList.forEach(hasChangedItem => {
        const selectedComProduct = hasChangedItem.comProduct
        const selectedMtProduct = hasChangedItem.mtProduct
        let targetList = ''
        let targetIndex = -1
        targetIndex = this.highSalelist.findIndex((highSaleItem, highSaleItemIndex) => {
          const comProduct = highSaleItem.comProduct || []
          return comProduct[0] && comProduct[0].comProductId == selectedComProduct.comProductId
        })
        targetList = targetIndex !== -1 ? 'highSalelist' : 'lowSalelist'
        if (targetIndex === -1) {
          targetIndex = this.lowSalelist.findIndex((lowSaleItem, lowSaleItemIndex) => {
            const comProduct = lowSaleItem.comProduct || []
            return comProduct[0] && comProduct[0].comProductId == selectedComProduct.comProductId
          })
        }
        if (targetIndex > -1 && this[targetList][targetIndex]) {
          this[targetList][targetIndex].comProduct[0] = hasChangedItem.comProduct
          if (!this[targetList][targetIndex].mtProduct) this[targetList][targetIndex].mtProduct = []
          this[targetList][targetIndex].mtProduct.push(selectedMtProduct)
        }
      })
      this.showUnselectDrawer = false
    },
    handleUrlParams() {
      const queryParmas = getUrlSearch()
      this.markId = queryParmas.markId
      this.type = queryParmas.type
      this.accessControl = queryParmas.accessControl
    },
    getUserInfo() {
      const params = {
        groupKey: this.type === OPERATE_TYPE.MARK ? 1 : 2,
        bizLine: BIZ_LINE.ZONG,
      }
      API.queryUserRole(params).then(data => {
        this.userInfo = data
      })
    },
    async getDetail() {
      if (!this.markId) {
        this.$mtd.message.error('缺少必要参数：标注ID')
        return
      }
      const params = {
        id: this.markId,
        bizLine: BIZ_LINE.ZONG,
      }
      let res =
        this.type === OPERATE_TYPE.MARK
          ? await API.queryManualAnnotationDetail(params)
          : await API.queryQualityInspectionDetail(params)
      if (!res) return
      let { originalInfo = {}, annotationInfo = {} } = res
      this.originMtPoiId = originalInfo.poiInfo && originalInfo.poiInfo.mtPoiId
      this.originPoiInfo = safeJSONParse(JSON.stringify(originalInfo.poiInfo))
      this.currentMtPoiId = annotationInfo.poiInfo && annotationInfo.poiInfo.mtPoiId
      this.poiInfo = annotationInfo.poiInfo
      // 获取质检结果（查询版）
      this.inspectionResult = this.formatInspectionResult(annotationInfo.poiInfo.inspectionStatus)
      this.formatRemarkCode(this.inspectionResult, annotationInfo.poiInfo.remarkCode)
      this.remark = annotationInfo.poiInfo.remark
      this.getMtPoiInfo(this.currentMtPoiId, true)
      await this.queryMtPoiDealSopInfo()
      const poiAnnotationExtStr = annotationInfo.poiInfo && annotationInfo.poiInfo.poiAnnotationExt
      this.poiAnnotationExt = safeJSONParse(poiAnnotationExtStr) || {}
      const mtPoiExtStr = annotationInfo.poiInfo && annotationInfo.poiInfo.mtPoiExt
      this.mtPoiExt = safeJSONParse(mtPoiExtStr) || {}
      const productInfoStr = annotationInfo.productInfo
      this.originProductInfo = originalInfo.manyToManyProductInfo
      if (!annotationInfo.productInfo) {
        // 首次进行标注
        this.productInfo = safeJSONParse(JSON.stringify(originalInfo.manyToManyProductInfo))
        this.formatProductInfo()
      } else {
        this.productInfo = safeJSONParse(productInfoStr)
        this.formatProductInfo()
      }
      if (!this.currentMtPoiId) return
      // 如果是质检单传qualityInspectionId，标注单传manualAnnotationId
      const noMatchingRelationHighSaleProductListParams = {}
      if (this.isInspect) {
        // 质检单传qualityInspectionId
        noMatchingRelationHighSaleProductListParams.qualityInspectionId = this.markId
      } else {
        // 标注单传manualAnnotationId
        noMatchingRelationHighSaleProductListParams.manualAnnotationId = this.markId
      }
      const noMatchingRelationList = await API.queryNoMatchingRelationHighSaleProductList({
        bizLine: BIZ_LINE.ZONG,
        mtPoiId: this.currentMtPoiId,
        ...noMatchingRelationHighSaleProductListParams,
      })
      this.noMatchingRelationList = noMatchingRelationList || []
    },

    // 查询美团门店信息
    async getMtPoiInfo(mtPoiId, isInit = false) {
      if (mtPoiId) {
        const params = {
          mtPoiId: mtPoiId,
          bizLine: BIZ_LINE.ZONG,
        }
        // 如果是质检单传qualityInspectionId，标注单传manualAnnotationId
        if (this.isInspect) {
          // 质检单传qualityInspectionId
          params.qualityInspectionId = this.markId
        } else {
          // 标注单传manualAnnotationId
          params.manualAnnotationId = this.markId
        }
        await API.queryMtPoiDealDetail(params)
          .then(data => {
            if (data && data.poiDetailInfo) {
              Object.assign(this.poiInfo, data.poiDetailInfo)
              this.poiInfo.mtPoiId = data.poiDetailInfo.poiId
              this.poiInfo.mtPoiName = data.poiDetailInfo.poiName
              delete this.poiInfo.poiId
              delete this.poiInfo.poiName
              if (!isInit && (data.poiDetailInfo.poiOnline || data.poiDetailInfo.poiOnline === 0)) {
                this.mtPoiExt = {
                  mtPoiOnline: data.poiDetailInfo.poiOnline,
                  mtPoiNotComparableReasonDesc: '',
                }
              }
            } else {
              this.$mtd.message.error('门店信息获取失败')
            }
          })
          .catch(errorHandler)
      }
      this.formatShopInfo()
      if (this.isChangedMtPoi && mtPoiId === this.originMtPoiId) {
        // 从更改的门店换回算法匹配的门店, deal匹配信息代入原算法匹配结果
        this.productInfo = safeJSONParse(JSON.stringify(this.originProductInfo))
        this.formatProductInfo()
      }
      this.currentMtPoiId = mtPoiId
      this.isNoMatchMtPoi = !this.currentMtPoiId
      this.isChangedMtPoi = this.isNoMatchMtPoi || this.currentMtPoiId !== this.originMtPoiId
    },

    // 获取mt-poi下所有发单的deal
    async queryMtPoiDealSopInfo() {
      const params = {
        inflowDate: this.poiInfo.dateKey,
        comPoiId: this.poiInfo.comPoiId,
        mtPoiId: this.currentMtPoiId,
        bizLine: BIZ_LINE.ZONG,
      }
      this.mtSopDealList = (await API.queryMtPoiDealSopInfo(params)) || []
    },

    formatShopInfo() {
      if (!this.poiInfo) return
      this.comShopInfo = {
        title: this.poiInfo.comPoiName,
        comPoiId: this.poiInfo.comPoiId,
        // 待标注/待质检 & 实时链路 高亮展示门店卡片
        needHighlight: this.notDone && this.poiInfo?.source === SOURCE_ENUMS.REAL_TIME,
        infoList: [
          {
            title: '品类',
            content: this.originPoiInfo.comPoiCateName,
          },
          {
            title: '地址',
            content: this.originPoiInfo.comPoiAddress,
          },
        ],
      }
      this.mtShopInfo = {
        title: this.poiInfo.mtPoiName,
        mtPoiId: this.poiInfo.mtPoiId,
        dpPoiId: this.poiInfo.dpPoiId,
        dpProvinceId: this.poiInfo.dpProvinceId,
        dpCityId: this.poiInfo.dpCityId,
        infoList: [
          {
            title: '一级类目',
            content: this.poiInfo.mtCat1Name,
          },
          {
            title: '二级类目',
            content: this.poiInfo.mtCat2Name,
          },
          {
            title: '电话',
            content: this.poiInfo.contactNumber,
          },
          {
            title: '城市',
            content: this.poiInfo.dpCityName,
          },
          {
            title: '地址',
            content: this.poiInfo.poiAddress,
          },
          // 调整布局
          {},
          {
            title: '点评ID',
            content: this.isWb ? encryptPoiId(this.poiInfo.dpPoiId) : this.poiInfo.dpPoiId,
          },
          {
            title: '我方ID',
            content: this.isWb ? encryptPoiId(this.poiInfo.mtPoiId) : this.poiInfo.mtPoiId,
          },
        ],
      }
    },

    formatProductInfo() {
      if (!this.productInfo) return
      this.highSalelist = this.productInfo.highSales || []
      this.lowSalelist = this.productInfo.lowSales || []
      this.highSalelistCache = cloneDeep(this.productInfo.highSales || [])
      this.lowSalelistCache = cloneDeep(this.productInfo.lowSales || [])
    },

    // 联动修改页面上所有相同deal的目标属性
    changeDealLinkInfo({ isMT, dealId, name, value }) {
      const allList = this.highSalelist.concat(this.lowSalelist)
      const productName = isMT ? 'mtProduct' : 'comProduct'
      const dealKey = isMT ? 'dpProductId' : 'comProductId'
      allList.map(item => {
        // eslint-disable-next-line no-extra-semi
        ;(item[productName] || []).forEach(productItem => {
          if (productItem[dealKey] === dealId) {
            productItem[name] = value
          }
        })
      })
    },

    changeComShopInfo(name, value) {
      this.poiAnnotationExt[name] = value
      this.$forceUpdate()
      this.dealEditable =
        !this.isChangedMtPoi && this.shopEditable && this.poiAnnotationExt.comPoiComparable
    },

    changeMtShopInfo(name, value) {
      this.mtPoiExt[name] = value
      this.$forceUpdate()
    },

    removeMtDealFromGroup({ type, index, mtDealIndex }) {
      if (this[type] && this[type][index] && this[type][index].mtProduct) {
        this[type][index].mtProduct.splice(mtDealIndex, 1)
      }
    },
    showAddMtDealModal(currentUpdateInfo) {
      this.currentUpdateInfo = currentUpdateInfo
      this.mtMultiDealSelectVisible = true
    },
    closeAddMtDealModal() {
      this.currentUpdateInfo = {}
      this.mtMultiDealSelectVisible = false
    },

    closeAddComDealModal() {
      this.currentUnselectItemIndex = null
      this.comDealSelectVisible = false
    },
    // 更换匹配门店
    changeMtShop(poiId) {
      if (poiId === this.currentMtPoiId) return
      if (poiId === '-1') {
        // 无匹配门店
        this.poiInfo.mtPoiId = ''
        this.poiInfo.dpPoiId = ''
        this.formatShopInfo()
        this.currentMtPoiId = ''
        this.isNoMatchMtPoi = true
        this.isChangedMtPoi = true
      } else {
        this.getMtPoiInfo(poiId)
      }
    },

    formatCommonSubmitParams() {
      const {
        mtPoiId,
        mtPoiName,
        dpPoiId,
        dpProvinceId,
        dpCityId,
        dpProvinceName,
        dpCityName,
        mtCat2Id,
        mtCat2Name,
        bu,
        mtCat2IdIsOrderGoods,
      } = this.poiInfo
      const removeProductAnnotationInfo =
        this.isChangedMtPoi || (this.poiAnnotationExt && !this.poiAnnotationExt.comPoiComparable)
      let recoverProductInfo = {}
      if (!removeProductAnnotationInfo) {
        recoverProductInfo = {
          highSales: this.highSalelist,
          lowSales: this.lowSalelist,
        }
      }
      const params = {
        id: this.markId,
        mtPoiId,
        mtPoiName,
        dpPoiId,
        dpProvinceId,
        dpCityId,
        dpProvinceName,
        dpCityName,
        mtCat2Id,
        mtCat2Name,
        bu,
        mtCat2IdIsOrderGoods,
        productAnnotationInfo: removeProductAnnotationInfo
          ? null
          : JSON.stringify(recoverProductInfo),
        poiAnnotationExt: JSON.stringify(this.poiAnnotationExt),
        mtPoiExt: JSON.stringify(this.mtPoiExt),
        bizLine: BIZ_LINE.ZONG,
      }
      return params
    },
    // 判断bml结果是否填写
    validateBmlResult(list, type) {
      let hasGoodsError = false,
        hasResultError = false
      let errorGoodsMessages = '当前',
        errorResultMessages = '当前'
      for (let index = 0; index < list.length; index++) {
        const item = list[index]
        if (item.comProduct.length > 0 && this.poiInfo.mtCat2IdIsOrderGoods) {
          const i = item.comProduct[0]
          if (i.orderGoods !== null && i.orderGoods === 1 && item.mtProduct.length === 0) {
            if (i.orderBmlResult === null || i.orderBmlResult === '') {
              errorResultMessages += `deal${
                type === 'high' ? index + 1 : this.highSalelist.length + index + 1
              } `
              hasResultError = true
            }
          } else if (i.orderGoods === null && item.mtProduct.length === 0) {
            errorGoodsMessages += `deal${
              type === 'high' ? index + 1 : this.highSalelist.length + index + 1
            } `
            hasGoodsError = true
          }
        }
      }
      if (hasGoodsError) {
        errorGoodsMessages += '的预订商品未选择'
        Message.error(errorGoodsMessages)
        return false
      }
      if (hasResultError) {
        errorResultMessages += '的bml结果未填写'
        Message.error(errorResultMessages)
        return false
      }
      return true // 所有检查通过，返回true
    },
    addMtDeal(selectDeal) {
      const { type, index } = this.currentUpdateInfo || {}
      let targetGroup = this[type]?.[index]
      if (!targetGroup) return
      const { dpDealId } = selectDeal
      if (!targetGroup.mtProduct) targetGroup.mtProduct = []
      if (targetGroup.mtProduct.findIndex(item => item.dpProductId === dpDealId) !== -1) {
        // 已有该匹配关系跳过
        return
      }

      // 已有的相同mtdeal 直接使用该deal信息更新 争议case默认false
      const sameMtDeal = (this[`${type}Cache`]?.[index]?.mtProduct || []).find(
        dealItem => dealItem.dpProductId === dpDealId,
      )
      let newMtDeal = null
      if (sameMtDeal && sameMtDeal.dpProductId) {
        newMtDeal = safeJSONParse(JSON.stringify(sameMtDeal))
        newMtDeal.disputedCase = false
      } else {
        newMtDeal = {
          dpProductId: selectDeal.dpDealId,
          mtProductName: selectDeal.dealName,
          mtProductType: selectDeal.dealType,
          mtMarketPrice: selectDeal.marketPrice,
          mtSalePrice: selectDeal.salePrice,
          mtPromotionPrice: selectDeal.promotionPrice,
          mtProductOnline: selectDeal.dealOnline,
          sopType: selectDeal.sopType,
          sopPriority: selectDeal.sopPriority,
          mtProductGroupDealNum: selectDeal.mtDealNum,
          mtCommercialSubsidyPrice: selectDeal.mtCommercialSubsidyPrice,
          mtConsumerPriceNewUserAmount: selectDeal.mtConsumerPriceNewUserAmount,
          mtConsumerPriceOldUserAmount: selectDeal.mtConsumerPriceOldUserAmount,
          mtWorkPrice: selectDeal.mtWorkPrice,
          disputedCase: false,
          isAdd: true,
        }
      }
      newMtDeal && targetGroup.mtProduct.push(newMtDeal)
    },

    addMtDealIntoGroup(selectDeals) {
      // eslint-disable-next-line no-extra-semi
      ;(selectDeals || []).forEach(selectDeal => {
        this.addMtDeal(selectDeal)
      })
      this.closeAddMtDealModal()
    },

    addComDealIntoGroup(selectDeal) {
      const { comProductId, isChoose } = selectDeal
      const selectDealInPair = this.comInPairDealList.find(
        item => item.comProductId == comProductId,
      )
      this.unselectList[this.currentUnselectItemIndex].comProduct = isChoose
        ? selectDealInPair
        : selectDeal
      this.closeAddComDealModal()
    },

    handleSubmitJump(nextMarkId) {
      let href = ''
      if (nextMarkId) {
        href = `/crm-saletool-config/mark-detail/index.html#/mark-operation?markId=${nextMarkId}&type=${this.type}&accessControl=${this.accessControl}`
        location.href = href
      } else {
        href = `/crm-saletool-config/${
          this.type === OPERATE_TYPE.INSPECTION ? 'inspection' : 'annotation'
        }-workbench/index.html#/`
      }
      jumpUrl({ url: href, target: '_self', platform: 'platform' })
    },
    // 提交标注结果
    submitManualAnnotation(opType) {
      let params = {}
      if (opType === MANUAL_ANNOTATION_TYPE.SUBMIT) {
        params = this.formatCommonSubmitParams()
      } else {
        params = {
          id: this.markId,
          bizLine: BIZ_LINE.ZONG,
        }
      }
      Object.assign(params, { opType, grayRelease: true })
      const newList = this.highSalelist.concat(this.lowSalelist)
      if (
        opType === MANUAL_ANNOTATION_TYPE.SUBMIT && this.dealEditable
          ? this.validateBmlResult(newList, 'high') && this.validateBmlResult(newList, 'low')
          : true
      ) {
        this.isSubmitting = true
        let nextMarkId = null
        API.saveManualAnnotationThriftService(params)
          .then(res => {
            if (res && res.code === 200) {
              // 提交成功
              this.$mtd.message({
                type: 'success',
                message: opType === MANUAL_ANNOTATION_TYPE.NO_HANDLE ? '稍后处理' : '提交成功',
              })
              nextMarkId = res.data
            } else {
              this.$mtd.message.error((res && res.msg) || '提交失败')
            }
          })
          .catch(errorHandler)
          .finally(() => {
            this.isSubmitting = false
            this.handleSubmitJump(nextMarkId)
          })
      }
    },
    submitInspection() {
      if (!this.inspectionResult) {
        this.$mtd.message.error('请选择质检结果')
        return
      }
      if (
        this.inspectionResult === INSPECRTION_RESULT.NOT_PASS &&
        (this.remarkCode.length < 0 || !this.remark)
      ) {
        this.$mtd.message.error('请填写质检备注')
        return
      }
      const params = this.formatCommonSubmitParams()
      Object.assign(params, {
        grayRelease: true,
        inspectionResult: this.inspectionResult,
        remarkCode: this.remarkCode[1] || null,
        remark: this.remark || '',
      })
      this.isSubmitting = true
      let nextMarkId = null
      API.saveQualityInspectionThriftService(params)
        .then(res => {
          if (res && res.code === 200) {
            // 提交成功
            this.$mtd.message({
              type: 'success',
              message: '提交成功',
            })
            nextMarkId = res.data
          } else {
            this.$mtd.message.error((res && res.msg) || '提交失败')
          }
        })
        .catch(errorHandler)
        .finally(() => {
          this.isSubmitting = false
          this.handleSubmitJump(nextMarkId)
        })
    },

    // 将标注信息恢复为原始匹配信息 门店状态不可比 或 更改门店匹配关系
    recoverOriginProductInfo() {
      this.productInfo = safeJSONParse(JSON.stringify(this.originProductInfo))
      this.formatProductInfo(true)
    },
    remarkFormatter(labels, selectOptions, value) {
      if (labels.length - 1 > 0) {
        return labels[labels.length - 1]
      } else {
        return labels
      }
    },

    changeRemark(value) {
      this.formatRemarkCode(value)
    },
    changeRemarkCode(value, selectOption) {
      this.remarkPlaceholder = selectOption[1].remark || '请输入质检备注'
    },
    formatInspectionResult(value) {
      const map = new Map()
      map
        .set(INSPECRTION_STATUS.TODO, null)
        .set(INSPECRTION_STATUS.PASS, INSPECRTION_RESULT.PASS)
        .set(INSPECRTION_STATUS.REJECT, INSPECRTION_RESULT.NOT_PASS)
        .set(INSPECRTION_STATUS.EXPIRE, null)
      return map.get(value)
    },
    // 构造标签数据
    async formatRemarkCode(inspectionResult, remarkCode) {
      if (!inspectionResult) return
      const type = inspectionResult === INSPECRTION_RESULT.PASS ? 2 : 1
      await API.queryInspectionRemarkTree({
        type: type,
        bizLine: BIZ_LINE.ZONG,
      }).then(res => {
        if (res && res.code === 200) {
          this.remarkOptions = []
          this.remarkCode = []
          this.remarkPlaceholder = '请输入质检备注'
          this.remarkOptions = safeJSONParse(res.data)
        }
      })
      // 查询状态
      if (remarkCode) {
        this.remarkOptions.map(item => {
          item.children.map(ele => {
            if (ele.code === remarkCode) {
              this.remarkCode = []
              this.remarkCode.push(item.code, ele.code)
            }
          })
        })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.mtd-radio-button-line.mtd-radio-button-checked {
  background-color: #ffd100;
}
.footer-btn-wrapper {
  text-align: right;
  .mtd-btn {
    height: 34px;
    width: 114px;
    &-primary {
      margin-left: 10px;
    }
  }
}
::v-deep {
  .mtd-drawer-footer {
    position: relative;
  }
}
.unselect {
  &-wrap {
    display: flex;
    margin-bottom: 8px;
  }
  &-list {
    width: 49%;
    &.left {
      margin-right: 10px;
    }
    .card-module {
      padding: 8px;
      background-color: #fff;
      height: 100%;
    }
    &-item {
      height: 100%;
      &-empty {
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.2);
        height: 100%;
        vertical-align: middle;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #bcbcbc;
        font-size: 20px;
        font-weight: 500;
        position: relative;
        .change {
          position: absolute;
          right: 8px;
          bottom: 14px;
        }
      }
    }
  }
}
.mark-operation-container {
  min-width: 1100px;
  padding: 16px;
  .page-title-text {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
  }
  .content-module {
    display: flex;
    justify-content: space-between;
    .content-module-left {
      width: 49%;
      background-color: #f2f2f2;
      padding: 12px;
    }
    .content-module-right {
      width: 49%;
      background-color: #f2f2f2;
      padding: 12px;

      &.highsale {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .module-unselected {
        margin-bottom: 8px;
        color: red;
      }
    }
    .no-match-module {
      padding: 8px;
      background-color: #fff;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: #b7babd;
    }
    .top-no-padding {
      padding-top: 0;
    }
    .bottom-no-padding {
      padding-bottom: 0;
    }
    .module-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
    }
    .module-title-flex {
      display: flex;
      align-items: center;
    }
    .module-title-flex-item {
      color: rgba(17, 25, 37, 0.85);
      font-size: 14px;
      font-weight: 400;
      margin-left: 20px;
    }
    .card-module {
      margin-bottom: 8px;
      padding: 8px;
      background-color: #fff;
      .deal-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 4px;
      }
    }
    .deal-serial-number {
      background-color: #fff;
      padding: 12px 12px 0 12px;
      font-size: 14px;
      font-weight: 500;
      display: flex;
    }
    .product-group-container {
      background-color: #fff;
      height: 100%;
      .group-deal-container {
        background-color: #fff;
        padding: 8px;
      }
      .operate-area {
        background-color: #fff;
        padding-bottom: 8px;
        display: flex;
        flex-direction: row-reverse;
      }
    }
  }
  .margin-to-bottom-operate-area {
    padding-bottom: 40px;
  }
  .bottom-operate-area {
    min-width: calc(1100px - 32px);
    width: calc(100% - 32px);
    position: fixed;
    bottom: 0;
    .button-group {
      display: flex;
      justify-content: flex-end;
    }
    .inspection-button-group {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
