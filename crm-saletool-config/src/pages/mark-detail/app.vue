<template>
  <div>
    <router-view></router-view>
  </div>
</template>
<script>
import { SHOP_STATUS_OPTIONS } from '@/lib/constants'

export default {
  data() {
    return {
      title: '功夫肩颈天使色彩',
      previewPicUrl: 'https://p0.meituan.net/travelcube/a650b46ef5cbf8cb9b9c05f03c9f4f9195186.png',
      infoList: [
        {
          title: '品类',
          content: '美容美体好多好多字美容美体好多好多字美容美体好多好多字美容美体好多好',
        },
        {
          title: '电话',
          content: '0829-92829827',
        },
        {
          title: '地址',
          content: '和平中路 535 号',
        },
        {
          title: '行业ID',
          content: '1289472389478923748',
        },
      ],
      selectModalVisible: false,
      shopStatusOptions: SHOP_STATUS_OPTIONS,
      compareStatus: 1,
      shopStatus: 1,
    }
  },

  watch: {
    shopStatus(newVal) {
      switch (newVal) {
        case 1: {
          this.compareStatus = 1
          return
        }
        case 2: {
          this.compareStatus = 2
          return
        }
        default: {
          this.compareStatus = null
          return
        }
      }
    },
  },

  methods: {
    confirmHandler() {
      this.selectModalVisible = false
    },
  },
}
</script>
