<template>
  <div :class="{ pangu: isPangu }">
    <router-view></router-view>
  </div>
</template>
<script>
import { reportError, getEnv } from '@/lib/utils'

export default {
  data() {
    return {
      isPangu: getEnv().isPanguPlatform,
    }
  },
  beforeCreate() {
    if (getEnv().isPanguPlatform) {
      // 盘古主题
      import('@ss/mtd-vue2/lib/theme-chalk/index.css').catch(reportError)
    } else if (getEnv().isApolloPlatform) {
      // 阿波罗主题
      import('@ss/mtd-vue2/lib/theme-yellow/index.css').catch(reportError)
      import('@nibfe/apollo-pc-ui-component-mtd2/src/styles/index.css').catch(reportError)
    } else {
      // 默认主题
      import('@ss/mtd-vue2/lib/theme-yellow/index.css').catch(reportError)
      import('@nibfe/apollo-pc-ui-component-mtd2/src/styles/index.css').catch(reportError)
    }
  },
}
</script>
