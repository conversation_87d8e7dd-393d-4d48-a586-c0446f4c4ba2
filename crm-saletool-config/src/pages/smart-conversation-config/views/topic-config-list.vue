<template>
  <div id="smart-conversation-config-list" class="smart-conversation-config-list">
    <div class="">
      <div id="ele"></div>
    </div>
  </div>
</template>
<script>
import psb from '@nibfe/platform-sdk'
import { REDIRECT_URL } from '@/lib/constants'
import graphCfg from '@nibfe/ccc-lowcode-render'
import gateway from '@/lib/gateway'
import { reportError, arrangeOrderByKeys, calcRowSpanOfKeys, getEnv } from '@/lib/utils'
import { LXUtils } from '@/lib/lx'
import { valLabGeneratorMap } from '../lib/lx'

export default {
  components: {},
  data() {
    return {
      pageMessage: '',
    }
  },
  created() {
    if (getEnv().isApolloPlatform) {
      psb.config('1705025435bj1yks', {
        masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
        redirectUrl: process.env.NODE_ENV === 'development' ? null : REDIRECT_URL,
      })
    }
    graphCfg({
      el: '#ele',
      tenantId: 3,
      pageType: 'chart', // 图表类型
      remoteURL: getEnv().isPanguPlatform
        ? process.env.VUE_APP_API_HOST_PANGU
        : process.env.VUE_APP_API_HOST,
      module: 'smart-conversation-config-v2',
      appEnv: process.env.VUE_APP_ENV,
      templateId: process.env.VUE_APP_ENV === 'production' ? 233 : 2172,
      modelId: process.env.VUE_APP_ENV === 'production' ? 337 : 1562,
      apiConfig: getEnv().isPanguPlatform
        ? {
            getTemplateByIdApi: '/gateway/pangucrm/templateApi/queryTemplateByTIdAndMIdAndMetaData',
            queryModelAndPageInteractionApi:
              '/gateway/pangucrm/modelQueryServiceApi/queryModelAndPageInteraction',
          }
        : null,
      initParams: {
        terminal: 0, //来自于PC，必填
        planSource: 0, //非来自拜访创建的，建议填0
      },
      lib: {
        reportError,
        vueInstance: this,
        arrangeOrderByKeys,
        calcRowSpanOfKeys,
        LXUtils: new LXUtils('gc_m', {
          cid: 'c_gc_m_9j8gp5fi',
          appnm: 'dp_apollo_pc',
          valLabGenerator: valLabGeneratorMap.misIdAndTenantId(this.$route.query.tenantId ?? '-1'),
        }),
        gateway,
      },
      passthrough: {
        //选填，本地项目中向编辑器传递的常量，在编辑器中通过this.constants.passthrough.  调用
        PLATFORM: getEnv().isPanguPlatform ? 'PANGU' : 'APOLLO',
      },
      // mockInfo: {
      //   dslJson: schema,
      //   variable: {},
      // },
    }).then(params => {
      const { instance, eventBus } = params || {}
      this.$bus = eventBus
      eventBus.$on('toOut', res => {
        this.pageMessage = res
      })
    })
  },
  methods: {
    // sendMessage() {
    //   this.$bus.$emit('toInner', '我是来自外部的消息' + Math.random())
    // },
  },
}
</script>

<style lang="scss" scoped>
.smart-conversation-config-list {
  min-width: 1100px;
}
</style>
