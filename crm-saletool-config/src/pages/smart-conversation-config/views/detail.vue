<template>
  <div id="smart-conversation-config-detail" class="smart-conversation-config-detail">
    <div class="">
      <div id="ele"></div>
    </div>
  </div>
</template>
<script>
import psb from '@nibfe/platform-sdk'
import { REDIRECT_URL } from '@/lib/constants'
import graphCfg from '@nibfe/ccc-lowcode-render'
import { createXHRForUpload } from '../lib/upload-picture'
import { getXlsxJsonData } from '../lib/utils'
import { getUrlSearch, reportError, getEnv } from '@/lib/utils'

export default {
  components: {},
  data() {
    return {
      pageMessage: '',
    }
  },
  created() {
    if (getEnv().isApolloPlatform) {
      psb.config('1698842078wf7f31', {
        masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
        redirectUrl: process.env.NODE_ENV === 'development' ? null : REDIRECT_URL,
      })
    }
    graphCfg({
      el: '#ele',
      tenantId: 3,
      pageType: 'chart', // 图表类型
      remoteURL: getEnv().isPanguPlatform
        ? process.env.VUE_APP_API_HOST_PANGU
        : process.env.VUE_APP_API_HOST,
      module: 'smart-conversation-config',
      appEnv: process.env.VUE_APP_ENV,
      templateId: process.env.VUE_APP_ENV === 'production' ? 192 : 2122,
      apiConfig: {
        getTemplateByIdApi: '/gateway/pangucrm/templateApi/queryTemplateByTIdAndMIdAndMetaData',
        queryModelAndPageInteractionApi:
          '/gateway/pangucrm/modelQueryServiceApi/queryModelAndPageInteraction',
      },
      initParams: {
        terminal: 0, //来自于PC，必填
        planSource: 0, //非来自拜访创建的，建议填0
      },
      lib: {
        createXHRForUpload,
        getUrlSearch,
        getXlsxJsonData,
        reportError,
      },
      passthrough: {
        //选填，本地项目中向编辑器传递的常量，在编辑器中通过this.constants.passthrough.  调用
        PLATFORM: getEnv().isPanguPlatform ? 'PANGU' : 'APOLLO',
      },
      // mockInfo: {
      //   dslJson: schema,
      //   variable: {},
      // },
    }).then(() => {})
  },
  methods: {
    // sendMessage() {
    //   this.$bus.$emit('toInner', '我是来自外部的消息' + Math.random())
    // },
  },
}
</script>

<style lang="scss" scoped>
.smart-conversation-config-detail {
  min-width: 1100px;
}
</style>
