<template>
  <div class="container">
    <div ref="ccc-container"></div>
  </div>
</template>
<script>
import psb from '@nibfe/platform-sdk'
import graphCfg from '@nibfe/ccc-lowcode-render'
import createUpload from '@ai/mss-upload-js'
import { REDIRECT_URL } from '@/lib/constants'
import gateway from '@/lib/gateway'
import dayjs from 'dayjs'
import {
  reportError,
  arrangeOrderByKeys,
  calcRowSpanOfKeys,
  jumpUrlByEnv,
  getEnv,
} from '@/lib/utils'
import { LXUtils } from '@/lib/lx'
import { valLabGeneratorMap } from '../lib/lx'

export default {
  components: {},
  data() {
    return {
      pageMessage: '',
    }
  },
  created() {
    if (getEnv().isApolloPlatform) {
      psb.config('1722219799l6bs65', {
        masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
        redirectUrl: process.env.NODE_ENV === 'development' ? null : REDIRECT_URL,
      })
    }
  },
  mounted() {
    graphCfg({
      el: this.$refs['ccc-container'],
      tenantId: 3,
      pageType: 'chart', // 图表类型
      remoteURL: getEnv().isPanguPlatform
        ? process.env.VUE_APP_API_HOST_PANGU
        : process.env.VUE_APP_API_HOST,
      module: 'smart-conversation-config',
      appEnv: process.env.VUE_APP_ENV,
      templateId: process.env.VUE_APP_ENV === 'production' ? 257 : 2337,
      modelId: process.env.VUE_APP_ENV === 'production' ? 375 : 1716,
      initParams: {
        terminal: 0, //来自于PC，必填
        planSource: 0, //非来自拜访创建的，建议填0
      },
      apiConfig: getEnv().isPanguPlatform
        ? {
            getTemplateByIdApi: '/gateway/pangucrm/templateApi/queryTemplateByTIdAndMIdAndMetaData',
            queryModelAndPageInteractionApi:
              '/gateway/pangucrm/modelQueryServiceApi/queryModelAndPageInteraction',
          }
        : null,
      lib: {
        reportError,
        vueInstance: this,
        arrangeOrderByKeys,
        calcRowSpanOfKeys,
        LXUtils: new LXUtils('gc_m', {
          cid: 'c_gc_m_sxuawo8l',
          appnm: 'dp_apollo_pc',
          valLabGenerator: valLabGeneratorMap.misIdAndTenantId(this.$route.query.tenantId ?? '3'),
        }),
        createUpload,
        gateway,
        jumpUrlByEnv,
        dayjs,
      },
      /**
       * [选填] 向 CCC 编辑器传递的常量
       *
       * 在编辑器中通过 `this.constants.passthrough` 调用
       */
      passthrough: {
        PLATFORM: getEnv().isPanguPlatform ? 'PANGU' : 'APOLLO',
      },
    }).catch(reportError)
  },
}
</script>

<style>
html,
body {
  padding: 0;
  margin: 0;
}
</style>

<style lang="scss" scoped>
$padding: 12px;

.container {
  ::v-deep.vi-page {
    position: relative;
    width: 100vw;
    height: 100vh;
    min-width: 900px + $padding * 2;
    overflow: visible;
    padding: $padding;
  }
}
</style>
