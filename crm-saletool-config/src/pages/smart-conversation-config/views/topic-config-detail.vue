<template>
  <div id="smart-conversation-config-detail" class="smart-conversation-config-detail">
    <div class="">
      <div id="ele"></div>
    </div>
  </div>
</template>
<script>
import psb from '@nibfe/platform-sdk'
import { REDIRECT_URL } from '@/lib/constants'
import graphCfg from '@nibfe/ccc-lowcode-render'
import { getUrlSearch, reportError, getEnv } from '@/lib/utils'
import { LXUtils } from '@/lib/lx'
import { getXlsxJsonData } from '../lib/utils'
import { createXHRForUpload } from '../lib/upload-picture'
import * as filterQueryLibs from '../lib/filter-items-query'
import { valLabGeneratorMap } from '../lib/lx'

export default {
  components: {},
  data() {
    return {
      pageMessage: '',
    }
  },
  created() {
    if (getEnv().isApolloPlatform) {
      psb.config('1705025435bj1yks', {
        masterOrigin: process.env.NODE_ENV === 'development' ? process.env.VUE_APP_APOLLO_HOST : '',
        redirectUrl: process.env.NODE_ENV === 'development' ? null : REDIRECT_URL,
      })
    }

    graphCfg({
      el: '#ele',
      tenantId: 3,
      pageType: 'chart', // 图表类型
      remoteURL: getEnv().isPanguPlatform
        ? process.env.VUE_APP_API_HOST_PANGU
        : process.env.VUE_APP_API_HOST,
      module: 'smart-conversation-config-v2',
      appEnv: process.env.VUE_APP_ENV,
      modelId: process.env.VUE_APP_ENV === 'production' ? 336 : 1563,
      templateId: process.env.VUE_APP_ENV === 'production' ? 232 : 2173,
      apiConfig: getEnv().isPanguPlatform
        ? {
            getTemplateByIdApi: '/gateway/pangucrm/templateApi/queryTemplateByTIdAndMIdAndMetaData',
            queryModelAndPageInteractionApi:
              '/gateway/pangucrm/modelQueryServiceApi/queryModelAndPageInteraction',
          }
        : null,
      initParams: {
        terminal: 0, //来自于PC，必填
        planSource: 0, //非来自拜访创建的，建议填0
      },
      lib: {
        createXHRForUpload,
        getUrlSearch,
        getXlsxJsonData,
        reportError,
        LXUtils: new LXUtils('gc_m', {
          cid: 'c_gc_m_wdepb9am',
          appnm: 'dp_apollo_pc',
          valLabGenerator: valLabGeneratorMap.misIdAndTenantId(this.$route.query.tenantId ?? '-1'),
        }),
        ...filterQueryLibs,
      },
      passthrough: {
        //选填，本地项目中向编辑器传递的常量，在编辑器中通过this.constants.passthrough.  调用
        PLATFORM: getEnv().isPanguPlatform ? 'PANGU' : 'APOLLO',
      },
      // mockInfo: {
      //   dslJson: schema,
      //   variable: {},
      // },
    }).then(() => {})
  },
  methods: {
    // sendMessage() {
    //   this.$bus.$emit('toInner', '我是来自外部的消息' + Math.random())
    // },
  },
}
</script>

<style lang="scss" scoped>
.smart-conversation-config-detail {
  min-width: 1100px;
}
</style>
