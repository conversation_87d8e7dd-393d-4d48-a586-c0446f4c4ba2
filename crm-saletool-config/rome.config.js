const { Loader } = require('@crm/https-certificate-generator')
const { defineConfig } = require('@rome/core')

module.exports = defineConfig({
  pluginOptions: {
    stylelint: {
      lintOnSave: process.env.NODE_ENV !== 'production',
    },
    autoRouting: true,
    // 关闭本地开发环境的按需编译
    lazyCompile: {
      status: false,
    },
  },
  configureWebpack: config => {
    // 非本地开发时取消构建样例页面
    if (process.env.NODE_ENV !== 'development') {
      delete config.entry['local-development-entry']
    }

    // 修复图标乱码问题
    config.module.rules
      .filter(rule => {
        return rule.test && rule.test.toString().indexOf('scss') !== -1
      })
      .forEach(rule => {
        rule.oneOf.forEach(oneOfRule => {
          oneOfRule.use.splice(oneOfRule.use.indexOf(require.resolve('sass-loader')), 0, {
            loader: require.resolve('css-unicode-loader'),
          })
        })
      })

    //研发框架侧为了修复样式包打进commons的临时方案
    config.optimization.splitChunks.cacheGroups.commons = {
      name: 'commons',
      test: (module, chunks) => {
        if (
          module.resource &&
          module.resource.includes('node_modules') &&
          !module.resource.includes('apollo-pc-ui-component-mtd2')
        ) {
          return true
        }
        return false
      },
      chunks: 'initial',
      minChunks: 2,
      priority: 0,
    }

    config.externals = {
      vue: 'Vue',
      'vue-demi': 'VueDemi',
      '@ss/mtd-vue2': 'MTD',
      '@vue/composition-api': 'VueCompositionAPI',
      '@vue/composition-api/dist/vue-composition-api.mjs': 'VueCompositionAPI',
    }
  },
  devServer: {
    host: 'local.sankuai.com',
    https: Loader.load({
      DNS: ['local.sankuai.com'],
    }),
    open: '/local-development-entry#/home',
  },
})
