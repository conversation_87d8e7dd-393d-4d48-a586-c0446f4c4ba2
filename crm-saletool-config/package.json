{"name": "crm-saletool-config", "version": "0.1.0", "private": true, "scripts": {"serve": "NODE_NO_WARNINGS=1 rome serve", "build": "NODE_NO_WARNINGS=1 node ./bin/talos_build --no-module", "lint": "rome romeLint --mode production", "check": "NODE_NO_WARNINGS=1 rome projectCheck", "lint:style": "rome lint:style --mode production", "rome": "NODE_NO_WARNINGS=1 rome list"}, "dependencies": {"@ai/mss-upload-js": "1.1.6-beta13", "@nibfe/apollo-pc-ui-component-mtd2": "0.1.1", "@nibfe/ccc-lowcode-render": "0.0.6", "@nibfe/dz-form-core": "0.3.12", "@nibfe/dz-form-mtd": "0.3.12", "@nibfe/dz-form-mtd-vue2": "0.3.10-beta.0", "@nibfe/dz-form-render": "0.3.12", "@nibfe/dz-form-shared": "0.3.12", "@nibfe/elink-sdk-web": "^1.0.0", "@nibfe/eslint-plugin-elink-scan": "^1.0.0", "@nibfe/platform-sdk": "0.2.4", "@ss/mtd-vue2": "1.2.7", "@vue/composition-api": "1.7.2", "axios": "1.6.4", "big.js": "6.2.1", "core-js": "^3.6.5", "css-unicode-loader": "1.0.3", "dayjs": "1.11.9", "image-preview-vue": "1.2.11", "lodash": "4.17.21", "qrcode-js": "0.0.2", "qs": "6.13.1", "querystring": "0.2.1", "querystringify": "2.2.0", "vue": "2.6.11", "xlsx": "0.18.5"}, "devDependencies": {"@crm/https-certificate-generator": "0.0.2-beta.1", "@nibfe/rome-config": "~1.1.0", "@nibfe/vue-cli-plugin-project-generator": "latest", "@rome/bundler-vue-cli-5": "~1.0.2", "@rome/preset-vue-cli-5": "~1.0.0", "@rome/rome": "~0.4.0", "@rome/vue-cli-plugin-vite": "~1.0.1", "less": "4.2.0", "less-loader": "11.1.3", "sass": "^1.19.0", "sass-loader": "^8.0.0", "vue-template-compiler": "2.6.11"}, "prettier": "@nibfe/rome-config/dist/prettier", "browserslist": ["> 1%", "last 2 versions"], "commitlint": {"extends": ["./node_modules/@nibfe/rome-config/dist/commitlint.js"]}, "gitHooks": {"post-merge": "yarn", "pre-commit": "lint-staged && yarn run check && node ./node_modules/@nibfe/eslint-plugin-elink-scan/check.js", "commit-msg": "commitlint -e $GIT_PARAMS", "pre-push": "branchlint"}, "lint-staged": {"*.{js,ts,jsx,tsx,vue,html}": ["yarn run lint", "git add"], "*.{vue,htm,html,css,sss,less,scss}": ["rome lint:style", "git add"]}, "stylelint": {"extends": "@nibfe/rome-config/dist/stylelint"}, "eslintConfig": {"extends": "./node_modules/@nibfe/rome-config/dist/eslint.js"}, "resolutions": {"@vue/composition-api": "1.7.2"}}