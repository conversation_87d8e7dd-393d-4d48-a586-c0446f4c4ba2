# test mode - NODE_ENV 必须为 production - 对应 Talos 测试环境(test01 ~ test08 + newtest) - 判断当前环境请使用 VUE_APP_ENV
# https://cli.vuejs.org/zh/guide/mode-and-env.html
NODE_ENV=production
GENERATE_SOURCEMAP=false

VUE_APP_ENV=test
VUE_APP_API_HOST=//apollo.nibcrm.test.sankuai.com
VUE_APP_API_HOST_PANGU=//crm.meishi.test.sankuai.com
VUE_APP_API_HOST_AIX=//aix.dzu.test.sankuai.com
VUE_APP_API_HOST_XIAOZHI=//xiaozhi.ai.test.sankuai.com
VUE_APP_HTTP_TIMEOUT=60000
