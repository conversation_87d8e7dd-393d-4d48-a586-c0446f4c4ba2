# crm-saletool-config

## 开发指南

首次本地运行项目直接访问 https 协议链接时，浏览器会报错，执行以下命令注册证书即可，详见相关工具说明 [@crm/https-certificate-generator](https://npm.sankuai.com/v2/pkg/detail?name=%40crm%2Fhttps-certificate-generator)：

```shell
# MacOS 本地注册证书
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain ~/.local-https-config/ca.crt

# iOS 模拟器注册证书, 其他常用指令详见大佬博客 https://suelan.github.io/2020/02/05/iOS-Simulator-from-the-Command-Line/
xcrun simctl keychain booted add-root-cert ~/.local-https-config/ca.crt
```

<!-- 如果是APP内嵌页，建议补充描述页面进入路径。有鉴权的提示鉴权方法. eg: -->

### 本地开发

<!-- 主要描述项目本地开发需要的环境、启动命令等信息 -->

### 环境依赖

- node v14

### 依赖安装

```
yarn install
```

### 项目构建

```
yarn run build
```

### 启动项目

```
yarn start
```

### 代码 Lint

```
yarn run lint
```

## 相关页面

## 标注走查工作台

### 标注详情

- 本地链接：https://local.sankuai.com:3000/mark-detail/index.html#/mark-operation?markId=${markId}&type=mark&accessControl=editable
  参数说明：
  markId: 标注/质检 id(必填)
  type: mark(标注)、inspection(质检)
  accessControl: editable(可编辑)、readOnly(只读)
- 本地嵌入阿波罗：https://apollo.nibcrm.test.sankuai.com/crm-om/?iurl=aHR0cHMlM0ElMkYlMkZsb2NhbC5zYW5rdWFpLmNvbSUzQTMwMDAlMkZtYXJrLWRldGFpbCUyRmluZGV4Lmh0bWwlMjMlMkZtYXJrLW9wZXJhdGlvbg%253D%253D
- 测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/mark-detail/index.html#/mark-operation?markId=${markId}&type=mark&accessControl=editable
- 线上环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/mark-detail/index.html#/mark-operation?markId=${markId}&type=mark&accessControl=editable

### 人员管理-标注员配置

- 本地链接：https://local.sankuai.com:3000/people-manage/index.html#/annotation
- 本地嵌入阿波罗：https://apollo.nibcrm.test.sankuai.com/crm-om/?iurl=aHR0cHMlM0ElMkYlMkZsb2NhbC5zYW5rdWFpLmNvbSUzQTMwMDAlMkZwZW9wbGUtbWFuYWdlJTJGaW5kZXguaHRtbCUyMyUyRmFubm90YXRpb24%253D
- 测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/people-manage/index.html#/annotation
- 线上环境链接：https://apollo.meituan.com/crm-saletool-config/people-manage/index.html#/annotation

### 人员管理-质检员配置

- 本地链接：https://local.sankuai.com:3000/people-manage/index.html#/inspection
- 本地嵌入阿波罗：https://apollo.nibcrm.test.sankuai.com/crm-om/?iurl=aHR0cHMlM0ElMkYlMkZsb2NhbC5zYW5rdWFpLmNvbSUzQTMwMDAlMkZwZW9wbGUtbWFuYWdlJTJGaW5kZXguaHRtbCUyMyUyRmluc3BlY3Rpb24%253D
- 测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/people-manage/index.html#/inspection
- 线上环境链接：https://apollo.meituan.com/crm-saletool-config/people-manage/index.html#/inspection

### 标注工作台

- 本地链接：https://local.sankuai.com:3000/annotation-workbench/index.html#/
- 本地嵌入阿波罗：https://apollo.nibcrm.test.sankuai.com/crm-om/?iurl=aHR0cHMlM0ElMkYlMkZsb2NhbC5zYW5rdWFpLmNvbSUzQTMwMDAlMkZhbm5vdGF0aW9uLXdvcmtiZW5jaCUyRmluZGV4Lmh0bWwlMjMlMkY%253D
- 测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/annotation-workbench/index.html#/
- 线上环境链接：https://apollo.meituan.com/crm-saletool-config/annotation-workbench/index.html#/

### 质检工作台

- 本地链接：https://local.sankuai.com:3000/inspection-workbench/index.html#/
- 本地嵌入阿波罗：https://apollo.nibcrm.test.sankuai.com/crm-om/?iurl=aHR0cHMlM0ElMkYlMkZsb2NhbC5zYW5rdWFpLmNvbSUzQTMwMDAlMkZpbnNwZWN0aW9uLXdvcmtiZW5jaCUyRmluZGV4Lmh0bWwlMjMlMkY%253D
- 测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/inspection-workbench/index.html#/
- 线上环境链接：https://apollo.meituan.com/crm-saletool-config/inspection-workbench/index.html#/

### 质检记录

- 本地链接：https://local.sankuai.com:3000/inspection-record/index.html#/
- 本地嵌入阿波罗：https://apollo.nibcrm.test.sankuai.com/crm-om/?iurl=aHR0cHMlM0ElMkYlMkZsb2NhbC5zYW5rdWFpLmNvbSUzQTMwMDAlMkZpbnNwZWN0aW9uLXJlY29yZCUyRmluZGV4Lmh0bWwlMjMlMkY%253D
- 测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/inspection-record/index.html#/
- 线上环境链接：https://apollo.meituan.com/crm-saletool-config/inspection-record/index.html#/

### 手动下发记录页

- 本地链接：https://local.sankuai.com:3000/manual-upload-poi/index.html#/record-list
- 测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/manual-upload-poi/index.html#/record-list
- 线上环境链接：https://apollo.meituan.com/crm-saletool-config/manual-upload-poi/index.html#/record-list

### 标注/质检操作日志页
- 本地链接：https://local.sankuai.com:3000/operate-log/index.html#/log-list?annInsId=${annInsId}&type=mark
  参数说明：
  annInsId: 标注/质检记录 id(必填)
  type: 1(标注)2(质检)
- 测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/operate-log/index.html#/log-list?annInsId=${annInsId}&type=1
- 线上环境链接：https://apollo.meituan.com/crm-saletool-config/operate-log/index.html#/log-list?annInsId=${annInsId}&type=1

## BML 自动补贴

- 测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-platform?iurl=JTJGJTJGYXBvbGxvLm5pYmNybS50ZXN0LnNhbmt1YWkuY29tJTJGY3JtLXNhbGV0b29sLWNvbmZpZyUyRnByaWNlLW1hdGNoLWF1dG9tYXRpb24lMkZpbmRleC5odG1sJTIzJTJGbGlzdA%253D%253D&pageId=apollo.shops
- 线上环境链接：-

## 智能演练配置平台

### 列表页

- 阿波罗本地链接：https://local.sankuai.com:3000/smart-conversation-config/index.html#/config-list
- 阿波罗测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/smart-conversation-config/index.html#/config-list
- 盘古本地链接：https://local.crm.meishi.test.sankuai.com:3000/smart-conversation-config/index.html#/config-list
- 盘古测试环境链接：https://crm.meishi.test.sankuai.com/crm-saletool-config/smart-conversation-config/index.html#/config-list

### 配置表单页

- 阿波罗本地链接：https://local.sankuai.com:3000/smart-conversation-config/index.html#/detail
- 阿波罗测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/smart-conversation-config/index.html#/detail
- 盘古本地链接：https://local.crm.meishi.test.sankuai.com:3000/smart-conversation-config/index.html#/detail
- 盘古测试环境链接：https://crm.meishi.test.sankuai.com/crm-saletool-config/smart-conversation-config/index.html#/detail

## BML销售作业路径

### 报错记录页

- 本地链接：https://local.sankuai.com:3000/bml-sale-pc/index.html#/error-record-list
- 本地内嵌阿波罗：
- 测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/bml-sale-pc/index.html#/error-record-list
- 线上环境链接：https://apollo.meituan.com/crm-saletool-config/bml-sale-pc/index.html#/error-record-list

### 匹配关系对变更记录页
参数说明：
  djShopId: dj门店id
  dpShopId: mt门店id
  djProductId: dj商品id
- 本地链接：https://local.sankuai.com:3000/bml-sale-pc/index.html#/match-relationship-change-records
- 本地内嵌阿波罗：
- 测试环境链接：https://apollo.nibcrm.test.sankuai.com/crm-saletool-config/bml-sale-pc/index.html#/match-relationship-change-records
- 线上环境链接：https://apollo.meituan.com/crm-saletool-config/bml-sale-pc/index.html#/match-relationship-change-records

## 其他项目信息

<!-- 可以记录一些重要的项目信息，如：常见问题、目录结构、文档等，eg: -->

- 后端 APP KEY:
- owl key: com.sankuai.gcfe.crm.saletoolconfig
- Talos 发布地址: https://talos.sankuai.com/#/project/17578?page_num=1
- 灵犀埋点信息:
